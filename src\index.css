@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer base {
  :root {
    /* Base colors - Professional blue theme */
    --background: 210 50% 98%; /* Light blue-tinted background */
    --foreground: 220 60% 15%; /* Dark blue text */

    /* Card colors */
    --card: 0 0% 100%; /* Pure white */
    --card-foreground: 220 60% 15%;

    /* UI colors */
    --popover: 0 0% 100%;
    --popover-foreground: 220 60% 15%;

    /* Primary colors - Rich blue */
    --primary: 220 85% 45%; /* Rich blue */
    --primary-foreground: 0 0% 100%; /* White text */

    /* Secondary colors - Lighter blue */
    --secondary: 215 70% 75%; /* Lighter blue */
    --secondary-foreground: 220 60% 15%;

    /* Muted colors */
    --muted: 210 40% 96%; /* Very light blue-gray */
    --muted-foreground: 220 20% 40%; /* Medium blue-gray */

    /* Accent colors */
    --accent: 215 100% 96%; /* Very light blue */
    --accent-foreground: 220 85% 45%;

    /* Functional colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --success: 160 84% 39%; /* Teal-blue green */
    --success-foreground: 0 0% 100%;
    --warning: 45 93% 55%;
    --warning-foreground: 0 0% 100%;
    --error: 0 84% 60%;
    --error-foreground: 0 0% 100%;
    --info: 215 85% 60%; /* Info blue */
    --info-foreground: 0 0% 100%;

    /* Border/input colors */
    --border: 210 30% 90%; /* Light blue-gray border */
    --input: 210 30% 90%;
    --ring: 220 85% 45%; /* Rich blue */

    /* Chart colors - Blue variations */
    --chart-1: 220 85% 45%; /* Primary blue */
    --chart-2: 195 85% 55%; /* Cyan-blue */
    --chart-3: 240 70% 60%; /* Indigo-blue */
    --chart-4: 210 90% 65%; /* Sky blue */
    --chart-5: 230 80% 70%; /* Lavender blue */
    --chart-6: 200 75% 55%; /* Ocean blue */
    --chart-7: 180 70% 45%; /* Teal blue */
    --chart-8: 225 95% 65%; /* Royal blue */

    --radius: 0.5rem;
  }

  .dark {
    /* Dark mode colors - Dark blue palette */
    --background: 220 40% 13%; /* Dark blue background */
    --foreground: 210 20% 98%; /* Near white with blue tint */

    --card: 220 35% 18%; /* Dark blue card */
    --card-foreground: 210 20% 98%;

    --popover: 220 35% 18%;
    --popover-foreground: 210 20% 98%;

    --primary: 215 85% 60%; /* Bright blue for dark mode */
    --primary-foreground: 220 40% 13%;

    --secondary: 215 70% 40%; /* Medium blue */
    --secondary-foreground: 210 20% 98%;

    --muted: 220 35% 25%; /* Dark blue muted */
    --muted-foreground: 210 25% 75%;

    --accent: 215 65% 30%; /* Dark blue accent */
    --accent-foreground: 210 20% 98%;

    --border: 220 30% 25%; /* Dark blue border */
    --input: 220 30% 25%;
    --ring: 215 85% 60%;

    /* Chart colors - Brighter blues for dark mode */
    --chart-1: 215 90% 60%; /* Bright blue */
    --chart-2: 195 85% 65%; /* Bright cyan-blue */
    --chart-3: 240 80% 70%; /* Bright indigo */
    --chart-4: 210 90% 75%; /* Bright sky blue */
    --chart-5: 230 85% 80%; /* Bright lavender blue */
    --chart-6: 200 80% 65%; /* Bright ocean blue */
    --chart-7: 180 75% 55%; /* Bright teal blue */
    --chart-8: 225 95% 75%; /* Bright royal blue */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* UI Enhancements */
.app-shadow {
  box-shadow: 0 4px 20px -5px rgba(30, 64, 175, 0.15);
}

/* Blue solid backgrounds */
.gradient-bg {
  background-image: linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary)/0.85));
}

.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px -5px rgba(30, 64, 175, 0.2);
}

/* Status badges */
.status-active {
  background-color: hsl(var(--success) / 0.15);
  color: hsl(var(--success));
  border-color: hsl(var(--success) / 0.3);
}

.status-inactive {
  background-color: hsl(var(--error) / 0.15);
  color: hsl(var(--error));
  border-color: hsl(var(--error) / 0.3);
}

.status-pending {
  background-color: hsl(var(--warning) / 0.15);
  color: hsl(var(--warning));
  border-color: hsl(var(--warning) / 0.3);
}

/* Blue-themed utilities */
.blue-card {
  border: 1px solid hsl(var(--primary)/0.1);
  background-color: hsl(var(--card));
  box-shadow: 0 4px 12px -2px rgba(30, 64, 175, 0.08);
}

.blue-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--chart-2)));
}

.blue-header {
  background: linear-gradient(to right, hsl(var(--primary)/0.15), hsl(var(--primary)/0.05));
  border-bottom: 1px solid hsl(var(--primary)/0.1);
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}