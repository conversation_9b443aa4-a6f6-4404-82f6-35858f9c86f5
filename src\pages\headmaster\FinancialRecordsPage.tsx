import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { showToast } from '@/lib/utils';
import { 
  Search,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Plus,
  Edit,
  MoreHorizontal,
  Loader2
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import { AddFinancialRecordModal } from '@/components/modals/AddFinancialRecordModal';
import { EditFinancialRecordModal } from '@/components/modals/EditFinancialRecordModal';

interface FinancialRecord {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  category: string;
  date: string;
  reference: string;
}

export function FinancialRecordsPage() {
  const [loading, setLoading] = useState(true);
  const [records, setRecords] = useState<FinancialRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<FinancialRecord[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<FinancialRecord | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });

  useEffect(() => {
    fetchRecords();
  }, []);

  useEffect(() => {
    filterRecords();
  }, [records, searchQuery, selectedType, selectedCategory, pagination.page]);

  const fetchRecords = async () => {
    try {
      setLoading(true);
      // Mock data
      const mockRecords: FinancialRecord[] = [
        {
          id: '1',
          type: 'income',
          amount: 50000.00,
          description: 'Government Grant for Infrastructure',
          category: 'Grant',
          date: '2024-01-15',
          reference: 'GRN001'
        },
        {
          id: '2',
          type: 'income',
          amount: 25000.00,
          description: 'Donation from Alumni Association',
          category: 'Donation',
          date: '2024-01-20',
          reference: 'DON001'
        },
        {
          id: '3',
          type: 'expense',
          amount: 15000.00,
          description: 'Classroom Renovation',
          category: 'Infrastructure',
          date: '2024-01-25',
          reference: 'EXP001'
        },
        {
          id: '4',
          type: 'expense',
          amount: 8000.00,
          description: 'Teaching Materials Purchase',
          category: 'Educational',
          date: '2024-02-01',
          reference: 'EXP002'
        },
        {
          id: '5',
          type: 'income',
          amount: 12000.00,
          description: 'Fundraising Event Revenue',
          category: 'Event',
          date: '2024-02-05',
          reference: 'INC001'
        }
      ];
      
      setRecords(mockRecords);
    } catch (error) {
      console.error('Error fetching records:', error);
      showToast.error('Error', 'Failed to load financial records');
    } finally {
      setLoading(false);
    }
  };

  const filterRecords = () => {
    let filtered = records;

    if (searchQuery) {
      filtered = filtered.filter(record =>
        record.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.reference.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (selectedType !== 'all') {
      filtered = filtered.filter(record => record.type === selectedType);
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(record => record.category === selectedCategory);
    }

    setPagination(prev => ({ ...prev, total: filtered.length }));
    
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    const paginatedRecords = filtered.slice(startIndex, endIndex);
    
    setFilteredRecords(paginatedRecords);
  };

  const handleAddRecord = (data: any) => {
    const newRecord: FinancialRecord = {
      id: (records.length + 1).toString(),
      ...data,
      amount: parseFloat(data.amount)
    };
    setRecords([...records, newRecord]);
    setIsAddModalOpen(false);
    showToast.success('Success', 'Financial record added successfully');
  };

  const handleEditRecord = (record: FinancialRecord) => {
    setSelectedRecord(record);
    setIsEditModalOpen(true);
  };

  const handleUpdateRecord = (data: any) => {
    if (!selectedRecord) return;
    const updatedRecords = records.map(record => 
      record.id === selectedRecord.id ? { ...record, ...data, amount: parseFloat(data.amount) } : record
    );
    setRecords(updatedRecords);
    setIsEditModalOpen(false);
    showToast.success('Success', 'Financial record updated successfully');
  };

  // Calculate summary statistics
  const allFilteredRecords = records.filter(record => {
    let matches = true;
    if (searchQuery) {
      matches = matches && (record.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           record.reference.toLowerCase().includes(searchQuery.toLowerCase()));
    }
    if (selectedType !== 'all') {
      matches = matches && record.type === selectedType;
    }
    if (selectedCategory !== 'all') {
      matches = matches && record.category === selectedCategory;
    }
    return matches;
  });
  
  const totalIncome = records.filter(r => r.type === 'income').reduce((sum, r) => sum + r.amount, 0);
  const totalExpenses = records.filter(r => r.type === 'expense').reduce((sum, r) => sum + r.amount, 0);
  const netBalance = totalIncome - totalExpenses;
  const totalRecords = allFilteredRecords.length;

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-900 via-indigo-800 to-purple-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Financial Records</h1>
                <p className="text-blue-200 mt-1">Track school income and expenses</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-white text-blue-900 hover:bg-blue-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>Add Record</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="bg-gradient-to-br from-green-50 to-green-100/50 border-green-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-700">Total Income</p>
              <p className="text-3xl font-bold text-green-900">GHS {totalIncome.toLocaleString()}</p>
            </div>
            <div className="bg-green-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-red-50 to-red-100/50 border-red-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-700">Total Expenses</p>
              <p className="text-3xl font-bold text-red-900">GHS {totalExpenses.toLocaleString()}</p>
            </div>
            <div className="bg-red-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <TrendingDown className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className={`bg-gradient-to-br ${netBalance >= 0 ? 'from-blue-50 to-blue-100/50 border-blue-200/50' : 'from-orange-50 to-orange-100/50 border-orange-200/50'} hover:shadow-lg transition-all duration-300 group rounded-xl p-6`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${netBalance >= 0 ? 'text-blue-700' : 'text-orange-700'}`}>Net Balance</p>
              <p className={`text-3xl font-bold ${netBalance >= 0 ? 'text-blue-900' : 'text-orange-900'}`}>GHS {netBalance.toLocaleString()}</p>
            </div>
            <div className={`${netBalance >= 0 ? 'bg-blue-600' : 'bg-orange-600'} p-3 rounded-xl group-hover:scale-110 transition-transform duration-300`}>
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700">Total Records</p>
              <p className="text-3xl font-bold text-purple-900">{totalRecords}</p>
            </div>
            <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <DollarSign className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">Financial Transactions</CardTitle>
                <p className="text-sm text-slate-600">Track all income and expense records</p>
              </div>
            </div>
          </div>
        </CardHeader>
        
        {/* Filters */}
        <div className="p-6 border-b border-slate-100/50 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search by description or reference..."
              className="pl-10 border-slate-200 focus:border-blue-500 focus:ring-blue-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="income">Income</SelectItem>
              <SelectItem value="expense">Expense</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="Grant">Grant</SelectItem>
              <SelectItem value="Donation">Donation</SelectItem>
              <SelectItem value="Event">Event</SelectItem>
              <SelectItem value="Infrastructure">Infrastructure</SelectItem>
              <SelectItem value="Educational">Educational</SelectItem>
              <SelectItem value="Administrative">Administrative</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Records Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead>Type</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Amount (GHS)</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Reference</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading records...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredRecords.length > 0 ? (
                filteredRecords.map((record) => (
                  <TableRow key={record.id} className="hover:bg-slate-50/50 transition-colors">
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`${
                          record.type === 'income' 
                            ? 'bg-green-100 text-green-800 border-green-200'
                            : 'bg-red-100 text-red-800 border-red-200'
                        }`}
                      >
                        {record.type === 'income' ? 'Income' : 'Expense'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium text-slate-900">{record.description}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                        {record.category}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className={`font-bold ${record.type === 'income' ? 'text-green-700' : 'text-red-700'}`}>
                        GHS {record.amount.toLocaleString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-slate-600">{format(new Date(record.date), 'MMM d, yyyy')}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-slate-700 font-mono">{record.reference}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem 
                            className="flex items-center gap-2 cursor-pointer"
                            onClick={() => handleEditRecord(record)}
                          >
                            <Edit className="h-4 w-4" />
                            <span>Edit Record</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No financial records found matching your criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Pagination */}
        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{Math.min((pagination.page - 1) * pagination.limit + 1, pagination.total)}</span> to{" "}
            <span className="font-medium">{Math.min(pagination.page * pagination.limit, pagination.total)}</span> of{" "}
            <span className="font-medium">{pagination.total}</span> records
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page * pagination.limit >= pagination.total}
            >
              Next
            </Button>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <AddFinancialRecordModal 
        open={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddRecord}
      />
      
      {selectedRecord && (
        <EditFinancialRecordModal
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={handleUpdateRecord}
          record={selectedRecord}
        />
      )}
    </Layout>
  );
}