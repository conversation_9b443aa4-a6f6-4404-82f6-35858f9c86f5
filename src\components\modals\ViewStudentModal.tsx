import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Phone, Calendar, GraduationCap, User, Home, BookOpen, Camera } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';

interface Student {
  id: string;
  name: string;
  class: string;
  email: string;
  parent: string;
  joinDate: string;
  status: string;
}

interface ViewStudentModalProps {
  open: boolean;
  onClose: () => void;
  student: any;
}

export function ViewStudentModal({ open, onClose, student }: ViewStudentModalProps) {
  const [profileImage, setProfileImage] = useState<string | null>(null);
  
  if (!student) return null;

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Mock data for student details
  const studentDetails = {
    parentPhone: '+****************'
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Student Profile</DialogTitle>
          <DialogDescription>
            Detailed information about {student.name}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex flex-col md:flex-row gap-6 py-4">
          <div className="flex flex-col items-center space-y-3">
            <div className="relative">
              <Avatar className="h-24 w-24">
                {profileImage ? (
                  <AvatarImage src={profileImage} alt={student.name} />
                ) : (
                  <AvatarFallback className="text-2xl bg-primary/20 text-primary">
                    {student.name.charAt(0)}
                  </AvatarFallback>
                )}
              </Avatar>
              <label htmlFor="image-upload" className="absolute bottom-0 right-0 bg-primary text-primary-foreground rounded-full p-1 cursor-pointer hover:bg-primary/80">
                <Camera className="h-3 w-3" />
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageUpload}
                />
              </label>
            </div>
            <Badge 
              variant={student.status === 'active' ? 'success' : 'secondary'}
              className={
                student.status === 'active' 
                  ? 'status-active' 
                  : 'status-inactive'
              }
            >
              {student.status === 'active' ? 'Active' : 'Inactive'}
            </Badge>
            <Badge variant="outline" className="bg-primary/10 text-primary hover:bg-primary/20">
              {student.class}
            </Badge>
          </div>
          
          <div className="flex-1 space-y-4">
            <div>
              <h3 className="text-xl font-bold">{student.name}</h3>
              <div className="flex items-center mt-1">
                <GraduationCap className="h-4 w-4 mr-2 text-primary" />
                <span className="text-muted-foreground">Student</span>
              </div>
            </div>
            
            <Separator />
            
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>Joined {format(new Date(student.joinDate), 'MMMM d, yyyy')}</span>
              </div>
            </div>
            
            <Separator />
            
            <div>
              <h4 className="text-sm font-medium mb-2">Parent Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{student.parent}</span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{studentDetails.parentPhone}</span>
                </div>
              </div>
            </div>
            

            
            <div>
              <h4 className="text-sm font-medium mb-2">Subjects</h4>
              <div className="flex flex-wrap gap-2">
                {student.subjects && student.subjects.length > 0 ? (
                  student.subjects.map((subject: string, index: number) => (
                    <Badge key={index} variant="outline" className="bg-primary/10 text-primary hover:bg-primary/20">
                      <BookOpen className="h-3 w-3 mr-1" />
                      {subject}
                    </Badge>
                  ))
                ) : (
                  <span className="text-sm text-muted-foreground">No subjects assigned</span>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button className="gradient-bg text-white hover:opacity-90">
            Edit Profile
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}