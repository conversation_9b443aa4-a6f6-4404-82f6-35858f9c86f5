import React from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Users, GraduationCap, Calendar, User } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface ViewClassModalProps {
  open: boolean;
  onClose: () => void;
  classData: any;
}

export function ViewClassModal({ open, onClose, classData }: ViewClassModalProps) {
  if (!classData) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Class Details</DialogTitle>
          <DialogDescription>
            Detailed information about {classData.name}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold">{classData.name}</h3>
              <p className="text-muted-foreground">Grade {classData.grade} - Section {classData.section}</p>
            </div>
            <Badge 
              variant={classData.active ? 'success' : 'secondary'}
              className={classData.active ? 'status-active' : 'status-inactive'}
            >
              {classData.active ? 'Active' : 'Inactive'}
            </Badge>
          </div>
          
          <Separator />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Class Teacher</p>
                  <p className="text-sm text-muted-foreground">{classData.teacher || 'Not assigned'}</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Students</p>
                  <p className="text-sm text-muted-foreground">{classData.studentCount} / {classData.capacity}</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <GraduationCap className="h-4 w-4 mr-2 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Academic Year</p>
                  <p className="text-sm text-muted-foreground">{classData.academicYear}</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-sm text-muted-foreground">
                    {classData.createdAt ? format(new Date(classData.createdAt), 'MMMM d, yyyy') : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div>
            <h4 className="text-sm font-medium mb-2">Capacity Overview</h4>
            <div className="w-full bg-muted/30 h-2 rounded-full">
              <div
                className="bg-primary h-full rounded-full"
                style={{ width: `${(classData.studentCount / classData.capacity) * 100}%` }}
              ></div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {Math.round((classData.studentCount / classData.capacity) * 100)}% capacity
            </p>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}