import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { 
  Search,
  Users,
  TrendingDown,
  AlertTriangle,
  Bell,
  Send,
  Loader2,
  Plus,
  Download
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { format } from 'date-fns';
import { NotifyParentsModal } from '@/components/modals/NotifyParentsModal';
import { AddArrearsModal } from '@/components/modals/AddArrearsModal';

interface ArrearsRecord {
  id: string;
  studentName: string;
  amountPaid: number;
  arrears: number;
  totalAmount: number;
  feeType: string;
  dueDate: string;
  studentClass: string;
}

export function ArrearsListPage() {
  const [loading, setLoading] = useState(true);
  const [arrearsRecords, setArrearsRecords] = useState<ArrearsRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<ArrearsRecord[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTerm, setSelectedTerm] = useState('all');
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedYear, setSelectedYear] = useState('2024/2025');
  const [selectedFeeType, setSelectedFeeType] = useState('all');
  const [isNotifyModalOpen, setIsNotifyModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });

  useEffect(() => {
    fetchClasses();
    fetchArrearsRecords();
  }, [selectedTerm, selectedClass, selectedYear, selectedFeeType]);

  useEffect(() => {
    filterRecords();
  }, [arrearsRecords, searchQuery, pagination.page]);

  const fetchClasses = async () => {
    try {
      const response = await headmasterAPI.getClasses();
      if (response.success) {
        setClasses(response.data.classes);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchArrearsRecords = async () => {
    try {
      setLoading(true);
      // Mock data for now
      const mockRecords: ArrearsRecord[] = [
        {
          id: '1',
          studentName: 'John Smith',
          amountPaid: 800.00,
          arrears: 700.00,
          totalAmount: 1500.00,
          feeType: 'Tuition Fee',
          dueDate: '2024-02-15',
          studentClass: 'Class 10A'
        },
        {
          id: '2',
          studentName: 'Sarah Johnson',
          amountPaid: 500.00,
          arrears: 700.00,
          totalAmount: 1200.00,
          feeType: 'School Fees',
          dueDate: '2024-02-20',
          studentClass: 'Class 9B'
        },
        {
          id: '3',
          studentName: 'Michael Brown',
          amountPaid: 200.00,
          arrears: 600.00,
          totalAmount: 800.00,
          feeType: 'Examination Fee',
          dueDate: '2024-02-10',
          studentClass: 'Class 11C'
        },
        {
          id: '4',
          studentName: 'Emma Davis',
          amountPaid: 1000.00,
          arrears: 1000.00,
          totalAmount: 2000.00,
          feeType: 'Tuition Fee',
          dueDate: '2024-02-25',
          studentClass: 'Class 12A'
        },
        {
          id: '5',
          studentName: 'Daniel Wilson',
          amountPaid: 450.00,
          arrears: 500.00,
          totalAmount: 950.00,
          feeType: 'Activity Fee',
          dueDate: '2024-02-18',
          studentClass: 'Class 10B'
        }
      ];
      
      setArrearsRecords(mockRecords);
    } catch (error) {
      console.error('Error fetching arrears records:', error);
      showToast.error('Error', 'Failed to load arrears records');
    } finally {
      setLoading(false);
    }
  };

  const filterRecords = () => {
    let filtered = arrearsRecords;

    if (searchQuery) {
      filtered = filtered.filter(record =>
        record.studentName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setPagination(prev => ({ ...prev, total: filtered.length }));
    
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    const paginatedRecords = filtered.slice(startIndex, endIndex);
    
    setFilteredRecords(paginatedRecords);
  };

  const handleNotifyParents = (data: any) => {
    showToast.success('Success', `Notifications sent to parents of ${data.selectedClass} students for ${data.feeType}`);
    setIsNotifyModalOpen(false);
  };

  const handleAddArrears = (data: any) => {
    const newRecord: ArrearsRecord = {
      id: (arrearsRecords.length + 1).toString(),
      ...data
    };
    setArrearsRecords([...arrearsRecords, newRecord]);
    setIsAddModalOpen(false);
    showToast.success('Success', 'Student added to arrears list successfully');
  };

  const handleDownloadPDF = () => {
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; padding: 20px;">
        <h1 style="text-align: center; color: #333; border-bottom: 2px solid #666; padding-bottom: 10px;">Student Arrears Report</h1>
        
        <div style="margin: 20px 0; display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <p><strong>Term:</strong> ${selectedTerm.charAt(0).toUpperCase() + selectedTerm.slice(1)} Term</p>
            <p><strong>Class:</strong> ${selectedClass === 'all' ? 'All Classes' : classes.find(c => c.id === selectedClass)?.name || 'Unknown'}</p>
          </div>
          <div>
            <p><strong>Fee Type:</strong> ${selectedFeeType === 'all' ? 'All Fee Types' : selectedFeeType}</p>
            <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
          </div>
        </div>
        
        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Student Name</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Class</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Amount Paid (GHS)</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Arrears (GHS)</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Total Amount (GHS)</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Fee Type</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Due Date</th>
            </tr>
          </thead>
          <tbody>
            ${allFilteredRecords.map((record, index) => {
              const isOverdue = new Date(record.dueDate) < new Date();
              return `
                <tr style="${index % 2 === 0 ? 'background-color: #f9f9f9;' : ''}">
                  <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">${record.studentName}</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${record.studentClass}</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${record.amountPaid.toLocaleString()}</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center; color: #dc2626; font-weight: bold;">${record.arrears.toLocaleString()}</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${record.totalAmount.toLocaleString()}</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${record.feeType}</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center; ${isOverdue ? 'color: #dc2626; font-weight: bold;' : ''}">${new Date(record.dueDate).toLocaleDateString()}${isOverdue ? ' (OVERDUE)' : ''}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
        
        <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
          <h3 style="margin: 0 0 10px 0; color: #333;">Summary</h3>
          <p><strong>Total Students with Arrears:</strong> ${totalStudents}</p>
          <p><strong>Total Outstanding Amount:</strong> GHS ${totalArrears.toLocaleString()}</p>
          <p><strong>Students Overdue:</strong> ${overdueCount}</p>
        </div>
      </div>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Arrears Report</title>
            <style>
              @page { margin: 20px; }
              body { margin: 0; font-size: 12px; }
            </style>
          </head>
          <body>
            ${htmlContent}
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 100);
              }
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
    
    showToast.success('PDF Ready', 'Use the print dialog to save as PDF');
  };

  // Calculate summary statistics
  const allFilteredRecords = arrearsRecords.filter(record => {
    if (!searchQuery) return true;
    return record.studentName.toLowerCase().includes(searchQuery.toLowerCase());
  });
  
  const totalStudents = allFilteredRecords.length;
  const totalArrears = allFilteredRecords.reduce((sum, record) => sum + record.arrears, 0);
  const averageArrears = totalStudents > 0 ? totalArrears / totalStudents : 0;
  const overdueCount = allFilteredRecords.filter(r => new Date(r.dueDate) < new Date()).length;

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-red-900 via-orange-800 to-yellow-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <AlertTriangle className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Arrears List</h1>
                <p className="text-red-200 mt-1">Track and manage outstanding fee payments</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={handleDownloadPDF}
              variant="outline"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Download className="h-4 w-4 mr-2" />
              <span>Download PDF</span>
            </Button>
            <Button
              onClick={() => setIsAddModalOpen(true)}
              variant="outline"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>Add Student</span>
            </Button>
            <Button
              onClick={() => setIsNotifyModalOpen(true)}
              className="bg-white text-red-900 hover:bg-red-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Bell className="h-4 w-4 mr-2" />
              <span>Notify Parents</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Students with Arrears</p>
              <p className="text-3xl font-bold text-blue-900">{totalStudents}</p>
            </div>
            <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-red-50 to-red-100/50 border-red-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-700">Total Arrears</p>
              <p className="text-3xl font-bold text-red-900">GHS {totalArrears.toLocaleString()}</p>
            </div>
            <div className="bg-red-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <TrendingDown className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-700">Average Arrears</p>
              <p className="text-3xl font-bold text-orange-900">GHS {averageArrears.toFixed(0)}</p>
            </div>
            <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <TrendingDown className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100/50 border-yellow-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-700">Overdue</p>
              <p className="text-3xl font-bold text-yellow-900">{overdueCount}</p>
            </div>
            <div className="bg-yellow-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <AlertTriangle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">Outstanding Fees</CardTitle>
                <p className="text-sm text-slate-600">Students with pending fee payments</p>
              </div>
            </div>
          </div>
        </CardHeader>
        
        {/* Filters */}
        <div className="p-6 border-b border-slate-100/50 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search by student name..."
              className="pl-10 border-slate-200 focus:border-red-500 focus:ring-red-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Select value={selectedTerm} onValueChange={setSelectedTerm}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Select Term" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Terms</SelectItem>
              <SelectItem value="first">First Term</SelectItem>
              <SelectItem value="second">Second Term</SelectItem>
              <SelectItem value="third">Third Term</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Select Class" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Classes</SelectItem>
              {classes.map((cls) => (
                <SelectItem key={cls.id} value={cls.id}>
                  {cls.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={selectedYear} onValueChange={setSelectedYear}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Academic Year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2024/2025">2024/2025</SelectItem>
              <SelectItem value="2023/2024">2023/2024</SelectItem>
              <SelectItem value="2022/2023">2022/2023</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedFeeType} onValueChange={setSelectedFeeType}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Fee Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Fee Types</SelectItem>
              <SelectItem value="Tuition Fee">Tuition Fee</SelectItem>
              <SelectItem value="School Fees">School Fees</SelectItem>
              <SelectItem value="Examination Fee">Examination Fee</SelectItem>
              <SelectItem value="Activity Fee">Activity Fee</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Arrears Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead>Student Name</TableHead>
                <TableHead>Amount Paid (GHS)</TableHead>
                <TableHead>Arrears (GHS)</TableHead>
                <TableHead>Total Amount (GHS)</TableHead>
                <TableHead>Fee Type</TableHead>
                <TableHead>Due Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading arrears records...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredRecords.length > 0 ? (
                filteredRecords.map((record) => {
                  const isOverdue = new Date(record.dueDate) < new Date();
                  return (
                    <TableRow key={record.id} className="hover:bg-slate-50/50 transition-colors">
                      <TableCell>
                        <div className="font-semibold text-slate-900">{record.studentName}</div>
                        <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200 mt-1">
                          {record.studentClass}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium text-green-700">GHS {record.amountPaid.toLocaleString()}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-bold text-red-700">GHS {record.arrears.toLocaleString()}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium text-slate-900">GHS {record.totalAmount.toLocaleString()}</div>
                      </TableCell>
                      <TableCell>
                        <span className="text-slate-700">{record.feeType}</span>
                      </TableCell>
                      <TableCell>
                        <div className={`${isOverdue ? 'text-red-600 font-medium' : 'text-slate-600'}`}>
                          {format(new Date(record.dueDate), 'MMM d, yyyy')}
                          {isOverdue && (
                            <Badge variant="destructive" className="ml-2 text-xs">
                              Overdue
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    No arrears records found matching your criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Pagination */}
        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{Math.min((pagination.page - 1) * pagination.limit + 1, pagination.total)}</span> to{" "}
            <span className="font-medium">{Math.min(pagination.page * pagination.limit, pagination.total)}</span> of{" "}
            <span className="font-medium">{pagination.total}</span> records
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page * pagination.limit >= pagination.total}
            >
              Next
            </Button>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <AddArrearsModal 
        open={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddArrears}
      />
      
      <NotifyParentsModal 
        open={isNotifyModalOpen}
        onClose={() => setIsNotifyModalOpen(false)}
        onSubmit={handleNotifyParents}
        classes={classes}
      />
    </Layout>
  );
}