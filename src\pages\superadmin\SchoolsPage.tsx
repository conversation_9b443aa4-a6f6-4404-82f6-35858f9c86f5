import React, { useState, useEffect, useCallback } from 'react';
import { Layout } from '@/components/common/Layout';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  MoreHorizontal, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye,
  Download,
  Building2,
  Filter,
  ArrowUpDown,
  Loader2,
  X,
  Users,
  GraduationCap,
  TrendingUp,
  MapPin,
  Calendar,
  School as SchoolIcon,
  Globe
} from 'lucide-react';
import { format } from 'date-fns';
import { AddSchoolModal } from '@/components/modals/AddSchoolModal';
import { Badge } from '@/components/ui/badge';
import { MOCK_SCHOOLS } from '@/lib/constants';
import type { AddSchoolFormData, School } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { superAdminAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';

export function SchoolsPage() {
  const [isAddSchoolModalOpen, setIsAddSchoolModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [schools, setSchools] = useState<School[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Debounce function
  function debounce(func: Function, wait: number) {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Debounced search
  const debouncedFetchSchools = useCallback(
    debounce(() => {
      fetchSchools();
    }, 300),
    [statusFilter, sortBy, sortOrder]
  );

  useEffect(() => {
    if (searchQuery) {
      debouncedFetchSchools();
    } else {
      fetchSchools();
    }
  }, [searchQuery]);

  useEffect(() => {
    fetchSchools();
  }, [statusFilter, sortBy, sortOrder]);

  // Initial load
  useEffect(() => {
    fetchSchools();
  }, []);

  const fetchSchools = async () => {
    try {
      setLoading(true);
      const params = {
        search: searchQuery,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        sortBy,
        sortOrder
      };
      const response = await superAdminAPI.getSchools(params);
      
      if (response.success) {
        setSchools(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching schools:', error);
      showToast.error('Error', 'Failed to load schools');
    } finally {
      setLoading(false);
    }
  };

  const handleAddSchool = async (data: AddSchoolFormData) => {
    try {
      const schoolData = {
        name: data.name,
        address: data.address,
        contactInfo: data.contactInfo,
        headmasterName: data.headmasterName,
        headmasterEmail: data.email,
        headmasterPassword: data.password,
        city: 'City', // Default values for required fields
        state: 'State',
        zipCode: '00000',
        phone: data.contactInfo,
        email: data.email,
        active: true
      };
      
      const response = await superAdminAPI.createSchool(schoolData);
      
      if (response.success) {
        showToast.success('School Added', `${data.name} has been successfully added`);
        
        // Refresh schools list
        fetchSchools();
      }
    } catch (error) {
      console.error('Error adding school:', error);
      showToast.error('Error', 'Failed to add school');
    }
    
    setIsAddSchoolModalOpen(false);
  };

  const handleDeleteSchool = async (id: string, name: string) => {
    if (window.confirm(`Are you sure you want to delete ${name}?`)) {
      try {
        const response = await superAdminAPI.deleteSchool(id);
        
        if (response.success) {
          showToast.success('School Deleted', `${name} has been successfully deleted`);
          
          // Refresh schools list
          fetchSchools();
        }
      } catch (error) {
        console.error('Error deleting school:', error);
        showToast.error('Error', 'Failed to delete school');
      }
    }
  };

  const resetFilters = () => {
    setSearchQuery('');
    setStatusFilter('all');
    setSortBy('name');
    setSortOrder('asc');
  };

  const handleExport = async () => {
    try {
      const params = {
        search: searchQuery,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        sortBy,
        sortOrder
      };
      
      const response = await superAdminAPI.exportSchools(params);
      
      // Create and download the file
      const blob = new Blob([response], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `schools-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      showToast.success('Export Complete', 'Schools data has been exported successfully');
    } catch (error) {
      console.error('Error exporting schools:', error);
      showToast.error('Export Failed', 'Failed to export schools data');
    }
  };

  const hasActiveFilters = searchQuery || statusFilter !== 'all' || sortBy !== 'name' || sortOrder !== 'asc';

  // Remove client-side filtering since it's now handled by the backend
  const filteredSchools = schools;

  // Calculate summary statistics
  const totalStudents = filteredSchools.reduce((sum, school) => sum + school.students, 0);
  const totalTeachers = filteredSchools.reduce((sum, school) => sum + school.teachers, 0);
  const activeSchools = filteredSchools.filter(school => school.status === 'active').length;

  return (
    <Layout>
      {/* Enhanced Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-indigo-900 via-purple-800 to-blue-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-40 h-40 border border-white/10 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <Building2 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">School Network</h1>
                <p className="text-indigo-200 mt-1">Manage and oversee all educational institutions</p>
              </div>
            </div>
            <p className="text-indigo-100 text-lg max-w-2xl">
              Comprehensive oversight of your educational network with real-time insights and management tools.
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Globe className="h-4 w-4 mr-2" />
              <span>View Map</span>
            </Button>
            <Button
              onClick={() => setIsAddSchoolModalOpen(true)}
              className="bg-white text-indigo-900 hover:bg-indigo-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>Add School</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 mb-8">
        <Card className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Schools</p>
                <p className="text-3xl font-bold text-blue-900">{loading ? "..." : filteredSchools.length.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <div className="flex items-center text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-green-600 font-medium">{activeSchools} active</span>
                    <span className="text-gray-500 mx-2">•</span>
                    <span className="text-gray-600">{filteredSchools.length - activeSchools} inactive</span>
                  </div>
                </div>
              </div>
              <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Building2 className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="relative overflow-hidden bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-700">Total Students</p>
                <p className="text-3xl font-bold text-emerald-900">{loading ? "..." : totalStudents.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-emerald-600 font-medium">
                    Avg. {filteredSchools.length > 0 ? Math.round(totalStudents / filteredSchools.length) : 0} per school
                  </span>
                </div>
              </div>
              <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Total Teachers</p>
                <p className="text-3xl font-bold text-purple-900">{loading ? "..." : totalTeachers.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <SchoolIcon className="h-4 w-4 text-purple-600 mr-1" />
                  <span className="text-sm text-purple-600 font-medium">
                    Ratio {totalTeachers > 0 ? (totalStudents / totalTeachers).toFixed(1) : 0}:1
                  </span>
                </div>
              </div>
              <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl hover:shadow-2xl transition-all duration-300">
        <div className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <Search className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-slate-900">School Directory</h3>
                <p className="text-sm text-slate-600">Search and manage educational institutions</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search schools..."
                  className="pl-10 w-64 border-slate-200 focus:border-indigo-500 focus:ring-indigo-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>
        
        <div className="p-4 border-b border-slate-100/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className={`flex items-center gap-1 border-slate-200 hover:bg-slate-50 ${hasActiveFilters ? 'bg-indigo-50 border-indigo-200 text-indigo-700' : ''}`}
                >
                  <Filter className="h-4 w-4" />
                  <span>Filter</span>
                  {hasActiveFilters && <span className="ml-1 text-xs bg-indigo-600 text-white rounded-full px-1.5 py-0.5">•</span>}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <div className="px-2 py-1.5 text-sm font-medium">Status</div>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === 'all'}
                  onCheckedChange={() => setStatusFilter('all')}
                >
                  All Schools
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === 'active'}
                  onCheckedChange={() => setStatusFilter('active')}
                >
                  Active Only
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === 'inactive'}
                  onCheckedChange={() => setStatusFilter('inactive')}
                >
                  Inactive Only
                </DropdownMenuCheckboxItem>
                <DropdownMenuSeparator />
                <div className="px-2 py-1.5 text-sm font-medium">Sort By</div>
                <DropdownMenuCheckboxItem
                  checked={sortBy === 'name'}
                  onCheckedChange={() => setSortBy('name')}
                >
                  School Name
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={sortBy === 'students'}
                  onCheckedChange={() => setSortBy('students')}
                >
                  Student Count
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={sortBy === 'createdAt'}
                  onCheckedChange={() => setSortBy('createdAt')}
                >
                  Date Created
                </DropdownMenuCheckboxItem>
                {hasActiveFilters && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={resetFilters} className="text-destructive">
                      <X className="h-4 w-4 mr-2" />
                      Reset Filters
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            <Button 
              variant="outline" 
              size="sm" 
              className="flex items-center gap-1"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              <ArrowUpDown className="h-4 w-4" />
              <span>{sortOrder === 'asc' ? 'A-Z' : 'Z-A'}</span>
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="flex items-center gap-1"
              onClick={handleExport}
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="w-[250px]">
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => setSortBy('name')}>
                    School Name
                    <ArrowUpDown className="h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead>Headmaster</TableHead>
                <TableHead>Students</TableHead>
                <TableHead>Teachers</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading schools...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredSchools.length > 0 ? (
                filteredSchools.map((school) => (
                  <TableRow key={school.id} className="hover:bg-slate-50/50 transition-colors">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="bg-indigo-100 p-2 rounded-lg">
                          <Building2 className="h-4 w-4 text-indigo-600" />
                        </div>
                        <div>
                          <div className="font-semibold text-slate-900">{school.name}</div>
                          <div className="flex items-center text-xs text-slate-500 mt-1">
                            <MapPin className="h-3 w-3 mr-1" />
                            <span>{school.address || 'Address not provided'}</span>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="bg-emerald-100 p-1.5 rounded-full">
                          <GraduationCap className="h-3 w-3 text-emerald-600" />
                        </div>
                        <div>
                          <div className="font-medium text-slate-900">{school.headmasterName}</div>
                          <div className="text-xs text-slate-500">{school.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-blue-600" />
                        <span className="font-semibold text-blue-900">{school.students.toLocaleString()}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <SchoolIcon className="h-4 w-4 text-purple-600" />
                        <span className="font-semibold text-purple-900">{school.teachers.toLocaleString()}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={school.status === 'active' ? 'default' : 'secondary'}
                        className={
                          school.status === 'active' 
                            ? 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200' 
                            : 'bg-gray-100 text-gray-800 border-gray-200'
                        }
                      >
                        <div className={`w-2 h-2 rounded-full mr-2 ${school.status === 'active' ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                        {school.status === 'active' ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2 text-slate-600">
                        <Calendar className="h-4 w-4" />
                        <span>{format(new Date(school.createdAt), 'MMM d, yyyy')}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-56 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl">
                          <DropdownMenuItem className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3">
                            <div className="bg-blue-100 p-1.5 rounded-lg">
                              <Eye className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <span className="font-medium">View Details</span>
                              <p className="text-xs text-slate-500">See full information</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuItem className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3">
                            <div className="bg-emerald-100 p-1.5 rounded-lg">
                              <Edit className="h-4 w-4 text-emerald-600" />
                            </div>
                            <div>
                              <span className="font-medium">Edit School</span>
                              <p className="text-xs text-slate-500">Modify school details</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-slate-200" />
                          <DropdownMenuItem 
                            className="flex items-center gap-3 text-red-600 focus:text-red-600 cursor-pointer hover:bg-red-50 p-3"
                            onClick={() => handleDeleteSchool(school.id, school.name)}
                          >
                            <div className="bg-red-100 p-1.5 rounded-lg">
                              <Trash2 className="h-4 w-4 text-red-600" />
                            </div>
                            <div>
                              <span className="font-medium">Delete School</span>
                              <p className="text-xs text-red-500">Remove permanently</p>
                            </div>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No schools found matching your search criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        
        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{filteredSchools.length}</span> schools
            {hasActiveFilters && (
              <span className="ml-2">
                • Filtered
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={resetFilters}
                  className="ml-1 h-auto p-1 text-xs text-destructive hover:text-destructive"
                >
                  Clear
                </Button>
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>Previous</Button>
            <Button variant="outline" size="sm" disabled>Next</Button>
          </div>
        </div>
      </Card>

      <AddSchoolModal
        isOpen={isAddSchoolModalOpen}
        onClose={() => setIsAddSchoolModalOpen(false)}
        onSubmit={handleAddSchool}
      />
    </Layout>
  );
}