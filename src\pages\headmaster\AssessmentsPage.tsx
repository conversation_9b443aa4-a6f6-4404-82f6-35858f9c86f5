import React, { useState, useEffect } from "react";
import { Layout } from "@/components/common/Layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { headmasterAPI } from "@/lib/api";
import { showToast } from "@/lib/utils";
import {
  Search,
  Users,
  TrendingUp,
  BookOpen,
  Award,
  Download,
  FileText,
  Calculator,
  Target,
  BarChart3,
} from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface Assessment {
  id: string;
  studentName: string;
  classScore: number; // out of 50
  examScore: number; // out of 100
  totalClassScore: number; // scaled to 30%
  finalExamScore: number; // scaled to 70%
  totalScore: number; // sum of totalClassScore and finalExamScore
}

export function AssessmentsPage() {
  const [loading, setLoading] = useState(true);
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [filteredAssessments, setFilteredAssessments] = useState<Assessment[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTerm, setSelectedTerm] = useState("first");
  const [selectedClass, setSelectedClass] = useState("all");
  const [selectedSubject, setSelectedSubject] = useState("all");

  useEffect(() => {
    fetchClasses();
    fetchSubjects();
    fetchAssessments();
  }, [selectedTerm, selectedClass, selectedSubject]);

  useEffect(() => {
    filterAssessments();
  }, [assessments, searchQuery]);

  const fetchClasses = async () => {
    try {
      const response = await headmasterAPI.getClasses();
      if (response.success) {
        setClasses(response.data.classes);
      }
    } catch (error) {
      console.error("Error fetching classes:", error);
    }
  };

  const fetchSubjects = async () => {
    try {
      const response = await headmasterAPI.getCourses();
      if (response.success) {
        setSubjects(Array.isArray(response.data.data) ? response.data.data : []);
      }
    } catch (error) {
      console.error("Error fetching subjects:", error);
    }
  };

  const fetchAssessments = async () => {
    try {
      setLoading(true);
      // Mock data for now - in real app, this would call an API
      const mockAssessments: Assessment[] = [
        {
          id: "1",
          studentName: "John Smith",
          classScore: 42,
          examScore: 85,
          totalClassScore: 25.2, // (42/50) * 30
          finalExamScore: 59.5, // (85/100) * 70
          totalScore: 84.7,
        },
        {
          id: "2",
          studentName: "Sarah Johnson",
          classScore: 45,
          examScore: 92,
          totalClassScore: 27, // (45/50) * 30
          finalExamScore: 64.4, // (92/100) * 70
          totalScore: 91.4,
        },
        {
          id: "3",
          studentName: "Michael Brown",
          classScore: 38,
          examScore: 78,
          totalClassScore: 22.8, // (38/50) * 30
          finalExamScore: 54.6, // (78/100) * 70
          totalScore: 77.4,
        },
        {
          id: "4",
          studentName: "Emma Davis",
          classScore: 47,
          examScore: 88,
          totalClassScore: 28.2, // (47/50) * 30
          finalExamScore: 61.6, // (88/100) * 70
          totalScore: 89.8,
        },
        {
          id: "5",
          studentName: "David Wilson",
          classScore: 35,
          examScore: 72,
          totalClassScore: 21, // (35/50) * 30
          finalExamScore: 50.4, // (72/100) * 70
          totalScore: 71.4,
        },
      ];

      setAssessments(mockAssessments);
    } catch (error) {
      console.error("Error fetching assessments:", error);
      showToast.error("Error", "Failed to load assessments");
    } finally {
      setLoading(false);
    }
  };

  const filterAssessments = () => {
    let filtered = assessments;

    if (searchQuery) {
      filtered = filtered.filter((assessment) =>
        assessment.studentName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredAssessments(filtered);
  };

  const handleDownloadPDF = () => {
    // Create HTML content for PDF conversion
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; padding: 20px;">
        <h1 style="text-align: center; color: #333; border-bottom: 2px solid #666; padding-bottom: 10px;">Student Assessment Report</h1>
        
        <div style="margin: 20px 0; display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <p><strong>Term:</strong> ${selectedTerm.charAt(0).toUpperCase() + selectedTerm.slice(1)} Term</p>
            <p><strong>Class:</strong> ${
              selectedClass === "all" ? "All Classes" : classes.find((c) => c.id === selectedClass)?.name || "Unknown"
            }</p>
          </div>
          <div>
            <p><strong>Subject:</strong> ${
              selectedSubject === "all"
                ? "All Subjects"
                : subjects.find((s) => s.id === selectedSubject)?.name || "Unknown"
            }</p>
            <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
          </div>
        </div>
        
        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Student Name</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Class Score (50%)</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Total Class Score (30%)</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Exam Score (100%)</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Final Exam Score (70%)</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Total Score (100%)</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Grade</th>
            </tr>
          </thead>
          <tbody>
            ${filteredAssessments
              .map((assessment, index) => {
                const grade =
                  assessment.totalScore >= 80
                    ? "A"
                    : assessment.totalScore >= 70
                    ? "B"
                    : assessment.totalScore >= 60
                    ? "C"
                    : assessment.totalScore >= 50
                    ? "D"
                    : "F";
                return `
                <tr style="${index % 2 === 0 ? "background-color: #f9f9f9;" : ""}">
                  <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">${assessment.studentName}</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${assessment.classScore}/50</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${assessment.totalClassScore.toFixed(
                    1
                  )}/30</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${assessment.examScore}/100</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${assessment.finalExamScore.toFixed(
                    1
                  )}/70</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">${assessment.totalScore.toFixed(
                    1
                  )}/100</td>
                  <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">${grade}</td>
                </tr>
              `;
              })
              .join("")}
          </tbody>
        </table>
      </div>
    `;

    // Create a temporary element to convert HTML to PDF
    const element = document.createElement("div");
    element.innerHTML = htmlContent;
    element.style.position = "absolute";
    element.style.left = "-9999px";
    document.body.appendChild(element);

    // Use browser's print functionality to save as PDF
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Assessment Report</title>
            <style>
              @page { margin: 20px; }
              body { margin: 0; font-size: 12px; }
            </style>
          </head>
          <body>
            ${htmlContent}
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 100);
              }
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }

    // Clean up
    document.body.removeChild(element);

    showToast.success("PDF Ready", "Use the print dialog to save as PDF");
  };

  // Calculate summary statistics
  const totalStudents = filteredAssessments.length;
  const averageScore =
    totalStudents > 0 ? filteredAssessments.reduce((sum, a) => sum + a.totalScore, 0) / totalStudents : 0;
  const highestScore = totalStudents > 0 ? Math.max(...filteredAssessments.map((a) => a.totalScore)) : 0;
  const passRate =
    totalStudents > 0 ? (filteredAssessments.filter((a) => a.totalScore >= 50).length / totalStudents) * 100 : 0;

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-violet-900 via-purple-800 to-indigo-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>

        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Student Assessments</h1>
                <p className="text-violet-200 mt-1">Track and analyze student academic performance</p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Button
              onClick={handleDownloadPDF}
              className="bg-white text-violet-900 hover:bg-violet-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Download className="h-4 w-4 mr-2" />
              <span>Save as PDF</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Total Students</p>
              <p className="text-3xl font-bold text-blue-900">{totalStudents}</p>
            </div>
            <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-emerald-700">Average Score</p>
              <p className="text-3xl font-bold text-emerald-900">{averageScore.toFixed(1)}%</p>
            </div>
            <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Calculator className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-700">Highest Score</p>
              <p className="text-3xl font-bold text-orange-900">{highestScore.toFixed(1)}%</p>
            </div>
            <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Award className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700">Pass Rate</p>
              <p className="text-3xl font-bold text-purple-900">{passRate.toFixed(1)}%</p>
            </div>
            <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Target className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">Assessment Records</CardTitle>
                <p className="text-sm text-slate-600">Student performance tracking and analysis</p>
              </div>
            </div>
          </div>
        </CardHeader>

        {/* Filters */}
        <div className="p-6 border-b border-slate-100/50 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search students..."
              className="pl-10 border-slate-200 focus:border-violet-500 focus:ring-violet-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Select value={selectedTerm} onValueChange={setSelectedTerm}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Select Term" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="first">First Term</SelectItem>
              <SelectItem value="second">Second Term</SelectItem>
              <SelectItem value="third">Third Term</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Select Class" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Classes</SelectItem>
              {classes.map((cls) => (
                <SelectItem key={cls.id} value={cls.id}>
                  {cls.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedSubject} onValueChange={setSelectedSubject}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Select Subject" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Subjects</SelectItem>
              {subjects.map((subject) => (
                <SelectItem key={subject.id} value={subject.id}>
                  {subject.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Assessment Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="w-[200px]">Student Name</TableHead>
                <TableHead>Class Score (50%)</TableHead>
                <TableHead>Total Class Score (30%)</TableHead>
                <TableHead>Exam Score (100%)</TableHead>
                <TableHead>Final Exam Score (70%)</TableHead>
                <TableHead>Total Score</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-violet-600 mr-2"></div>
                      <span>Loading assessments...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredAssessments.length > 0 ? (
                filteredAssessments.map((assessment) => (
                  <TableRow key={assessment.id} className="hover:bg-slate-50/50 transition-colors">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="bg-violet-100 p-2 rounded-lg">
                          <Users className="h-4 w-4 text-violet-600" />
                        </div>
                        <div>
                          <div className="font-semibold text-slate-900">{assessment.studentName}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-slate-900">{assessment.classScore}/50</span>
                        <Badge
                          variant="outline"
                          className={`${
                            assessment.classScore >= 40
                              ? "bg-green-100 text-green-800 border-green-200"
                              : assessment.classScore >= 30
                              ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                              : "bg-red-100 text-red-800 border-red-200"
                          }`}
                        >
                          {((assessment.classScore / 50) * 100).toFixed(0)}%
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-slate-900">{assessment.totalClassScore.toFixed(1)}/30</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-slate-900">{assessment.examScore}/100</span>
                        <Badge
                          variant="outline"
                          className={`${
                            assessment.examScore >= 80
                              ? "bg-green-100 text-green-800 border-green-200"
                              : assessment.examScore >= 60
                              ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                              : "bg-red-100 text-red-800 border-red-200"
                          }`}
                        >
                          {assessment.examScore}%
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-slate-900">{assessment.finalExamScore.toFixed(1)}/70</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-slate-900">{assessment.totalScore.toFixed(1)}/100</span>
                        <Badge
                          variant="outline"
                          className={`${
                            assessment.totalScore >= 80
                              ? "bg-green-100 text-green-800 border-green-200"
                              : assessment.totalScore >= 60
                              ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                              : assessment.totalScore >= 50
                              ? "bg-orange-100 text-orange-800 border-orange-200"
                              : "bg-red-100 text-red-800 border-red-200"
                          }`}
                        >
                          {assessment.totalScore >= 80
                            ? "A"
                            : assessment.totalScore >= 70
                            ? "B"
                            : assessment.totalScore >= 60
                            ? "C"
                            : assessment.totalScore >= 50
                            ? "D"
                            : "F"}
                        </Badge>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    No assessments found matching your criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </Card>
    </Layout>
  );
}
