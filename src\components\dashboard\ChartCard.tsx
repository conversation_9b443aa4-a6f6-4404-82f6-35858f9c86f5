import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { HelpCircle, BarChart3, <PERSON><PERSON>hart, <PERSON><PERSON>hart, Loader2, ArrowUpDown } from 'lucide-react';
import { Button } from '../ui/button';

interface ChartCardProps {
  title: string;
  children: React.ReactNode | { [key: string]: React.ReactNode };
  tabs?: string[];
  defaultTab?: string;
  chartType?: 'bar' | 'line' | 'pie';
  tooltipText?: string;
  loading?: boolean;
  actionButton?: React.ReactNode;
}

export function ChartCard({
  title,
  children,
  tabs,
  defaultTab,
  chartType = 'bar',
  tooltipText,
  loading = false,
  actionButton
}: ChartCardProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || (tabs && tabs.length > 0 ? tabs[0] : ''));

  const chartIcons = {
    bar: <BarChart3 className="h-4 w-4" />,
    line: <LineChart className="h-4 w-4" />,
    pie: <PieChart className="h-4 w-4" />
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary/50" />
        </div>
      );
    }

    if (tabs && tabs.length > 0) {
      return (
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex items-center justify-between mb-4">
            <TabsList className="bg-muted/30">
              {tabs.map(tab => (
                <TabsTrigger 
                  key={tab} 
                  value={tab}
                  className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  {tab}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
          {tabs.map(tab => (
            <TabsContent key={tab} value={tab} className="mt-0 h-[300px]">
              {React.isValidElement(children) ? children : (children as { [key: string]: React.ReactNode })[tab]}
            </TabsContent>
          ))}
        </Tabs>
      );
    }

    return children;
  };

  return (
    <Card className="hover-lift blue-card h-full">
      <CardHeader className="flex flex-row items-center justify-between pb-2 border-b border-primary/10">
        <div className="flex items-center gap-2">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          {tooltipText && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">{tooltipText}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        <div className="flex items-center gap-2">
          {actionButton}
          <Button variant="ghost" size="icon" className="h-8 w-8 text-primary/70 hover:text-primary">
            <ArrowUpDown className="h-4 w-4" />
          </Button>
          <div className="p-2 rounded-md bg-primary/10">
            {chartIcons[chartType]}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        {renderContent()}
      </CardContent>
    </Card>
  );
}