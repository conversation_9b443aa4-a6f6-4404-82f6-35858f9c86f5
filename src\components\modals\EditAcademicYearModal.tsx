import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Edit } from 'lucide-react';

interface EditAcademicYearModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  year: any;
}

export function EditAcademicYearModal({ open, onClose, onSubmit, year }: EditAcademicYearModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    startDate: '',
    endDate: '',
    active: false
  });
  const [selectedTerms, setSelectedTerms] = useState<string[]>([]);

  const availableTerms = ['First Term', 'Second Term', 'Third Term'];

  useEffect(() => {
    if (year) {
      setFormData({
        name: year.name || '',
        startDate: year.startDate || '',
        endDate: year.endDate || '',
        active: year.active || false
      });
      setSelectedTerms(year.terms || []);
    }
  }, [year]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      terms: selectedTerms
    });
  };

  const handleTermChange = (term: string, checked: boolean) => {
    if (checked) {
      setSelectedTerms([...selectedTerms, term]);
    } else {
      setSelectedTerms(selectedTerms.filter(t => t !== term));
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Academic Year
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Academic Year Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              placeholder="e.g., 2024/2025"
              required
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                required
              />
            </div>
            <div>
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({...formData, endDate: e.target.value})}
                required
              />
            </div>
          </div>
          
          <div>
            <Label>Terms</Label>
            <div className="space-y-2 mt-2">
              {availableTerms.map((term) => (
                <div key={term} className="flex items-center space-x-2">
                  <Checkbox
                    id={term}
                    checked={selectedTerms.includes(term)}
                    onCheckedChange={(checked) => handleTermChange(term, checked as boolean)}
                  />
                  <Label htmlFor={term} className="text-sm">{term}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="active"
              checked={formData.active}
              onCheckedChange={(checked) => setFormData({...formData, active: checked as boolean})}
            />
            <Label htmlFor="active" className="text-sm">Set as active academic year</Label>
          </div>
          
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              <Edit className="h-4 w-4 mr-2" />
              Update Academic Year
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}