import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { Login } from "@/pages/auth/Login";
import { ForgotPassword } from "@/pages/auth/ForgotPassword";
import { ResetPassword } from "@/pages/auth/ResetPassword";
import { SuperAdminDashboard } from "@/pages/superadmin/SuperAdminDashboard";
import { SchoolsPage } from "@/pages/superadmin/SchoolsPage";
import { AnalyticsPage } from "@/pages/superadmin/AnalyticsPage";
import { SchoolSettingsPage } from "@/pages/superadmin/SchoolSettingsPage";
import { SettingsPage } from "@/pages/superadmin/SettingsPage";
import { HeadmasterDashboard } from "@/pages/headmaster/HeadmasterDashboard";
import { TeachersPage } from "@/pages/headmaster/TeachersPage";
import { StudentsPage } from "@/pages/headmaster/StudentsPage";
import { ParentsPage } from "@/pages/headmaster/ParentsPage";
import { ClassesPage } from "@/pages/headmaster/ClassesPage";
import { CoursesPage } from "@/pages/headmaster/CoursesPage";
import { AttendancePage } from "@/pages/headmaster/AttendancePage";
import { AssessmentsPage } from "@/pages/headmaster/AssessmentsPage";
import { ReportsPage } from "@/pages/headmaster/ReportsPage";
import { FeesManagementPage } from "@/pages/headmaster/FeesManagementPage";
import { ArrearsListPage } from "@/pages/headmaster/ArrearsListPage";
import { FinancialRecordsPage } from "@/pages/headmaster/FinancialRecordsPage";
import { AccountPage } from "@/pages/headmaster/AccountPage";
import { AccountUsersPage } from "@/pages/headmaster/AccountUsersPage";
import { TermsAcademicYearPage } from "@/pages/headmaster/TermsAcademicYearPage";
import { GradingSystemPage } from "@/pages/headmaster/GradingSystemPage";
import { StudentsPromotionPage } from "@/pages/headmaster/StudentsPromotionPage";
import { AnnouncementsPage } from "@/pages/headmaster/AnnouncementsPage";
import { CalendarPage } from "@/pages/headmaster/CalendarPage";
import { Toaster } from "@/components/ui/toaster";
import "./App.css";

// Protected route component
const ProtectedRoute = ({ children, allowedRoles }: { children: JSX.Element; allowedRoles: string[] }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles && !allowedRoles.includes(user.role)) {
    return <Navigate to="/" replace />;
  }

  return children;
};

// Root redirect component
function RootRedirect() {
  const { user } = useAuth();
  return <Navigate to={user?.role === "super_admin" ? "/admin/dashboard" : "/headmaster/dashboard"} replace />;
}

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="theme-preference">
      <AuthProvider>
        <Router>
          <Routes>
            {/* Auth Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />

            {/* Super Admin Routes */}
            <Route
              path="/admin/dashboard"
              element={
                <ProtectedRoute allowedRoles={["super_admin"]}>
                  <SuperAdminDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/schools"
              element={
                <ProtectedRoute allowedRoles={["super_admin"]}>
                  <SchoolsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/analytics"
              element={
                <ProtectedRoute allowedRoles={["super_admin"]}>
                  <AnalyticsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/school-settings"
              element={
                <ProtectedRoute allowedRoles={["super_admin"]}>
                  <SchoolSettingsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/settings"
              element={
                <ProtectedRoute allowedRoles={["super_admin"]}>
                  <SettingsPage />
                </ProtectedRoute>
              }
            />

            {/* Headmaster Routes */}
            <Route
              path="/headmaster/dashboard"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <HeadmasterDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/teachers"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <TeachersPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/students"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <StudentsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/parents"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <ParentsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/classes"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <ClassesPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/courses"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <CoursesPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/attendance"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <AttendancePage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/assessments"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <AssessmentsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/reports"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <ReportsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/fees"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <FeesManagementPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/arrears"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <ArrearsListPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/financial-records"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <FinancialRecordsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/account"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <AccountPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/account-users"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <AccountUsersPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/terms-academic-year"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <TermsAcademicYearPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/grading-system"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <GradingSystemPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/students-promotion"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <StudentsPromotionPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/announcements"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <AnnouncementsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/headmaster/calendar"
              element={
                <ProtectedRoute allowedRoles={["headmaster"]}>
                  <CalendarPage />
                </ProtectedRoute>
              }
            />

            {/* Default redirect */}
            <Route
              path="/"
              element={
                <ProtectedRoute allowedRoles={["headmaster", "super_admin"]}>
                  <RootRedirect />
                </ProtectedRoute>
              }
            />
            <Route
              path="*"
              element={
                <ProtectedRoute allowedRoles={["headmaster", "super_admin"]}>
                  <RootRedirect />
                </ProtectedRoute>
              }
            />

            {/* End of routes */}
          </Routes>
          <Toaster />
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
