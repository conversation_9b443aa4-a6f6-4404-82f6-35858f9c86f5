import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { 
  Users,
  UserPlus,
  Search,
  Edit,
  Trash2,
  MoreHorizontal,
  Loader2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AddUserModal } from '@/components/modals/AddUserModal';
import { EditUserModal } from '@/components/modals/EditUserModal';
import { RolePermissionsModal } from '@/components/modals/RolePermissionsModal';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

interface AccountUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: 'administrator' | 'teacher';
  createdAt: string;
  active: boolean;
}

export function AccountUsersPage() {
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState<AccountUser[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<AccountUser | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState('');
  const [rolePermissions, setRolePermissions] = useState({
    administrator: ['view_dashboard', 'view_teachers', 'create_teachers', 'edit_teachers', 'delete_teachers', 'view_students', 'create_students', 'edit_students', 'delete_students', 'view_parents', 'view_classes', 'create_classes', 'view_courses', 'view_attendance', 'manage_attendance', 'view_assessments', 'view_reports', 'view_fees', 'manage_fees', 'view_financial_records', 'view_announcements', 'create_announcements', 'view_calendar', 'manage_school_settings', 'manage_users'],
    teacher: ['view_dashboard', 'view_students', 'view_classes', 'view_attendance', 'manage_attendance', 'view_assessments', 'view_announcements', 'view_calendar']
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockUsers: AccountUser[] = [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Administrator',
          email: '<EMAIL>',
          phone: '+233 24 123 4567',
          role: 'administrator',
          createdAt: '2024-01-15',
          active: true
        },
        {
          id: '2',
          firstName: 'Sarah',
          lastName: 'Teacher',
          email: '<EMAIL>',
          phone: '+233 24 234 5678',
          role: 'teacher',
          createdAt: '2024-01-20',
          active: true
        },
        {
          id: '3',
          firstName: 'Mike',
          lastName: 'Admin',
          email: '<EMAIL>',
          phone: '+233 24 345 6789',
          role: 'administrator',
          createdAt: '2024-02-01',
          active: false
        }
      ];
      setUsers(mockUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      showToast.error('Error', 'Failed to load account users');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async (data: any) => {
    try {
      const newUser: AccountUser = {
        id: (users.length + 1).toString(),
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        role: data.role,
        createdAt: new Date().toISOString().split('T')[0],
        active: true
      };
      setUsers([...users, newUser]);
      setIsAddModalOpen(false);
      showToast.success('Success', 'User created successfully');
    } catch (error) {
      console.error('Error creating user:', error);
      showToast.error('Error', 'Failed to create user');
    }
  };

  const handleEditUser = (user: AccountUser) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  const handleUpdateUser = async (data: any) => {
    if (!selectedUser) return;
    try {
      const updatedUsers = users.map(user => 
        user.id === selectedUser.id 
          ? { ...user, firstName: data.firstName, lastName: data.lastName, email: data.email, phone: data.phone, role: data.role }
          : user
      );
      setUsers(updatedUsers);
      setIsEditModalOpen(false);
      showToast.success('Success', 'User updated successfully');
    } catch (error) {
      console.error('Error updating user:', error);
      showToast.error('Error', 'Failed to update user');
    }
  };

  const handleDeleteUser = (user: AccountUser) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteUser = async () => {
    if (!selectedUser) return;
    try {
      const updatedUsers = users.filter(user => user.id !== selectedUser.id);
      setUsers(updatedUsers);
      setIsDeleteDialogOpen(false);
      showToast.success('Success', 'User deleted successfully');
    } catch (error) {
      console.error('Error deleting user:', error);
      showToast.error('Error', 'Failed to delete user');
    }
  };

  const handleRolePermissions = (role: string) => {
    setSelectedRole(role);
    setIsPermissionsModalOpen(true);
  };

  const handleUpdatePermissions = (data: any) => {
    setRolePermissions(prev => ({
      ...prev,
      [data.role]: data.permissions
    }));
    setIsPermissionsModalOpen(false);
    showToast.success('Success', `${data.role} permissions updated successfully`);
  };

  const filteredUsers = users.filter(user =>
    `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Account Users</h1>
                <p className="text-indigo-200 mt-1">Manage dashboard access users</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-white text-indigo-900 hover:bg-indigo-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              <span>Add User</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-3 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Total Users</p>
              <p className="text-3xl font-bold text-blue-900">{users.length}</p>
            </div>
            <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-green-100/50 border-green-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-700">Administrators</p>
              <p className="text-3xl font-bold text-green-900">{users.filter(u => u.role === 'administrator').length}</p>
            </div>
            <div className="bg-green-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700">Teachers</p>
              <p className="text-3xl font-bold text-purple-900">{users.filter(u => u.role === 'teacher').length}</p>
            </div>
            <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Role Permissions Section */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl mb-8">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <CardTitle className="text-lg font-semibold text-slate-900">Role Permissions</CardTitle>
          <p className="text-sm text-slate-600">Configure access permissions for each role</p>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex justify-between items-center mb-3">
                <div>
                  <h3 className="font-semibold text-blue-900">Administrator</h3>
                  <p className="text-sm text-blue-700">{rolePermissions.administrator.length} permissions assigned</p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleRolePermissions('administrator')}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  Configure
                </Button>
              </div>
              <p className="text-sm text-blue-600">Full access to manage school operations and users</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <div className="flex justify-between items-center mb-3">
                <div>
                  <h3 className="font-semibold text-green-900">Teacher</h3>
                  <p className="text-sm text-green-700">{rolePermissions.teacher.length} permissions assigned</p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleRolePermissions('teacher')}
                  className="border-green-300 text-green-700 hover:bg-green-100"
                >
                  Configure
                </Button>
              </div>
              <p className="text-sm text-green-600">Limited access focused on teaching and student management</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">Dashboard Users</CardTitle>
                <p className="text-sm text-slate-600">Users with dashboard access</p>
              </div>
            </div>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search users..."
                className="pl-10 w-64 border-slate-200 focus:border-indigo-500 focus:ring-indigo-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>

        {/* Users Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading users...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <TableRow key={user.id} className="hover:bg-slate-50/50 transition-colors">
                    <TableCell>
                      <div className="font-semibold text-slate-900">{user.firstName} {user.lastName}</div>
                    </TableCell>
                    <TableCell>
                      <span className="text-slate-700">{user.email}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-slate-700">{user.phone}</span>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`${
                          user.role === 'administrator' 
                            ? 'bg-blue-100 text-blue-800 border-blue-200'
                            : 'bg-green-100 text-green-800 border-green-200'
                        }`}
                      >
                        {user.role === 'administrator' ? 'Administrator' : 'Teacher'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`${
                          user.active 
                            ? 'bg-green-100 text-green-800 border-green-200'
                            : 'bg-gray-100 text-gray-800 border-gray-200'
                        }`}
                      >
                        {user.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-slate-600">{new Date(user.createdAt).toLocaleDateString()}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem 
                            className="flex items-center gap-2 cursor-pointer"
                            onClick={() => handleEditUser(user)}
                          >
                            <Edit className="h-4 w-4" />
                            <span>Edit User</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="flex items-center gap-2 text-red-600 focus:text-red-600 cursor-pointer"
                            onClick={() => handleDeleteUser(user)}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>Delete User</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No users found matching your search criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </Card>

      {/* Modals */}
      <AddUserModal 
        open={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddUser}
      />
      
      {selectedUser && (
        <EditUserModal
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={handleUpdateUser}
          user={selectedUser}
        />
      )}
      
      <RolePermissionsModal
        open={isPermissionsModalOpen}
        onClose={() => setIsPermissionsModalOpen(false)}
        onSubmit={handleUpdatePermissions}
        role={selectedRole}
        permissions={rolePermissions[selectedRole as keyof typeof rolePermissions] || []}
      />
      
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the user
              {selectedUser && ` ${selectedUser.firstName} ${selectedUser.lastName}`} and remove their dashboard access.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteUser}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}