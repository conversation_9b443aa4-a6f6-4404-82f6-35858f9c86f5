import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Paintbrush, Save, RotateCcw } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useThemeContext } from '@/contexts/ThemeContext';

interface ColorOption {
  name: string;
  variable: string;
  value: string;
}

interface ThemeCustomizerProps {
  onSave?: (colors: Record<string, string>) => void;
}

export function ThemeCustomizer({ onSave }: ThemeCustomizerProps) {
  const { toast } = useToast();
  const { applyTheme, resetTheme, currentTheme } = useThemeContext();
  const [colors, setColors] = useState<ColorOption[]>([
    { name: 'Primary', variable: '--primary', value: '260 60% 45%' },
    { name: 'Secondary', variable: '--secondary', value: '250 50% 60%' },
    { name: 'Accent', variable: '--accent', value: '270 70% 85%' }
  ]);

  // Load current theme on mount
  useEffect(() => {
    if (currentTheme) {
      const updatedColors = [...colors];
      Object.entries(currentTheme).forEach(([variable, value]) => {
        const colorIndex = updatedColors.findIndex(c => c.variable === variable);
        if (colorIndex !== -1) {
          updatedColors[colorIndex].value = value as string;
        }
      });
      setColors(updatedColors);
    }
  }, [currentTheme]);

  const handleColorChange = (index: number, value: string) => {
    const newColors = [...colors];
    newColors[index].value = value;
    setColors(newColors);
    
    // Apply color change in real-time
    document.documentElement.style.setProperty(colors[index].variable, value);
  };

  const handleSave = () => {
    const colorValues = colors.reduce((acc, color) => {
      acc[color.variable] = color.value;
      return acc;
    }, {} as Record<string, string>);
    
    // Apply and save theme
    applyTheme(colorValues);
    
    if (onSave) {
      onSave(colorValues);
    }
    
    toast({
      title: "Theme Updated",
      description: "Your school theme colors have been saved successfully.",
    });
  };

  const handleReset = () => {
    // Reset to default colors
    resetTheme();
    
    // Update local state
    setColors([
      { name: 'Primary', variable: '--primary', value: '260 60% 45%' },
      { name: 'Secondary', variable: '--secondary', value: '250 50% 60%' },
      { name: 'Accent', variable: '--accent', value: '270 70% 85%' }
    ]);
    
    toast({
      title: "Theme Reset",
      description: "Your school theme colors have been reset to default.",
    });
  };

  // Helper function to convert HSL to hex for color input
  const hslToHex = (hsl: string) => {
    try {
      const [h, s, l] = hsl.split(' ').map(val => parseFloat(val));
      const hDecimal = h / 360;
      const sDecimal = parseInt(s) / 100;
      const lDecimal = parseInt(l) / 100;
      
      if (sDecimal === 0) {
        const value = Math.round(lDecimal * 255);
        return `#${value.toString(16).padStart(2, '0').repeat(3)}`;
      }
      
      const getRGB = (p: number, q: number, t: number) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };
      
      const q = lDecimal < 0.5 ? lDecimal * (1 + sDecimal) : lDecimal + sDecimal - lDecimal * sDecimal;
      const p = 2 * lDecimal - q;
      
      const r = Math.round(getRGB(p, q, hDecimal + 1/3) * 255);
      const g = Math.round(getRGB(p, q, hDecimal) * 255);
      const b = Math.round(getRGB(p, q, hDecimal - 1/3) * 255);
      
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    } catch (error) {
      console.error('Error converting HSL to hex:', error);
      return '#6d28d9'; // Default purple color
    }
  };

  // Helper function to convert hex to HSL for storing
  const hexToHsl = (hex: string) => {
    try {
      // Remove the # if present
      hex = hex.replace(/^#/, '');
      
      // Parse the hex values
      let r = parseInt(hex.slice(0, 2), 16) / 255;
      let g = parseInt(hex.slice(2, 4), 16) / 255;
      let b = parseInt(hex.slice(4, 6), 16) / 255;
      
      // Find min and max values
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h = 0, s = 0, l = (max + min) / 2;
      
      if (max !== min) {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        
        switch (max) {
          case r: h = (g - b) / d + (g < b ? 6 : 0); break;
          case g: h = (b - r) / d + 2; break;
          case b: h = (r - g) / d + 4; break;
        }
        
        h /= 6;
      }
      
      // Convert to HSL format
      h = Math.round(h * 360);
      s = Math.round(s * 100);
      l = Math.round(l * 100);
      
      return `${h} ${s}% ${l}%`;
    } catch (error) {
      console.error('Error converting hex to HSL:', error);
      return '260 60% 45%'; // Default purple color
    }
  };

  return (
    <Card className="app-shadow">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Paintbrush className="h-5 w-5 mr-2" />
          School Theme Customization
        </CardTitle>
        <CardDescription>
          Customize your dashboard colors to match your school's branding
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {colors.map((color, index) => (
            <div key={color.variable} className="space-y-2">
              <Label htmlFor={`color-${index}`} className="flex items-center justify-between">
                {color.name}
                <div 
                  className="h-4 w-4 rounded-full" 
                  style={{ backgroundColor: `hsl(${color.value})` }}
                />
              </Label>
              <input
                id={`color-${index}`}
                type="color"
                value={hslToHex(color.value)}
                onChange={(e) => handleColorChange(index, hexToHsl(e.target.value))}
                className="w-full h-10 rounded-md cursor-pointer"
              />
            </div>
          ))}
        </div>
        
        <div className="flex flex-col space-y-4 mt-4">
          <div className="p-4 rounded-md bg-card border">
            <h3 className="text-sm font-medium mb-2">Preview</h3>
            <div className="flex flex-wrap gap-2">
              <div className="p-4 rounded-md bg-primary text-primary-foreground">Primary</div>
              <div className="p-4 rounded-md bg-secondary text-secondary-foreground">Secondary</div>
              <div className="p-4 rounded-md bg-accent text-accent-foreground">Accent</div>
              <div className="p-4 rounded-md bg-muted text-muted-foreground">Muted</div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-between pt-4">
          <Button 
            variant="outline" 
            onClick={handleReset}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset to Default
          </Button>
          <Button 
            onClick={handleSave}
            className="gradient-bg text-white hover:opacity-90 flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            Save Theme
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}