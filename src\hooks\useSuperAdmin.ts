import { useState, useEffect, useCallback } from 'react';
import { superAdminAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';

interface School {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  phone: string;
  email: string;
  website?: string;
  contactInfo: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Admin {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: string;
  schoolId: string;
  schoolName: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface DashboardStats {
  totalSchools: number;
  totalAdmins: number;
  activeSchools: number;
  inactiveSchools: number;
  recentSchools: School[];
  recentAdmins: Admin[];
}

interface ChartData {
  schoolsGrowth: Array<{ month: string; count: number }>;
  adminsGrowth: Array<{ month: string; count: number }>;
  schoolsByState: Array<{ state: string; count: number }>;
}

interface SchoolFormData {
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  phone: string;
  email: string;
  website?: string;
  contactInfo: string;
}

interface AdminFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  schoolId: string;
  password: string;
}

interface UseSuperAdminReturn {
  // Schools State
  schools: School[];
  schoolsLoading: boolean;
  schoolsError: string | null;
  schoolsTotal: number;
  schoolsPage: number;
  schoolsLimit: number;

  // Admins State
  admins: Admin[];
  adminsLoading: boolean;
  adminsError: string | null;
  adminsTotal: number;
  adminsPage: number;
  adminsLimit: number;

  // Dashboard State
  dashboardStats: DashboardStats | null;
  chartData: ChartData | null;
  dashboardLoading: boolean;

  // Actions
  fetchSchools: (params?: any) => Promise<void>;
  fetchAdmins: (params?: any) => Promise<void>;
  fetchDashboardData: () => Promise<void>;
  createSchool: (data: SchoolFormData) => Promise<void>;
  updateSchool: (id: string, data: Partial<SchoolFormData>) => Promise<void>;
  deleteSchool: (id: string) => Promise<void>;
  createAdmin: (data: AdminFormData) => Promise<void>;
  updateAdmin: (id: string, data: Partial<AdminFormData>) => Promise<void>;
  deleteAdmin: (id: string) => Promise<void>;
  exportSchools: (params?: any) => Promise<void>;
}

export const useSuperAdmin = (): UseSuperAdminReturn => {
  // Schools State
  const [schools, setSchools] = useState<School[]>([]);
  const [schoolsLoading, setSchoolsLoading] = useState(false);
  const [schoolsError, setSchoolsError] = useState<string | null>(null);
  const [schoolsTotal, setSchoolsTotal] = useState(0);
  const [schoolsPage, setSchoolsPage] = useState(1);
  const [schoolsLimit] = useState(10);

  // Admins State
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [adminsLoading, setAdminsLoading] = useState(false);
  const [adminsError, setAdminsError] = useState<string | null>(null);
  const [adminsTotal, setAdminsTotal] = useState(0);
  const [adminsPage, setAdminsPage] = useState(1);
  const [adminsLimit] = useState(10);

  // Dashboard State
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [dashboardLoading, setDashboardLoading] = useState(false);

  const fetchSchools = useCallback(async (params: any = {}) => {
    try {
      setSchoolsLoading(true);
      setSchoolsError(null);

      const response = await superAdminAPI.getSchools({
        page: schoolsPage,
        limit: schoolsLimit,
        ...params
      });

      if (response.success) {
        setSchools(response.data.data || []);
        setSchoolsTotal(response.data.total || 0);
      }
    } catch (err) {
      console.error('Failed to fetch schools:', err);
      setSchoolsError('Failed to load schools');
      showToast('Failed to load schools', 'error');
    } finally {
      setSchoolsLoading(false);
    }
  }, [schoolsPage, schoolsLimit]);

  const fetchAdmins = useCallback(async (params: any = {}) => {
    try {
      setAdminsLoading(true);
      setAdminsError(null);

      const response = await superAdminAPI.getAdmins({
        page: adminsPage,
        limit: adminsLimit,
        ...params
      });

      if (response.success) {
        setAdmins(response.data.data || []);
        setAdminsTotal(response.data.total || 0);
      }
    } catch (err) {
      console.error('Failed to fetch admins:', err);
      setAdminsError('Failed to load admins');
      showToast('Failed to load admins', 'error');
    } finally {
      setAdminsLoading(false);
    }
  }, [adminsPage, adminsLimit]);

  const fetchDashboardData = useCallback(async () => {
    try {
      setDashboardLoading(true);

      const [statsResponse, chartResponse] = await Promise.all([
        superAdminAPI.getDashboardStats(),
        superAdminAPI.getChartData()
      ]);

      if (statsResponse.success) {
        setDashboardStats(statsResponse.data);
      }

      if (chartResponse.success) {
        setChartData(chartResponse.data);
      }
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err);
      showToast('Failed to load dashboard data', 'error');
    } finally {
      setDashboardLoading(false);
    }
  }, []);

  const createSchool = useCallback(async (data: SchoolFormData) => {
    try {
      const response = await superAdminAPI.createSchool(data);
      if (response.success) {
        showToast('School created successfully', 'success');
        fetchSchools();
        fetchDashboardData();
      }
    } catch (err) {
      console.error('Failed to create school:', err);
      showToast('Failed to create school', 'error');
      throw err;
    }
  }, [fetchSchools, fetchDashboardData]);

  const updateSchool = useCallback(async (id: string, data: Partial<SchoolFormData>) => {
    try {
      const response = await superAdminAPI.updateSchool(id, data);
      if (response.success) {
        showToast('School updated successfully', 'success');
        fetchSchools();
      }
    } catch (err) {
      console.error('Failed to update school:', err);
      showToast('Failed to update school', 'error');
      throw err;
    }
  }, [fetchSchools]);

  const deleteSchool = useCallback(async (id: string) => {
    try {
      const response = await superAdminAPI.deleteSchool(id);
      if (response.success) {
        showToast('School deleted successfully', 'success');
        fetchSchools();
        fetchDashboardData();
      }
    } catch (err) {
      console.error('Failed to delete school:', err);
      showToast('Failed to delete school', 'error');
      throw err;
    }
  }, [fetchSchools, fetchDashboardData]);

  const createAdmin = useCallback(async (data: AdminFormData) => {
    try {
      const response = await superAdminAPI.createAdmin(data);
      if (response.success) {
        showToast('Admin created successfully', 'success');
        fetchAdmins();
        fetchDashboardData();
      }
    } catch (err) {
      console.error('Failed to create admin:', err);
      showToast('Failed to create admin', 'error');
      throw err;
    }
  }, [fetchAdmins, fetchDashboardData]);

  const updateAdmin = useCallback(async (id: string, data: Partial<AdminFormData>) => {
    try {
      const response = await superAdminAPI.updateAdmin(id, data);
      if (response.success) {
        showToast('Admin updated successfully', 'success');
        fetchAdmins();
      }
    } catch (err) {
      console.error('Failed to update admin:', err);
      showToast('Failed to update admin', 'error');
      throw err;
    }
  }, [fetchAdmins]);

  const deleteAdmin = useCallback(async (id: string) => {
    try {
      const response = await superAdminAPI.deleteAdmin(id);
      if (response.success) {
        showToast('Admin deleted successfully', 'success');
        fetchAdmins();
        fetchDashboardData();
      }
    } catch (err) {
      console.error('Failed to delete admin:', err);
      showToast('Failed to delete admin', 'error');
      throw err;
    }
  }, [fetchAdmins, fetchDashboardData]);

  const exportSchools = useCallback(async (params: any = {}) => {
    try {
      const response = await superAdminAPI.exportSchools(params);
      // Handle CSV download
      const blob = new Blob([response], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `schools-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      showToast('Schools exported successfully', 'success');
    } catch (err) {
      console.error('Failed to export schools:', err);
      showToast('Failed to export schools', 'error');
    }
  }, []);

  return {
    // Schools State
    schools,
    schoolsLoading,
    schoolsError,
    schoolsTotal,
    schoolsPage,
    schoolsLimit,

    // Admins State
    admins,
    adminsLoading,
    adminsError,
    adminsTotal,
    adminsPage,
    adminsLimit,

    // Dashboard State
    dashboardStats,
    chartData,
    dashboardLoading,

    // Actions
    fetchSchools,
    fetchAdmins,
    fetchDashboardData,
    createSchool,
    updateSchool,
    deleteSchool,
    createAdmin,
    updateAdmin,
    deleteAdmin,
    exportSchools,
  };
};
