import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  cols?: {
    default: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: string;
}

export function ResponsiveGrid({
  children,
  className,
  cols = { default: 1 },
  gap = "gap-4",
  ...props
}: ResponsiveGridProps) {
  const { default: defaultCols, sm, md, lg, xl } = cols;
  
  const getGridCols = () => {
    const classes = [];
    
    classes.push(`grid-cols-${defaultCols}`);
    if (sm) classes.push(`sm:grid-cols-${sm}`);
    if (md) classes.push(`md:grid-cols-${md}`);
    if (lg) classes.push(`lg:grid-cols-${lg}`);
    if (xl) classes.push(`xl:grid-cols-${xl}`);
    
    return classes.join(' ');
  };
  
  return (
    <div 
      className={cn(
        "grid", 
        getGridCols(),
        gap,
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}