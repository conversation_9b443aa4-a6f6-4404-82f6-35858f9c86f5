import React, { useState } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar,
  Download,
  Printer,
  Mail,
  BarChart3,
  PieChart,
  LineChart
} from 'lucide-react';
import { format, subDays } from 'date-fns';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AttendanceReportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AttendanceReportModal({ isOpen, onClose }: AttendanceReportModalProps) {
  const [reportType, setReportType] = useState('daily');
  const [selectedClass, setSelectedClass] = useState('all');
  
  // Mock data for reports
  const mockClasses = [
    { value: 'all', label: 'All Classes' },
    { value: '9A', label: 'Class 9A' },
    { value: '9B', label: 'Class 9B' },
    { value: '10A', label: 'Class 10A' },
    { value: '10B', label: 'Class 10B' },
    { value: '11A', label: 'Class 11A' },
    { value: '11B', label: 'Class 11B' },
    { value: '12A', label: 'Class 12A' },
    { value: '12B', label: 'Class 12B' },
  ];
  
  const mockAttendanceData = {
    daily: [
      { date: format(new Date(), 'yyyy-MM-dd'), present: 85, late: 10, absent: 5 },
      { date: format(subDays(new Date(), 1), 'yyyy-MM-dd'), present: 82, late: 8, absent: 10 },
      { date: format(subDays(new Date(), 2), 'yyyy-MM-dd'), present: 88, late: 7, absent: 5 },
      { date: format(subDays(new Date(), 3), 'yyyy-MM-dd'), present: 80, late: 12, absent: 8 },
      { date: format(subDays(new Date(), 4), 'yyyy-MM-dd'), present: 86, late: 9, absent: 5 },
    ],
    weekly: [
      { week: 'Week 1', present: 84, late: 9, absent: 7 },
      { week: 'Week 2', present: 86, late: 8, absent: 6 },
      { week: 'Week 3', present: 82, late: 10, absent: 8 },
      { week: 'Week 4', present: 85, late: 9, absent: 6 },
    ],
    monthly: [
      { month: 'January', present: 85, late: 8, absent: 7 },
      { month: 'February', present: 83, late: 10, absent: 7 },
      { month: 'March', present: 86, late: 9, absent: 5 },
      { month: 'April', present: 84, late: 8, absent: 8 },
      { month: 'May', present: 87, late: 7, absent: 6 },
    ],
    classwise: {
      '9A': { present: 88, late: 7, absent: 5 },
      '9B': { present: 85, late: 10, absent: 5 },
      '10A': { present: 82, late: 8, absent: 10 },
      '10B': { present: 86, late: 9, absent: 5 },
      '11A': { present: 84, late: 10, absent: 6 },
      '11B': { present: 83, late: 9, absent: 8 },
      '12A': { present: 90, late: 5, absent: 5 },
      '12B': { present: 80, late: 12, absent: 8 },
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Attendance Reports</DialogTitle>
          <DialogDescription>
            View and analyze attendance data for your school
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Select class" />
            </SelectTrigger>
            <SelectContent>
              {mockClasses.map((cls) => (
                <SelectItem key={cls.value} value={cls.value}>
                  {cls.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <Printer className="h-4 w-4" />
              <span>Print</span>
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <Mail className="h-4 w-4" />
              <span>Email</span>
            </Button>
          </div>
        </div>
        
        <Tabs defaultValue="daily" value={reportType} onValueChange={setReportType} className="w-full">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="daily">Daily</TabsTrigger>
            <TabsTrigger value="weekly">Weekly</TabsTrigger>
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="classwise">Class-wise</TabsTrigger>
          </TabsList>
          
          <TabsContent value="daily" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h3 className="text-lg font-medium mb-2">Daily Attendance Trend</h3>
                <div className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
                  <LineChart className="h-8 w-8 text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">Chart visualization would appear here</span>
                </div>
              </Card>
              
              <Card className="p-4">
                <h3 className="text-lg font-medium mb-2">Daily Breakdown</h3>
                <div className="space-y-4">
                  {mockAttendanceData.daily.map((day, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{format(new Date(day.date), 'MMMM d, yyyy')}</span>
                        <span className="text-muted-foreground">
                          Present: {day.present}% | Late: {day.late}% | Absent: {day.absent}%
                        </span>
                      </div>
                      <div className="flex h-2 rounded-full overflow-hidden">
                        <div 
                          className="bg-success" 
                          style={{ width: `${day.present}%` }}
                        ></div>
                        <div 
                          className="bg-warning" 
                          style={{ width: `${day.late}%` }}
                        ></div>
                        <div 
                          className="bg-destructive" 
                          style={{ width: `${day.absent}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="weekly" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h3 className="text-lg font-medium mb-2">Weekly Attendance Trend</h3>
                <div className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
                  <BarChart3 className="h-8 w-8 text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">Chart visualization would appear here</span>
                </div>
              </Card>
              
              <Card className="p-4">
                <h3 className="text-lg font-medium mb-2">Weekly Breakdown</h3>
                <div className="space-y-4">
                  {mockAttendanceData.weekly.map((week, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{week.week}</span>
                        <span className="text-muted-foreground">
                          Present: {week.present}% | Late: {week.late}% | Absent: {week.absent}%
                        </span>
                      </div>
                      <div className="flex h-2 rounded-full overflow-hidden">
                        <div 
                          className="bg-success" 
                          style={{ width: `${week.present}%` }}
                        ></div>
                        <div 
                          className="bg-warning" 
                          style={{ width: `${week.late}%` }}
                        ></div>
                        <div 
                          className="bg-destructive" 
                          style={{ width: `${week.absent}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="monthly" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h3 className="text-lg font-medium mb-2">Monthly Attendance Trend</h3>
                <div className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
                  <BarChart3 className="h-8 w-8 text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">Chart visualization would appear here</span>
                </div>
              </Card>
              
              <Card className="p-4">
                <h3 className="text-lg font-medium mb-2">Monthly Breakdown</h3>
                <div className="space-y-4">
                  {mockAttendanceData.monthly.map((month, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{month.month}</span>
                        <span className="text-muted-foreground">
                          Present: {month.present}% | Late: {month.late}% | Absent: {month.absent}%
                        </span>
                      </div>
                      <div className="flex h-2 rounded-full overflow-hidden">
                        <div 
                          className="bg-success" 
                          style={{ width: `${month.present}%` }}
                        ></div>
                        <div 
                          className="bg-warning" 
                          style={{ width: `${month.late}%` }}
                        ></div>
                        <div 
                          className="bg-destructive" 
                          style={{ width: `${month.absent}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="classwise" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h3 className="text-lg font-medium mb-2">Class-wise Attendance</h3>
                <div className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
                  <PieChart className="h-8 w-8 text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">Chart visualization would appear here</span>
                </div>
              </Card>
              
              <Card className="p-4">
                <h3 className="text-lg font-medium mb-2">Class Breakdown</h3>
                <div className="space-y-4">
                  {Object.entries(mockAttendanceData.classwise).map(([className, data], index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Class {className}</span>
                        <span className="text-muted-foreground">
                          Present: {data.present}% | Late: {data.late}% | Absent: {data.absent}%
                        </span>
                      </div>
                      <div className="flex h-2 rounded-full overflow-hidden">
                        <div 
                          className="bg-success" 
                          style={{ width: `${data.present}%` }}
                        ></div>
                        <div 
                          className="bg-warning" 
                          style={{ width: `${data.late}%` }}
                        ></div>
                        <div 
                          className="bg-destructive" 
                          style={{ width: `${data.absent}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}