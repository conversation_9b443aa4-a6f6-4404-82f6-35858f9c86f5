import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarIcon, Clock, MapPin } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

const eventSchema = z.object({
  title: z.string().min(2, { message: 'Title must be at least 2 characters' }),
  date: z.date({ required_error: 'Date is required' }),
  startTime: z.string().min(1, { message: 'Start time is required' }),
  endTime: z.string().min(1, { message: 'End time is required' }),
  location: z.string().min(1, { message: 'Location is required' }),
  description: z.string().optional(),
  type: z.string().min(1, { message: 'Event type is required' }),
  attendees: z.array(z.string()).min(1, { message: 'At least one attendee group must be selected' }),
});

export type AddEventFormData = z.infer<typeof eventSchema>;

interface AddEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AddEventFormData) => void;
}

export function AddEventModal({ isOpen, onClose, onSubmit }: AddEventModalProps) {
  const form = useForm<AddEventFormData>({
    resolver: zodResolver(eventSchema),
    defaultValues: {
      title: '',
      date: new Date(),
      startTime: '',
      endTime: '',
      location: '',
      description: '',
      type: 'meeting',
      attendees: ['all'],
    },
  });
  
  const [selectedAttendees, setSelectedAttendees] = useState<string[]>(['all']);
  
  const handleAttendeeChange = (attendee: string, checked: boolean) => {
    if (attendee === 'all' && checked) {
      setSelectedAttendees(['all']);
      form.setValue('attendees', ['all']);
    } else {
      let newAttendees = [...selectedAttendees];
      
      if (checked) {
        // Remove 'all' if it exists
        newAttendees = newAttendees.filter(a => a !== 'all');
        // Add the new attendee
        newAttendees.push(attendee);
      } else {
        // Remove the attendee
        newAttendees = newAttendees.filter(a => a !== attendee);
        // If empty, add 'all'
        if (newAttendees.length === 0) {
          newAttendees = ['all'];
        }
      }
      
      setSelectedAttendees(newAttendees);
      form.setValue('attendees', newAttendees);
    }
  };

  const handleSubmit = (data: AddEventFormData) => {
    onSubmit(data);
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add New Event</DialogTitle>
          <DialogDescription>
            Create a new event in the school calendar.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Event Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter event title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Event Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select event type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="meeting">Meeting</SelectItem>
                        <SelectItem value="sports">Sports</SelectItem>
                        <SelectItem value="academic">Academic</SelectItem>
                        <SelectItem value="holiday">Holiday</SelectItem>
                        <SelectItem value="cultural">Cultural</SelectItem>
                        <SelectItem value="guidance">Guidance</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Time</FormLabel>
                    <div className="relative">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <FormControl>
                        <Input type="time" className="pl-10" {...field} />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="endTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Time</FormLabel>
                    <div className="relative">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <FormControl>
                        <Input type="time" className="pl-10" {...field} />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <FormControl>
                      <Input placeholder="Event location" className="pl-10" {...field} />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Enter event details..." 
                      className="min-h-[100px]" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="attendees"
              render={() => (
                <FormItem>
                  <FormLabel>Attendees</FormLabel>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="attendees-all" 
                        checked={selectedAttendees.includes('all')}
                        onCheckedChange={(checked) => handleAttendeeChange('all', !!checked)}
                      />
                      <Label htmlFor="attendees-all">Everyone</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="attendees-students" 
                        checked={selectedAttendees.includes('students')}
                        onCheckedChange={(checked) => handleAttendeeChange('students', !!checked)}
                        disabled={selectedAttendees.includes('all')}
                      />
                      <Label htmlFor="attendees-students">Students</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="attendees-teachers" 
                        checked={selectedAttendees.includes('teachers')}
                        onCheckedChange={(checked) => handleAttendeeChange('teachers', !!checked)}
                        disabled={selectedAttendees.includes('all')}
                      />
                      <Label htmlFor="attendees-teachers">Teachers</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="attendees-parents" 
                        checked={selectedAttendees.includes('parents')}
                        onCheckedChange={(checked) => handleAttendeeChange('parents', !!checked)}
                        disabled={selectedAttendees.includes('all')}
                      />
                      <Label htmlFor="attendees-parents">Parents</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="attendees-staff" 
                        checked={selectedAttendees.includes('staff')}
                        onCheckedChange={(checked) => handleAttendeeChange('staff', !!checked)}
                        disabled={selectedAttendees.includes('all')}
                      />
                      <Label htmlFor="attendees-staff">Staff</Label>
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" className="gradient-bg text-white hover:opacity-90">
                Add Event
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}