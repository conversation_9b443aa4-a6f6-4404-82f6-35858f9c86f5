import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronDown, 
  ChevronUp,
  LayoutDashboard,
  Building2,
  BarChart3,
  Settings,
  Users,
  BookOpen,
  UserCog,
  GraduationCap,
  UserPlus,
  School,
  ClipboardCheck,
  BellRing,
  Calendar,
  DollarSign,
  CreditCard,
  AlertTriangle,
  FileText
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { SUPER_ADMIN_NAV_ITEMS, HEADMASTER_NAV_ITEMS } from "@/lib/constants";
import { headmasterAPI } from "@/lib/api";

interface CollapsibleSidebarProps {
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  isMobile?: boolean;
  onClose?: () => void;
  schoolName?: string;
  expandAllSubmenus?: boolean;
}

// Map of icon names to components
const iconMap: Record<string, React.ComponentType<any>> = {
  LayoutDashboard,
  Building2,
  BarChart3,
  Settings,
  Users,
  BookOpen,
  UserCog,
  GraduationCap,
  UserPlus,
  School,
  ClipboardCheck,
  BellRing,
  Calendar,
  DollarSign,
  CreditCard,
  AlertTriangle,
  FileText
};

export function CollapsibleSidebar({
  isCollapsed = false,
  onToggleCollapse,
  isMobile = false,
  onClose,
  schoolName: propSchoolName,
  expandAllSubmenus = false,
}: CollapsibleSidebarProps) {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [openSubmenus, setOpenSubmenus] = useState<Record<string, boolean>>({});
  const [schoolName, setSchoolName] = useState(propSchoolName || "School Management System");

  // Fetch school name from dashboard API
  const fetchSchoolName = async () => {
    try {
      if (user?.role === "super_admin") {
        setSchoolName("School Manager");
        return;
      }
      
      const response = await headmasterAPI.getDashboardStats();
      if (response.success && response.data.stats.schoolName) {
        setSchoolName(response.data.stats.schoolName);
      }
    } catch (error) {
      console.error('Error fetching school name:', error);
    }
  };

  useEffect(() => {
    if (user && !propSchoolName) {
      fetchSchoolName();
    } else if (propSchoolName) {
      setSchoolName(propSchoolName);
    }
  }, [user, propSchoolName]);

  const isSuperAdmin = user?.role === "super_admin";
  const navItems = isSuperAdmin ? SUPER_ADMIN_NAV_ITEMS : HEADMASTER_NAV_ITEMS;

  const toggleSubmenu = (path: string) => {
    setOpenSubmenus((prev) => ({
      ...prev,
      [path]: !prev[path],
    }));
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    if (isMobile && onClose) {
      onClose();
    }
  };

  // Function to render icon safely
  const renderIcon = (iconName: string, className: string) => {
    const IconComponent = iconMap[iconName];
    if (IconComponent) {
      return <IconComponent className={className} />;
    }
    return <Building2 className={className} />; // Fallback icon
  };

  return (
    <div
      className={cn(
        "h-full bg-white border-r border-slate-200 flex flex-col transition-all duration-300 fixed shadow-sm",
        isCollapsed ? "w-[70px]" : "w-[260px]"
      )}
    >
      {/* Enhanced Sidebar Header */}
      <div className="h-16 border-b border-slate-200 flex items-center justify-between px-4 bg-gradient-to-r from-blue-50/50 to-purple-50/50">
        <div className="flex items-center gap-3 overflow-hidden">
          <div className="flex items-center justify-center h-9 w-9 rounded-lg bg-gradient-to-br from-blue-600 to-purple-600 shadow-md">
            <span className="text-white font-bold text-sm">{schoolName.charAt(0)}</span>
          </div>
          {!isCollapsed && (
            <div>
              <span className="font-bold text-slate-800 text-sm truncate block">{schoolName}</span>
              <span className="text-xs text-slate-500">Management Portal</span>
            </div>
          )}
        </div>
        {!isMobile && onToggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded-lg"
            onClick={onToggleCollapse}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {/* Enhanced Navigation Items */}
      <div className="flex-1 overflow-y-auto py-4 px-3">
        <nav className="space-y-1">
          {navItems.map((item) => {
            const hasSubItems = item.subItems && item.subItems.length > 0;
            const isSubmenuOpen = expandAllSubmenus || openSubmenus[item.path];
            const isItemActive = isActive(item.path) || 
              (hasSubItems && item.subItems?.some(subItem => isActive(subItem.path)));

            return (
              <div key={item.path} className="mb-1">
                <div
                  className={cn(
                    "flex items-center justify-between px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200",
                    isItemActive
                      ? "bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border border-blue-200/50 shadow-sm"
                      : "text-slate-600 hover:bg-slate-50 hover:text-slate-800"
                  )}
                  onClick={() => {
                    if (hasSubItems) {
                      toggleSubmenu(item.path);
                    } else {
                      handleNavigation(item.path);
                    }
                  }}
                >
                  <div className="flex items-center">
                    <div className={cn(
                      "flex items-center justify-center p-1.5 rounded-md",
                      isItemActive ? "text-blue-600" : "text-slate-500"
                    )}>
                      {renderIcon(item.icon, "h-5 w-5")}
                    </div>
                    {!isCollapsed && (
                      <span className="ml-3 text-sm font-semibold">{item.name}</span>
                    )}
                  </div>
                  {!isCollapsed && hasSubItems && (
                    <div className={cn(
                      "p-1 rounded-md",
                      isItemActive ? "text-blue-600" : "text-slate-400"
                    )}>
                      {isSubmenuOpen ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  )}
                </div>

                {/* Enhanced Submenu */}
                {!isCollapsed && hasSubItems && isSubmenuOpen && (
                  <div className="mt-2 ml-6 space-y-1 border-l-2 border-slate-100 pl-3">
                    {item.subItems?.map((subItem) => (
                      <div
                        key={subItem.path}
                        className={cn(
                          "flex items-center px-3 py-2 rounded-lg text-sm cursor-pointer transition-all duration-200",
                          isActive(subItem.path)
                            ? "bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border border-blue-200/50"
                            : "text-slate-600 hover:bg-slate-50 hover:text-slate-800"
                        )}
                        onClick={() => handleNavigation(subItem.path)}
                      >
                        <div className={cn(
                          "flex items-center justify-center p-1 rounded-md",
                          isActive(subItem.path) ? "text-blue-600" : "text-slate-500"
                        )}>
                          {renderIcon(subItem.icon, "h-4 w-4")}
                        </div>
                        <span className="ml-3 font-medium">{subItem.name}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </nav>
      </div>
    </div>
  );
}