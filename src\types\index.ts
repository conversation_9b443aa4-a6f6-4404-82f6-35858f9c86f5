// API request/response types
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

export interface RequestParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  class?: string;
}

// Value enums
export const SubjectTypes = [
  "Mathematics",
  "English",
  "Science",
  "History",
  "Geography",
  "Physics",
  "Chemistry",
  "Biology",
  "Computer Science",
  "Art",
  "Music",
  "Physical Education",
  "Foreign Languages",
  "Social Studies",
  "Economics",
] as const;

export type SubjectType = (typeof SubjectTypes)[number];

export const PHONE_PATTERN = /^\+?[1-9]\d{8,14}$/;

// Entity types
export interface User {
  id: string;
  name: string;
  email: string;
  role: "super_admin" | "headmaster" | "teacher" | "student" | "parent";
  status: "active" | "inactive";
  school?: any;
}

export interface Class {
  id: string;
  name: string;
  grade: number;
  section: string;
  teacher: string;
  studentCount: number;
  capacity: number;
  academicYear: number;
  active: boolean;
  classTeacher: string;
  students: number;
  boys: number;
  girls: number;
  room: string;
  averageAttendance: number;
  averageGrade: string;
  status: "active" | "inactive";
}

export interface Teacher {
  id: string;
  name: string;
  email: string;
  phone: string;
  subject: SubjectType;
  joinDate: string;
  status: "active" | "inactive";
}

export interface Student {
  id: string;
  name: string;
  email: string;
  class: string;
  parent: string;
  joinDate: string;
  status: "active" | "inactive";
}

export interface Parent {
  id: string;
  name: string;
  email: string;
  phone: string;
  children: string[];
  occupation: string;
  status: "active" | "inactive";
}

export interface Course {
  id: string;
  name: string;
  code: string;
  teacher: string;
  department: string;
  credits: number;
  studentCount: number;
  capacity: number;
  schedule: string;
  status: "active" | "inactive";
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  priority: "high" | "medium" | "low";
  author: string;
  date: string;
}

export interface Fee {
  id: string;
  studentId: string;
  studentName: string;
  type: "TUITION" | "LIBRARY" | "LABORATORY" | "SPORTS" | "TRANSPORT" | "EXAM" | "OTHER";
  amount: number;
  description?: string;
  dueDate: string;
  status: "PENDING" | "PAID" | "OVERDUE" | "PARTIAL";
  academicYear: string;
  term: string;
  createdAt: string;
  updatedAt: string;
}

export interface Payment {
  id: string;
  studentId: string;
  feeId: string;
  amountPaid: number;
  paymentMode: "CASH" | "BANK_TRANSFER" | "CHEQUE" | "CARD" | "MOBILE_MONEY" | "OTHER";
  paymentDate: string;
  paidBy: string;
  payerPhone?: string;
  receiptId?: string;
  notes?: string;
  referenceNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ArrearsRecord {
  id: string;
  studentName: string;
  studentClass: string;
  feeType: string;
  totalAmount: number;
  amountPaid: number;
  arrears: number;
  dueDate: string;
  daysOverdue: number;
  academicYear: string;
  term: string;
}

export interface FeeSummary {
  totalExpected: number;
  totalCollected: number;
  totalOutstanding: number;
  collectionRate: number;
  monthlyCollections: Array<{
    month: string;
    amount: number;
  }>;
}

export interface ArrearsSummary {
  totalArrears: number;
  totalStudentsWithArrears: number;
  totalOutstandingAmount: number;
  totalAmountPaid: number;
  overdueAmount: number;
  overdueCount: number;
}

export interface DashboardStats {
  schoolName: string;
  totalStudents: number;
  totalTeachers: number;
  totalClasses: number;
  totalSubjects: number;
  attendanceStats: {
    total: number;
    present: number;
    absent: number;
    late: number;
    excused: number;
  };
  recentAnnouncements: Announcement[];
}

// Form Data types
export interface ClassFormData {
  name: string;
  classTeacher: string;
  grade: string;
  section: string;
  capacity: string;
}

export interface TeacherFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  subjectIds: string[]; // Array of subject IDs
  classId?: string; // Primary class assignment
  password?: string;
  status?: "active" | "inactive";
}

export interface StudentFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  classId: string;
  parentId: string;
  password?: string;
  status?: "active" | "inactive";
}

// Dropdown option types
export interface DropdownOption {
  id: string;
  name: string;
  displayName?: string;
}

export interface SubjectOption extends DropdownOption {
  code: string;
}

export interface ClassOption extends DropdownOption {
  grade: string;
  section: string;
}

export interface ParentOption extends DropdownOption {
  email: string;
}

export interface StudentOption extends DropdownOption {
  email: string;
  class?: {
    id: string;
    name: string;
    displayName: string;
  } | null;
}

export interface ParentFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  children: string;
  occupation: string;
  status?: "active" | "inactive";
}

export interface CourseFormData {
  name: string;
  code: string;
  teacher: string;
  department: string;
  credits: string;
  schedule: string;
}

export interface FeeFormData {
  studentId: string;
  type: string;
  amount: string;
  description?: string;
  dueDate: string;
  academicYear?: string;
  term?: string;
  status?: string;
}

export interface PaymentFormData {
  studentId: string;
  feeId: string;
  amountPaid: string;
  paymentMode: string;
  paymentDate: string;
  paidBy: string;
  payerPhone?: string;
  receiptId?: string;
  notes?: string;
  referenceNumber?: string;
}

// Common types used across components
export interface SortConfig<T> {
  key: keyof T;
  direction: "asc" | "desc";
}
