import { useState, useEffect, useCallback } from "react";
import { headmaster<PERSON><PERSON> } from "@/lib/api";
import { showToast } from "@/lib/utils";
import type {
  DashboardStats,
  Announcement,
  Teacher,
  Student,
  Class,
  Parent,
  Course,
  TeacherFormData,
  StudentFormData,
  ParentFormData,
  ClassFormData,
  CourseFormData,
} from "@/types";

interface UseHeadmasterDashboardReturn {
  // State
  stats: DashboardStats | null;
  announcements: Announcement[];
  loading: boolean;
  error: string | null;

  // Actions
  fetchDashboardData: () => Promise<void>;
  createAnnouncement: (data: any) => Promise<void>;
  createTeacher: (data: TeacherFormData) => Promise<void>;
  createStudent: (data: StudentFormData) => Promise<void>;
  createParent: (data: ParentFormData) => Promise<void>;
  createClass: (data: ClassFormData) => Promise<void>;
  createCourse: (data: CourseFormData) => Promise<void>;
}

export const useHeadmasterDashboard = (): UseHeadmasterDashboardReturn => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const [statsResponse, announcementsResponse] = await Promise.all([
        headmasterAPI.getDashboardStats(),
        headmasterAPI.getAnnouncements({ limit: 5 }),
      ]);

      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

      if (announcementsResponse.success) {
        setAnnouncements(announcementsResponse.data || []);
      }
    } catch (err) {
      console.error("Failed to fetch dashboard data:", err);
      setError("Failed to load dashboard data");
      showToast("Failed to load dashboard data", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const createAnnouncement = useCallback(async (data: any) => {
    try {
      const response = await headmasterAPI.createAnnouncement(data);
      if (response.success) {
        showToast("Announcement created successfully", "success");
        // Refresh announcements
        const announcementsResponse = await headmasterAPI.getAnnouncements({ limit: 5 });
        if (announcementsResponse.success) {
          setAnnouncements(announcementsResponse.data || []);
        }
      }
    } catch (err) {
      console.error("Failed to create announcement:", err);
      showToast("Failed to create announcement", "error");
      throw err;
    }
  }, []);

  const createTeacher = useCallback(
    async (data: TeacherFormData) => {
      try {
        const response = await headmasterAPI.createTeacher(data);
        if (response.success) {
          showToast("Teacher created successfully", "success");
          // Refresh stats to update teacher count
          fetchDashboardData();
        }
      } catch (err) {
        console.error("Failed to create teacher:", err);
        showToast("Failed to create teacher", "error");
        throw err;
      }
    },
    [fetchDashboardData]
  );

  const createStudent = useCallback(
    async (data: StudentFormData) => {
      try {
        const response = await headmasterAPI.createStudent(data);
        if (response.success) {
          showToast("Student created successfully", "success");
          // Refresh stats to update student count
          fetchDashboardData();
        }
      } catch (err) {
        console.error("Failed to create student:", err);
        showToast("Failed to create student", "error");
        throw err;
      }
    },
    [fetchDashboardData]
  );

  const createParent = useCallback(async (data: ParentFormData) => {
    try {
      // Send firstName and lastName directly as the backend expects
      const parentData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        // Note: occupation is not stored in the backend user table
        // password will use default if not provided
      };

      const response = await headmasterAPI.createParent(parentData);
      if (response.success) {
        showToast("Parent created successfully", "success");
      }
    } catch (err) {
      console.error("Failed to create parent:", err);
      showToast("Failed to create parent", "error");
      throw err;
    }
  }, []);

  const createClass = useCallback(
    async (data: ClassFormData) => {
      try {
        const response = await headmasterAPI.createClass(data);
        if (response.success) {
          showToast("Class created successfully", "success");
          // Refresh stats to update class count
          fetchDashboardData();
        }
      } catch (err) {
        console.error("Failed to create class:", err);
        showToast("Failed to create class", "error");
        throw err;
      }
    },
    [fetchDashboardData]
  );

  const createCourse = useCallback(
    async (data: CourseFormData) => {
      try {
        const response = await headmasterAPI.createCourse(data);
        if (response.success) {
          showToast("Course created successfully", "success");
          // Refresh stats to update subject count
          fetchDashboardData();
        }
      } catch (err) {
        console.error("Failed to create course:", err);
        showToast("Failed to create course", "error");
        throw err;
      }
    },
    [fetchDashboardData]
  );

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    stats,
    announcements,
    loading,
    error,
    fetchDashboardData,
    createAnnouncement,
    createTeacher,
    createStudent,
    createParent,
    createClass,
    createCourse,
  };
};
