import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { 
  Search,
  Users,
  FileText,
  Award,
  Bell,
  Download,
  Loader2,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { format } from 'date-fns';
import { NotifyReportsModal } from '@/components/modals/NotifyReportsModal';
import { ReportRemarksModal } from '@/components/modals/ReportRemarksModal';

interface StudentReport {
  id: string;
  studentName: string;
  studentClass: string;
  subjects: {
    [key: string]: {
      classScore: number;
      examScore: number;
      totalScore: number;
      grade: string;
    };
  };
  overallAverage: number;
  overallGrade: string;
  position: number;
}

export function ReportsPage() {
  const [loading, setLoading] = useState(true);
  const [reports, setReports] = useState<StudentReport[]>([]);
  const [filteredReports, setFilteredReports] = useState<StudentReport[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTerm, setSelectedTerm] = useState('first');
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedYear, setSelectedYear] = useState('2024/2025');
  const [isNotifyModalOpen, setIsNotifyModalOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [isRemarksModalOpen, setIsRemarksModalOpen] = useState(false);
  const [remarkRules, setRemarkRules] = useState([
    {
      id: '1',
      name: 'Excellent Performance',
      minAverage: 90,
      maxAverage: 100,
      remark: 'Outstanding academic performance. Keep up the excellent work!',
      classes: ['Primary 1', 'Primary 2', 'Primary 3'],
      advanced: { enabled: false, conditions: [], priority: 1 }
    },
    {
      id: '2',
      name: 'Good Performance',
      minAverage: 70,
      maxAverage: 89,
      remark: 'Good academic performance. Continue to strive for excellence.',
      classes: ['All Classes'],
      advanced: { enabled: true, conditions: ['attendance'], priority: 2 }
    }
  ]);

  useEffect(() => {
    fetchClasses();
    fetchReports();
  }, [selectedTerm, selectedClass, selectedYear]);

  useEffect(() => {
    filterReports();
  }, [reports, searchQuery, pagination.page]);

  const fetchClasses = async () => {
    try {
      const response = await headmasterAPI.getClasses();
      if (response.success) {
        setClasses(response.data.classes);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchReports = async () => {
    try {
      setLoading(true);
      // Mock data
      const mockReports: StudentReport[] = [
        {
          id: '1',
          studentName: 'John Smith',
          studentClass: 'Class 10A',
          subjects: {
            'Mathematics': { classScore: 42, examScore: 85, totalScore: 84.7, grade: 'B', position: 5, remark: 'Good performance' },
            'English': { classScore: 45, examScore: 78, totalScore: 81.6, grade: 'B', position: 3, remark: 'Well done' },
            'Science': { classScore: 38, examScore: 88, totalScore: 83.2, grade: 'B', position: 4, remark: 'Good effort' },
            'History': { classScore: 40, examScore: 75, totalScore: 79.5, grade: 'B', position: 6, remark: 'Satisfactory' }
          },
          totalScore: 329.0,
          overallAverage: 82.3,
          overallGrade: 'B',
          position: 3
        },
        {
          id: '2',
          studentName: 'Sarah Johnson',
          studentClass: 'Class 9B',
          subjects: {
            'Mathematics': { classScore: 48, examScore: 92, totalScore: 92.8, grade: 'A', position: 1, remark: 'Excellent work' },
            'English': { classScore: 46, examScore: 89, totalScore: 89.8, grade: 'A', position: 2, remark: 'Outstanding' },
            'Science': { classScore: 44, examScore: 85, totalScore: 86.2, grade: 'A', position: 1, remark: 'Excellent' },
            'History': { classScore: 47, examScore: 88, totalScore: 90.2, grade: 'A', position: 1, remark: 'Superb performance' }
          },
          totalScore: 359.0,
          overallAverage: 89.8,
          overallGrade: 'A',
          position: 1
        },
        {
          id: '3',
          studentName: 'Michael Brown',
          studentClass: 'Class 11C',
          subjects: {
            'Mathematics': { classScore: 35, examScore: 72, totalScore: 71.4, grade: 'B', position: 8, remark: 'Room for improvement' },
            'English': { classScore: 38, examScore: 68, totalScore: 70.4, grade: 'B', position: 9, remark: 'Fair performance' },
            'Science': { classScore: 32, examScore: 75, totalScore: 71.8, grade: 'B', position: 7, remark: 'Good effort' },
            'History': { classScore: 36, examScore: 70, totalScore: 70.8, grade: 'B', position: 8, remark: 'Satisfactory' }
          },
          totalScore: 284.4,
          overallAverage: 71.1,
          overallGrade: 'B',
          position: 8
        }
      ];
      
      setReports(mockReports);
    } catch (error) {
      console.error('Error fetching reports:', error);
      showToast.error('Error', 'Failed to load student reports');
    } finally {
      setLoading(false);
    }
  };

  const filterReports = () => {
    let filtered = reports;

    if (searchQuery) {
      filtered = filtered.filter(report =>
        report.studentName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setPagination(prev => ({ ...prev, total: filtered.length }));
    
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    const paginatedReports = filtered.slice(startIndex, endIndex);
    
    setFilteredReports(paginatedReports);
  };

  const handleNotifyParents = (data: any) => {
    showToast.success('Success', `Report notifications sent to parents of ${data.selectedClass} students`);
    setIsNotifyModalOpen(false);
  };

  const handleSaveRemarkRules = (data: any) => {
    setRemarkRules(data.remarkRules);
    setIsRemarksModalOpen(false);
    showToast.success('Success', 'Report remarks configuration saved successfully');
  };

  const toggleRowExpansion = (reportId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(reportId)) {
      newExpanded.delete(reportId);
    } else {
      newExpanded.add(reportId);
    }
    setExpandedRows(newExpanded);
  };

  const handleDownloadPDF = () => {
    const htmlContent = `
      <div style="font-family: Arial, sans-serif;">
        ${filteredReports.map((report, index) => `
          <div style="${index > 0 ? 'page-break-before: always;' : ''} min-height: 100vh; padding: 40px; position: relative;">
            <!-- Header -->
            <div style="text-align: center; border-bottom: 3px solid #333; padding-bottom: 20px; margin-bottom: 30px;">
              <h1 style="color: #333; margin: 0; font-size: 28px;">GREENFIELD ACADEMY</h1>
              <p style="margin: 5px 0; color: #666; font-size: 14px;">Excellence in Education</p>
              <h2 style="color: #555; margin: 10px 0; font-size: 20px;">ACADEMIC REPORT CARD</h2>
            </div>
            
            <!-- Student Info -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
              <div>
                <p style="margin: 8px 0;"><strong>Student Name:</strong> ${report.studentName}</p>
                <p style="margin: 8px 0;"><strong>Class:</strong> ${report.studentClass}</p>
                <p style="margin: 8px 0;"><strong>Academic Year:</strong> ${selectedYear}</p>
              </div>
              <div>
                <p style="margin: 8px 0;"><strong>Term:</strong> ${selectedTerm.charAt(0).toUpperCase() + selectedTerm.slice(1)} Term</p>
                <p style="margin: 8px 0;"><strong>Position:</strong> ${report.position}</p>
                <p style="margin: 8px 0;"><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
              </div>
            </div>
            
            <!-- Subjects Table -->
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
              <thead>
                <tr style="background-color: #f8f9fa;">
                  <th style="border: 2px solid #333; padding: 12px; text-align: left; font-weight: bold;">Subject</th>
                  <th style="border: 2px solid #333; padding: 12px; text-align: center; font-weight: bold;">Class Score<br/>(50)</th>
                  <th style="border: 2px solid #333; padding: 12px; text-align: center; font-weight: bold;">Exam Score<br/>(100)</th>
                  <th style="border: 2px solid #333; padding: 12px; text-align: center; font-weight: bold;">Total<br/>(%)</th>
                  <th style="border: 2px solid #333; padding: 12px; text-align: center; font-weight: bold;">Grade</th>
                  <th style="border: 2px solid #333; padding: 12px; text-align: center; font-weight: bold;">Position</th>
                  <th style="border: 2px solid #333; padding: 12px; text-align: left; font-weight: bold;">Remark</th>
                </tr>
              </thead>
              <tbody>
                ${Object.entries(report.subjects).map(([subject, scores]) => `
                  <tr>
                    <td style="border: 1px solid #333; padding: 10px; font-weight: bold;">${subject}</td>
                    <td style="border: 1px solid #333; padding: 10px; text-align: center;">${scores.classScore}</td>
                    <td style="border: 1px solid #333; padding: 10px; text-align: center;">${scores.examScore}</td>
                    <td style="border: 1px solid #333; padding: 10px; text-align: center; font-weight: bold;">${scores.totalScore.toFixed(1)}</td>
                    <td style="border: 1px solid #333; padding: 10px; text-align: center; font-weight: bold; color: ${scores.grade === 'A' ? '#16a34a' : scores.grade === 'B' ? '#2563eb' : '#ea580c'};">${scores.grade}</td>
                    <td style="border: 1px solid #333; padding: 10px; text-align: center;">${scores.position}</td>
                    <td style="border: 1px solid #333; padding: 10px;">${scores.remark}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
            
            <!-- Summary -->
            <div style="background-color: #f8f9fa; padding: 20px; border: 2px solid #333; margin-bottom: 30px;">
              <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; text-align: center;">
                <div>
                  <p style="margin: 0; font-size: 14px; color: #666;">Total Score</p>
                  <p style="margin: 5px 0; font-size: 24px; font-weight: bold; color: #333;">${report.totalScore.toFixed(1)}</p>
                </div>
                <div>
                  <p style="margin: 0; font-size: 14px; color: #666;">Average</p>
                  <p style="margin: 5px 0; font-size: 24px; font-weight: bold; color: #333;">${report.overallAverage.toFixed(1)}%</p>
                </div>
                <div>
                  <p style="margin: 0; font-size: 14px; color: #666;">Overall Grade</p>
                  <p style="margin: 5px 0; font-size: 24px; font-weight: bold; color: ${report.overallGrade === 'A' ? '#16a34a' : report.overallGrade === 'B' ? '#2563eb' : '#ea580c'};">${report.overallGrade}</p>
                </div>
              </div>
            </div>
            
            <!-- Footer -->
            <div style="position: absolute; bottom: 40px; left: 40px; right: 40px; border-top: 2px solid #333; padding-top: 20px;">
              <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 30px; text-align: center;">
                <div>
                  <p style="margin: 0; border-top: 1px solid #333; padding-top: 5px; font-size: 12px;">Class Teacher</p>
                </div>
                <div>
                  <p style="margin: 0; border-top: 1px solid #333; padding-top: 5px; font-size: 12px;">Headmaster</p>
                </div>
                <div>
                  <p style="margin: 0; border-top: 1px solid #333; padding-top: 5px; font-size: 12px;">Parent/Guardian</p>
                </div>
              </div>
              <p style="text-align: center; margin: 20px 0 0 0; font-size: 10px; color: #666;">This report is computer generated and does not require signature</p>
            </div>
          </div>
        `).join('')}
      </div>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Academic Report</title>
            <style>
              @page { margin: 20px; }
              body { margin: 0; font-size: 12px; }
            </style>
          </head>
          <body>
            ${htmlContent}
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 100);
              }
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
    
    showToast.success('PDF Ready', 'Use the print dialog to save as PDF');
  };

  // Calculate summary statistics
  const allFilteredReports = reports.filter(report => {
    if (!searchQuery) return true;
    return report.studentName.toLowerCase().includes(searchQuery.toLowerCase());
  });
  
  const totalStudents = allFilteredReports.length;
  const averageScore = totalStudents > 0 ? allFilteredReports.reduce((sum, r) => sum + r.overallAverage, 0) / totalStudents : 0;
  const topPerformers = allFilteredReports.filter(r => r.overallGrade === 'A').length;
  const passRate = allFilteredReports.filter(r => r.overallAverage >= 50).length;

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Academic Reports</h1>
                <p className="text-purple-200 mt-1">Student performance and results</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={handleDownloadPDF}
              variant="outline"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Download className="h-4 w-4 mr-2" />
              <span>Download PDF</span>
            </Button>
            <Button
              onClick={() => setIsNotifyModalOpen(true)}
              className="bg-white text-purple-900 hover:bg-purple-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Bell className="h-4 w-4 mr-2" />
              <span>Notify Parents</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Report Remarks Section */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl mb-8">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">Report Remarks</CardTitle>
                <p className="text-sm text-slate-600">Configure automatic remarks for report cards</p>
              </div>
            </div>
            <Button onClick={() => setIsRemarksModalOpen(true)} variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Configure Remarks
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {remarkRules.map((rule) => (
              <div key={rule.id} className="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold text-slate-900">{rule.name}</h4>
                  {rule.advanced.enabled && (
                    <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                      Advanced
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-slate-600 mb-3">{rule.remark}</p>
                <div className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <span className="text-slate-500">Range:</span>
                    <span className="font-medium">{rule.minAverage}% - {rule.maxAverage}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-500">Classes:</span>
                    <span className="font-medium">{rule.classes.length} selected</span>
                  </div>
                </div>
              </div>
            ))}
            {remarkRules.length === 0 && (
              <div className="col-span-full text-center py-8 text-slate-500">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No remark rules configured</p>
                <p className="text-sm">Click "Configure Remarks" to add automatic remarks</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Total Students</p>
              <p className="text-3xl font-bold text-blue-900">{totalStudents}</p>
            </div>
            <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-green-100/50 border-green-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-700">Average Score</p>
              <p className="text-3xl font-bold text-green-900">{averageScore.toFixed(1)}%</p>
            </div>
            <div className="bg-green-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Award className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700">Top Performers</p>
              <p className="text-3xl font-bold text-purple-900">{topPerformers}</p>
            </div>
            <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Award className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-700">Pass Rate</p>
              <p className="text-3xl font-bold text-orange-900">{passRate}/{totalStudents}</p>
            </div>
            <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <FileText className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">Student Reports</CardTitle>
                <p className="text-sm text-slate-600">Academic performance by subject</p>
              </div>
            </div>
          </div>
        </CardHeader>
        
        {/* Filters */}
        <div className="p-6 border-b border-slate-100/50 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search by student name..."
              className="pl-10 border-slate-200 focus:border-purple-500 focus:ring-purple-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Select value={selectedYear} onValueChange={setSelectedYear}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Academic Year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2024/2025">2024/2025</SelectItem>
              <SelectItem value="2023/2024">2023/2024</SelectItem>
              <SelectItem value="2022/2023">2022/2023</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedTerm} onValueChange={setSelectedTerm}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Select Term" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="first">First Term</SelectItem>
              <SelectItem value="second">Second Term</SelectItem>
              <SelectItem value="third">Third Term</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Select Class" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Classes</SelectItem>
              {classes.map((cls) => (
                <SelectItem key={cls.id} value={cls.id}>
                  {cls.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Reports Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead>Student Name</TableHead>
                <TableHead>Class</TableHead>
                <TableHead>Total Score</TableHead>
                <TableHead>Overall Average</TableHead>
                <TableHead>Position</TableHead>
                <TableHead>Details</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading reports...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredReports.length > 0 ? (
                filteredReports.map((report) => (
                  <React.Fragment key={report.id}>
                    <TableRow className="hover:bg-slate-50/50 transition-colors">
                      <TableCell>
                        <div className="font-semibold text-slate-900">{report.studentName}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
                          {report.studentClass}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-bold text-blue-700 text-center">
                          {report.totalScore.toFixed(1)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-bold text-blue-700 text-center">
                          {report.overallAverage.toFixed(1)}%
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="font-bold text-purple-700 text-center">
                          {report.position}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleRowExpansion(report.id)}
                          className="flex items-center gap-1"
                        >
                          {expandedRows.has(report.id) ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                          <span>Details</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                    {expandedRows.has(report.id) && (
                      <TableRow>
                        <TableCell colSpan={6} className="p-4">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Subject</TableHead>
                                <TableHead>Class Score</TableHead>
                                <TableHead>Exam Score</TableHead>
                                <TableHead>Total</TableHead>
                                <TableHead>Position</TableHead>
                                <TableHead>Grade</TableHead>
                                <TableHead>Remarks</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {Object.entries(report.subjects).map(([subject, scores]) => (
                                <TableRow key={subject}>
                                  <TableCell className="font-medium">{subject}</TableCell>
                                  <TableCell>{scores.classScore}/50</TableCell>
                                  <TableCell>{scores.examScore}/100</TableCell>
                                  <TableCell className="font-bold">{scores.totalScore.toFixed(1)}%</TableCell>
                                  <TableCell>{scores.position}</TableCell>
                                  <TableCell>
                                    <Badge variant="outline" className="text-xs">
                                      {scores.grade}
                                    </Badge>
                                  </TableCell>
                                  <TableCell className="text-sm">{scores.remark}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    No student reports found matching your criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Pagination */}
        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{Math.min((pagination.page - 1) * pagination.limit + 1, pagination.total)}</span> to{" "}
            <span className="font-medium">{Math.min(pagination.page * pagination.limit, pagination.total)}</span> of{" "}
            <span className="font-medium">{pagination.total}</span> reports
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page * pagination.limit >= pagination.total}
            >
              Next
            </Button>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <NotifyReportsModal 
        open={isNotifyModalOpen}
        onClose={() => setIsNotifyModalOpen(false)}
        onSubmit={handleNotifyParents}
        classes={classes}
      />
      
      <ReportRemarksModal
        open={isRemarksModalOpen}
        onClose={() => setIsRemarksModalOpen(false)}
        onSubmit={handleSaveRemarkRules}
        remarkRules={remarkRules}
      />
    </Layout>
  );
}