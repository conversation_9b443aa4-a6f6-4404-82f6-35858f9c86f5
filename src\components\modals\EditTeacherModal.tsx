import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import { SubjectTypes } from '@/types';
import { headmasterAPI } from '@/lib/api';

const teacherSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  phone: z.string().min(10, { message: 'Please enter a valid phone number' }),
  subjects: z.array(z.string()).min(1, { message: 'Please select at least one subject' }),
  classes: z.array(z.string()).min(1, { message: 'Please select at least one class' }),
  status: z.enum(['active', 'inactive'])
});

export type EditTeacherFormData = z.infer<typeof teacherSchema>;

interface EditTeacherModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: EditTeacherFormData) => void;
  teacher: any;
}

export function EditTeacherModal({ open, onClose, onSubmit, teacher }: EditTeacherModalProps) {
  const [availableClasses, setAvailableClasses] = useState<any[]>([]);
  const [selectedClasses, setSelectedClasses] = useState<string[]>([]);
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);
  
  const form = useForm<EditTeacherFormData>({
    resolver: zodResolver(teacherSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      subjects: [],
      classes: [],
      status: 'active'
    },
  });

  useEffect(() => {
    if (open) {
      fetchClasses();
    }
  }, [open]);

  const fetchClasses = async () => {
    try {
      const response = await headmasterAPI.getClasses();
      if (response.success) {
        setAvailableClasses(response.data.classes);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  // Update form when teacher changes
  useEffect(() => {
    if (teacher) {
      const teacherSubjects = Array.isArray(teacher.subjects) ? teacher.subjects : [teacher.subject];
      const teacherClasses = teacher.classes || [];
      
      setSelectedSubjects(teacherSubjects);
      setSelectedClasses(teacherClasses.map((cls: any) => typeof cls === 'string' ? cls : cls.id));
      
      form.reset({
        name: teacher.name,
        email: teacher.email,
        phone: teacher.phone,
        subjects: teacherSubjects,
        classes: teacherClasses,
        status: teacher.status as 'active' | 'inactive'
      });
    }
  }, [teacher, form]);

  const handleSubmit = () => {
    // Validate selections
    if (selectedSubjects.length === 0) {
      return;
    }
    if (selectedClasses.length === 0) {
      return;
    }
    
    const formData = form.getValues();
    const submitData = {
      ...formData,
      subjects: selectedSubjects,
      classes: selectedClasses,
    };
    onSubmit(submitData);
    onClose();
  };

  const handleSubjectToggle = (subject: string) => {
    setSelectedSubjects(prev => 
      prev.includes(subject) 
        ? prev.filter(s => s !== subject)
        : [...prev, subject]
    );
  };

  const handleClassToggle = (classId: string) => {
    setSelectedClasses(prev => 
      prev.includes(classId) 
        ? prev.filter(id => id !== classId)
        : [...prev, classId]
    );
  };

  const removeSelectedSubject = (subject: string) => {
    setSelectedSubjects(prev => prev.filter(s => s !== subject));
  };

  const removeSelectedClass = (classId: string) => {
    setSelectedClasses(prev => prev.filter(id => id !== classId));
  };

  const subjects = [
    'Mathematics',
    'English',
    'Science',
    'History',
    'Geography',
    'Physics',
    'Chemistry',
    'Biology',
    'Computer Science',
    'Art',
    'Music',
    'Physical Education',
    'Foreign Languages',
    'Social Studies',
    'Economics'
  ];

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Teacher</DialogTitle>
          <DialogDescription>
            Update the teacher's information.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input placeholder="John Smith" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input placeholder="+****************" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="space-y-3">
              <FormLabel>Subjects to Teach *</FormLabel>
              
              {/* Selected Subjects Display */}
              {selectedSubjects.length > 0 && (
                <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-muted/20">
                  {selectedSubjects.map(subject => (
                    <Badge key={subject} variant="secondary" className="flex items-center gap-1">
                      {subject}
                      <X 
                        className="h-3 w-3 cursor-pointer hover:text-destructive" 
                        onClick={() => removeSelectedSubject(subject)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
              
              {/* Available Subjects */}
              <div className="max-h-32 overflow-y-auto border rounded-md p-2 space-y-2">
                {SubjectTypes.map(subject => (
                  <div key={subject} className="flex items-center space-x-2">
                    <Checkbox
                      id={`subject-${subject}`}
                      checked={selectedSubjects.includes(subject)}
                      onCheckedChange={() => handleSubjectToggle(subject)}
                    />
                    <label 
                      htmlFor={`subject-${subject}`} 
                      className="text-sm cursor-pointer flex-1"
                    >
                      {subject}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="space-y-3">
              <FormLabel>Classes to Teach *</FormLabel>
              
              {/* Selected Classes Display */}
              {selectedClasses.length > 0 && (
                <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-muted/20">
                  {selectedClasses.map(classId => {
                    const classInfo = availableClasses.find(c => c.id === classId);
                    return (
                      <Badge key={classId} variant="secondary" className="flex items-center gap-1">
                        {classInfo?.name}
                        <X 
                          className="h-3 w-3 cursor-pointer hover:text-destructive" 
                          onClick={() => removeSelectedClass(classId)}
                        />
                      </Badge>
                    );
                  })}
                </div>
              )}
              
              {/* Available Classes */}
              <div className="max-h-32 overflow-y-auto border rounded-md p-2 space-y-2">
                {availableClasses.length > 0 ? (
                  availableClasses.map(cls => (
                    <div key={cls.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`class-${cls.id}`}
                        checked={selectedClasses.includes(cls.id)}
                        onCheckedChange={() => handleClassToggle(cls.id)}
                      />
                      <label 
                        htmlFor={`class-${cls.id}`} 
                        className="text-sm cursor-pointer flex-1"
                      >
                        {cls.name} ({cls.grade} {cls.section})
                      </label>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">No classes available</p>
                )}
              </div>
            </div>
            
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Active Status</FormLabel>
                    <p className="text-sm text-muted-foreground">
                      Set whether this teacher is currently active
                    </p>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value === 'active'}
                      onCheckedChange={(checked) => field.onChange(checked ? 'active' : 'inactive')}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" className="gradient-bg text-white hover:opacity-90">
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}