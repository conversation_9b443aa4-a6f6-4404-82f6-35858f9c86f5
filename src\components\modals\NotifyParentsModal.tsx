import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Send } from 'lucide-react';

interface NotifyParentsModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  classes: any[];
}

export function NotifyParentsModal({ open, onClose, onSubmit, classes }: NotifyParentsModalProps) {
  const [formData, setFormData] = useState({
    selectedClass: '',
    feeType: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    setFormData({
      selectedClass: '',
      feeType: '',
      message: ''
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Notify Parents
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="selectedClass">Select Class</Label>
            <Select value={formData.selectedClass} onValueChange={(value) => setFormData({...formData, selectedClass: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Choose class to notify" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Classes</SelectItem>
                {classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.name}>
                    {cls.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="feeType">Fee Type</Label>
            <Select value={formData.feeType} onValueChange={(value) => setFormData({...formData, feeType: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select fee type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Fee Types</SelectItem>
                <SelectItem value="Tuition Fee">Tuition Fee</SelectItem>
                <SelectItem value="School Fees">School Fees</SelectItem>
                <SelectItem value="Examination Fee">Examination Fee</SelectItem>
                <SelectItem value="Activity Fee">Activity Fee</SelectItem>
                <SelectItem value="Library Fee">Library Fee</SelectItem>
                <SelectItem value="Transport Fee">Transport Fee</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="message">Notification Message</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => setFormData({...formData, message: e.target.value})}
              placeholder="Enter message to send to parents..."
              className="min-h-[100px]"
              required
            />
          </div>
          
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-700">
              <strong>Note:</strong> This notification will be sent to parents of students in the selected class who have outstanding fees for the specified fee type.
            </p>
          </div>
          
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
              <Send className="h-4 w-4 mr-2" />
              Send Notifications
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}