import axios from "axios";
import type { ApiR<PERSON>ponse, <PERSON>, <PERSON>board<PERSON>tats, Teacher, TeacherFormData } from "@/types";

// Create axios instance with base URL
const api = axios.create({
  baseURL: "http://localhost:5001/api/v1",
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Important for cookies
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem("token");
      window.location.href = "/auth/login";
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email: string, password: string) => {
    const response = await api.post("/auth/login", { email, password });
    if (response.data.success && response.data.data.token) {
      localStorage.setItem("token", response.data.data.token);
    }
    return response.data;
  },
  register: async (userData: any) => {
    const response = await api.post("/auth/register", userData);
    return response.data;
  },
  getCurrentUser: async () => {
    const response = await api.get("/auth/me");
    return response.data;
  },
  logout: async () => {
    const response = await api.post("/auth/logout");
    localStorage.removeItem("token");
    return response.data;
  },
  updateDetails: async (userData: any) => {
    const response = await api.put("/auth/updatedetails", userData);
    return response.data;
  },
  updatePassword: async (passwordData: any) => {
    const response = await api.put("/auth/updatepassword", passwordData);
    return response.data;
  },
};

// SuperAdmin API
export const superAdminAPI = {
  // Schools
  getSchools: async (params = {}) => {
    const response = await api.get("/superadmin/schools", { params });
    return response.data;
  },
  getSchool: async (id: string) => {
    const response = await api.get(`/superadmin/schools/${id}`);
    return response.data;
  },
  createSchool: async (schoolData: any) => {
    const response = await api.post("/superadmin/schools", schoolData);
    return response.data;
  },
  updateSchool: async (id: string, schoolData: any) => {
    const response = await api.put(`/superadmin/schools/${id}`, schoolData);
    return response.data;
  },
  deleteSchool: async (id: string) => {
    const response = await api.delete(`/superadmin/schools/${id}`);
    return response.data;
  },

  // School Admins
  getAdmins: async (params = {}) => {
    const response = await api.get("/superadmin/admins", { params });
    return response.data;
  },
  getAdmin: async (id: string) => {
    const response = await api.get(`/superadmin/admins/${id}`);
    return response.data;
  },
  createAdmin: async (adminData: any) => {
    const response = await api.post("/superadmin/admins", adminData);
    return response.data;
  },
  updateAdmin: async (id: string, adminData: any) => {
    const response = await api.put(`/superadmin/admins/${id}`, adminData);
    return response.data;
  },
  deleteAdmin: async (id: string) => {
    const response = await api.delete(`/superadmin/admins/${id}`);
    return response.data;
  },

  // Stats
  getDashboardStats: async () => {
    const response = await api.get("/superadmin/stats/dashboard");
    return response.data;
  },
  getChartData: async () => {
    const response = await api.get("/superadmin/stats/charts");
    return response.data;
  },

  // Export
  exportSchools: async (params = {}) => {
    const response = await api.get("/superadmin/schools/export", {
      params,
      responseType: "text",
    });
    return response.data;
  },
};

// Headmaster API
export const headmasterAPI = {
  // Dashboard
  getDashboardStats: async (): Promise<ApiResponse<DashboardStats>> => {
    const response = await api.get("/headmaster/dashboard");
    return response.data;
  },

  // Teachers
  getTeachers: async (params: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<ApiResponse<{ data: Teacher[]; total: number }>> => {
    const response = await api.get("/headmaster/teachers", { params });
    return response.data;
  },
  createTeacher: async (teacherData: TeacherFormData): Promise<ApiResponse<Teacher>> => {
    const response = await api.post("/headmaster/teachers", teacherData);
    return response.data;
  },
  updateTeacher: async (id: string, teacherData: Partial<TeacherFormData>): Promise<ApiResponse<Teacher>> => {
    const response = await api.put(`/headmaster/teachers/${id}`, teacherData);
    return response.data;
  },
  deleteTeacher: async (id: string): Promise<ApiResponse<void>> => {
    const response = await api.delete(`/headmaster/teachers/${id}`);
    return response.data;
  },

  // Students
  getStudents: async (params = {}) => {
    const response = await api.get("/headmaster/students", { params });
    return response.data;
  },
  createStudent: async (studentData: any) => {
    const response = await api.post("/headmaster/students", studentData);
    return response.data;
  },
  createStudentWithImage: async (formData: FormData) => {
    const response = await api.post("/headmaster/students", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },
  updateStudent: async (id: string, studentData: any) => {
    const response = await api.put(`/headmaster/students/${id}`, studentData);
    return response.data;
  },
  updateStudentWithImage: async (id: string, formData: FormData) => {
    const response = await api.put(`/headmaster/students/${id}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },
  deleteStudent: async (id: string) => {
    const response = await api.delete(`/headmaster/students/${id}`);
    return response.data;
  },

  // Classes
  getClasses: async (params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }): Promise<ApiResponse<{ classes: Class[]; total: number }>> => {
    const response = await api.get("/headmaster/classes", { params });
    return response.data;
  },
  createClass: async (classData: any) => {
    const response = await api.post("/headmaster/classes", classData);
    return response.data;
  },
  updateClass: async (id: string, classData: any) => {
    const response = await api.put(`/headmaster/classes/${id}`, classData);
    return response.data;
  },
  deleteClass: async (id: string) => {
    const response = await api.delete(`/headmaster/classes/${id}`);
    return response.data;
  },

  // Parents
  getParents: async (params = {}) => {
    const response = await api.get("/headmaster/parents", { params });
    return response.data;
  },
  getParent: async (id: string) => {
    const response = await api.get(`/headmaster/parents/${id}`);
    return response.data;
  },
  createParent: async (parentData: any) => {
    const response = await api.post("/headmaster/parents", parentData);
    return response.data;
  },
  updateParent: async (id: string, parentData: any) => {
    const response = await api.put(`/headmaster/parents/${id}`, parentData);
    return response.data;
  },
  deleteParent: async (id: string) => {
    const response = await api.delete(`/headmaster/parents/${id}`);
    return response.data;
  },

  // Courses
  getCourses: async (params = {}) => {
    const response = await api.get("/headmaster/courses", { params });
    return response.data;
  },
  createCourse: async (courseData: any) => {
    const response = await api.post("/headmaster/courses", courseData);
    return response.data;
  },
  updateCourse: async (id: string, courseData: any) => {
    const response = await api.put(`/headmaster/courses/${id}`, courseData);
    return response.data;
  },
  deleteCourse: async (id: string) => {
    const response = await api.delete(`/headmaster/courses/${id}`);
    return response.data;
  },

  // Announcements
  getAnnouncements: async (params = {}) => {
    const response = await api.get("/headmaster/announcements", { params });
    return response.data;
  },
  createAnnouncement: async (announcementData: any) => {
    const response = await api.post("/headmaster/announcements", announcementData);
    return response.data;
  },
  updateAnnouncement: async (id: string, announcementData: any) => {
    const response = await api.put(`/headmaster/announcements/${id}`, announcementData);
    return response.data;
  },
  deleteAnnouncement: async (id: string) => {
    const response = await api.delete(`/headmaster/announcements/${id}`);
    return response.data;
  },

  // Attendance
  getTodayAttendance: async () => {
    const response = await api.get("/headmaster/attendance/today");
    return response.data;
  },
  getAttendanceSummary: async (date?: string) => {
    const params = date ? { date } : {};
    const response = await api.get("/headmaster/attendance/summary", { params });
    return response.data;
  },
  getAttendance: async (params = {}) => {
    const response = await api.get("/headmaster/attendance", { params });
    return response.data;
  },
  getStudentsAttendance: async (params = {}) => {
    const response = await api.get("/headmaster/students/attendance", { params });
    return response.data;
  },
  markAttendance: async (attendanceData: any) => {
    const response = await api.post("/headmaster/attendance", attendanceData);
    return response.data;
  },

  // Assessments
  getAssessments: async (params = {}) => {
    const response = await api.get("/headmaster/assessments", { params });
    return response.data;
  },

  // Fees Management
  getFeeRecords: async (params = {}) => {
    const response = await api.get("/headmaster/fees", { params });
    return response.data;
  },
  getFeeRecord: async (id: string) => {
    const response = await api.get(`/headmaster/fees/${id}`);
    return response.data;
  },
  createFeeRecord: async (feeData: any) => {
    const response = await api.post("/headmaster/fees", feeData);
    return response.data;
  },
  updateFeeRecord: async (id: string, feeData: any) => {
    const response = await api.put(`/headmaster/fees/${id}`, feeData);
    return response.data;
  },
  deleteFeeRecord: async (id: string) => {
    const response = await api.delete(`/headmaster/fees/${id}`);
    return response.data;
  },

  // Payment Management
  getPaymentRecords: async (params = {}) => {
    const response = await api.get("/headmaster/fees/payments/records", { params });
    return response.data;
  },
  createPayment: async (paymentData: any) => {
    const response = await api.post("/headmaster/fees/payments", paymentData);
    return response.data;
  },
  updatePayment: async (id: string, paymentData: any) => {
    const response = await api.put(`/headmaster/fees/payments/${id}`, paymentData);
    return response.data;
  },
  deletePayment: async (id: string) => {
    const response = await api.delete(`/headmaster/fees/payments/${id}`);
    return response.data;
  },

  // Arrears Management
  getArrears: async (params = {}) => {
    const response = await api.get("/headmaster/fees/arrears", { params });
    return response.data;
  },
  getArrearsSummary: async (params = {}) => {
    const response = await api.get("/headmaster/fees/arrears/summary", { params });
    return response.data;
  },

  // Fee Reports
  getFeeSummary: async (params = {}) => {
    const response = await api.get("/headmaster/fees/reports/summary", { params });
    return response.data;
  },
  exportFeeRecords: async (params = {}) => {
    const response = await api.get("/headmaster/fees/reports/export", {
      params,
      responseType: "text",
    });
    return response.data;
  },

  // Student Fees
  getStudentFees: async (studentId: string, params = {}) => {
    const response = await api.get(`/headmaster/fees/students/${studentId}`, { params });
    return response.data;
  },

  // Dropdown data endpoints
  getSubjectsForDropdown: async () => {
    const response = await api.get("/headmaster/dropdown/subjects");
    return response.data;
  },
  getClassesForDropdown: async () => {
    const response = await api.get("/headmaster/dropdown/classes");
    return response.data;
  },
  getParentsForDropdown: async () => {
    const response = await api.get("/headmaster/dropdown/parents");
    return response.data;
  },
  getStudentsForDropdown: async () => {
    const response = await api.get("/headmaster/dropdown/students");
    return response.data;
  },
};

export default api;
