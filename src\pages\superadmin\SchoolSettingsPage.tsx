import React, { useState } from 'react';
import { Layout } from '@/components/common/Layout';
import { 
  Save, 
  Building,
  Mail,
  Phone,
  Globe,
  MapPin,
  Settings,
  Database,
  Lock,
  Plus,
  Trash2,
  User,
  UserPlus,
  School,
  BookOpen,
  GraduationCap
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';

export function SchoolSettingsPage() {
  const [activeTab, setActiveTab] = useState('general');
  const { toast } = useToast();
  
  // Mock school data
  const schoolData = {
    name: 'Greenfield Academy',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'www.greenfieldacademy.edu',
    address: '123 Education Lane, Knowledge City, KC 12345',
    logo: '',
    established: '1985',
    description: 'Greenfield Academy is a premier educational institution dedicated to providing quality education and fostering academic excellence.',
    socialMedia: {
      facebook: 'facebook.com/greenfieldacademy',
      twitter: 'twitter.com/greenfieldacad',
      instagram: 'instagram.com/greenfieldacademy'
    }
  };
  
  // Mock academic settings
  const academicSettings = {
    currentSession: '2023-2024',
    sessionStart: '2023-09-01',
    sessionEnd: '2024-06-30',
    gradeScale: [
      { grade: 'A', minScore: 90, maxScore: 100 },
      { grade: 'B', minScore: 80, maxScore: 89 },
      { grade: 'C', minScore: 70, maxScore: 79 },
      { grade: 'D', minScore: 60, maxScore: 69 },
      { grade: 'F', minScore: 0, maxScore: 59 }
    ],
    departments: [
      'Mathematics',
      'Science',
      'English',
      'History',
      'Physical Education',
      'Art',
      'Music'
    ],
    classes: [
      '9A', '9B', '10A', '10B', '11A', '11B', '12A', '12B'
    ]
  };
  
  // Mock admin users
  const adminUsers = [
    { id: '1', name: 'John Smith', email: '<EMAIL>', role: 'Super Admin', status: 'active' },
    { id: '2', name: 'Sarah Johnson', email: '<EMAIL>', role: 'Admin', status: 'active' },
    { id: '3', name: 'Michael Brown', email: '<EMAIL>', role: 'Admin', status: 'inactive' }
  ];
  
  const handleSaveChanges = () => {
    toast({
      title: "Settings Saved",
      description: "Your changes have been successfully saved.",
    });
  };
  
  const handleAddAdmin = () => {
    toast({
      title: "Add Admin",
      description: "New admin user would be added here.",
    });
  };
  
  const handleDeleteAdmin = (admin: any) => {
    toast({
      title: "Delete Admin",
      description: `Admin ${admin.name} would be deleted here.`,
    });
  };

  return (
    <Layout>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">School Settings</h1>
          <p className="text-muted-foreground mt-1">
            Configure your school information and settings
          </p>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4 md:w-auto md:inline-flex">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="academic">Academic</TabsTrigger>
          <TabsTrigger value="admins">Administrators</TabsTrigger>
          <TabsTrigger value="branding">Branding</TabsTrigger>
        </TabsList>
        
        {/* General Settings */}
        <TabsContent value="general" className="space-y-6">
          <Card className="app-shadow">
            <CardHeader>
              <CardTitle>School Information</CardTitle>
              <CardDescription>
                Basic information about your school
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="school-name">School Name</Label>
                  <div className="relative">
                    <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input id="school-name" defaultValue={schoolData.name} className="pl-10" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="established">Established Year</Label>
                  <div className="relative">
                    <School className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input id="established" defaultValue={schoolData.established} className="pl-10" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input id="email" defaultValue={schoolData.email} className="pl-10" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input id="phone" defaultValue={schoolData.phone} className="pl-10" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <div className="relative">
                    <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input id="website" defaultValue={schoolData.website} className="pl-10" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Textarea id="address" defaultValue={schoolData.address} className="pl-10 min-h-[70px]" />
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">School Description</Label>
                <Textarea 
                  id="description" 
                  defaultValue={schoolData.description} 
                  className="min-h-[100px]"
                />
              </div>
              
              <Separator className="my-4" />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Social Media</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="facebook">Facebook</Label>
                    <Input id="facebook" defaultValue={schoolData.socialMedia.facebook} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="twitter">Twitter</Label>
                    <Input id="twitter" defaultValue={schoolData.socialMedia.twitter} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="instagram">Instagram</Label>
                    <Input id="instagram" defaultValue={schoolData.socialMedia.instagram} />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button className="gradient-bg text-white hover:opacity-90" onClick={handleSaveChanges}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* Academic Settings */}
        <TabsContent value="academic" className="space-y-6">
          <Card className="app-shadow">
            <CardHeader>
              <CardTitle>Academic Session</CardTitle>
              <CardDescription>
                Configure the current academic session and dates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="current-session">Current Session</Label>
                  <Input id="current-session" defaultValue={academicSettings.currentSession} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="session-start">Session Start Date</Label>
                  <Input id="session-start" type="date" defaultValue={academicSettings.sessionStart} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="session-end">Session End Date</Label>
                  <Input id="session-end" type="date" defaultValue={academicSettings.sessionEnd} />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button className="gradient-bg text-white hover:opacity-90" onClick={handleSaveChanges}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
          
          <Card className="app-shadow">
            <CardHeader>
              <CardTitle>Grading System</CardTitle>
              <CardDescription>
                Configure the grading scale for student assessments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Grade</TableHead>
                    <TableHead>Minimum Score</TableHead>
                    <TableHead>Maximum Score</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {academicSettings.gradeScale.map((grade, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{grade.grade}</TableCell>
                      <TableCell>{grade.minScore}</TableCell>
                      <TableCell>{grade.maxScore}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="mt-4 flex justify-end">
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Grade
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="app-shadow">
              <CardHeader>
                <CardTitle>Departments</CardTitle>
                <CardDescription>
                  Manage academic departments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {academicSettings.departments.map((dept, index) => (
                      <Badge key={index} variant="outline" className="bg-primary/10 text-primary flex items-center gap-1">
                        <BookOpen className="h-3 w-3" />
                        {dept}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2 mt-4">
                    <Input placeholder="New department name" className="max-w-xs" />
                    <Button variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="app-shadow">
              <CardHeader>
                <CardTitle>Classes</CardTitle>
                <CardDescription>
                  Manage school classes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {academicSettings.classes.map((cls, index) => (
                      <Badge key={index} variant="outline" className="bg-primary/10 text-primary flex items-center gap-1">
                        <GraduationCap className="h-3 w-3" />
                        {cls}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2 mt-4">
                    <Input placeholder="New class name" className="max-w-xs" />
                    <Button variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Administrators */}
        <TabsContent value="admins" className="space-y-6">
          <Card className="app-shadow">
            <CardHeader>
              <CardTitle>System Administrators</CardTitle>
              <CardDescription>
                Manage users with administrative access to the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {adminUsers.map((admin) => (
                    <TableRow key={admin.id}>
                      <TableCell className="font-medium">{admin.name}</TableCell>
                      <TableCell>{admin.email}</TableCell>
                      <TableCell>
                        <Badge variant={admin.role === 'Super Admin' ? 'default' : 'outline'}>
                          {admin.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={admin.status === 'active' ? 'success' : 'secondary'}
                          className={
                            admin.status === 'active' 
                              ? 'status-active' 
                              : 'status-inactive'
                          }
                        >
                          {admin.status === 'active' ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDeleteAdmin(admin)}
                          disabled={admin.role === 'Super Admin'}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="mt-4 flex justify-end">
                <Button variant="outline" onClick={handleAddAdmin}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Administrator
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card className="app-shadow">
            <CardHeader>
              <CardTitle>Access Control</CardTitle>
              <CardDescription>
                Configure role-based permissions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Admin Access</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow administrators to access all system features
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Teacher Management</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow administrators to manage teacher accounts
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Financial Management</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow administrators to access financial records
                    </p>
                  </div>
                  <Switch defaultChecked={false} />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>System Settings</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow administrators to modify system settings
                    </p>
                  </div>
                  <Switch defaultChecked={false} />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button className="gradient-bg text-white hover:opacity-90" onClick={handleSaveChanges}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* Branding */}
        <TabsContent value="branding" className="space-y-6">
          <Card className="app-shadow">
            <CardHeader>
              <CardTitle>School Branding</CardTitle>
              <CardDescription>
                Customize your school's visual identity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Label>School Logo</Label>
                  <div className="border-2 border-dashed border-muted-foreground/20 rounded-lg p-8 flex flex-col items-center justify-center">
                    {schoolData.logo ? (
                      <img src={schoolData.logo} alt="School Logo" className="max-h-32" />
                    ) : (
                      <div className="w-32 h-32 bg-muted/20 rounded-md flex items-center justify-center">
                        <Building className="h-12 w-12 text-muted-foreground/40" />
                      </div>
                    )}
                    <Button variant="outline" size="sm" className="mt-4">
                      Upload Logo
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <Label>School Colors</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="primary-color">Primary Color</Label>
                      <div className="flex gap-2">
                        <div className="w-10 h-10 rounded-md bg-primary"></div>
                        <Input id="primary-color" defaultValue="#0070F3" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="secondary-color">Secondary Color</Label>
                      <div className="flex gap-2">
                        <div className="w-10 h-10 rounded-md bg-secondary"></div>
                        <Input id="secondary-color" defaultValue="#F5F5F5" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="accent-color">Accent Color</Label>
                      <div className="flex gap-2">
                        <div className="w-10 h-10 rounded-md bg-accent"></div>
                        <Input id="accent-color" defaultValue="#FFDD00" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <Separator className="my-4" />
              
              <div className="space-y-4">
                <Label>Email Template</Label>
                <div className="border rounded-md p-4 bg-muted/20">
                  <div className="space-y-2">
                    <div className="h-8 w-full bg-primary/20 rounded-md"></div>
                    <div className="h-4 w-3/4 bg-muted rounded-md"></div>
                    <div className="h-4 w-1/2 bg-muted rounded-md"></div>
                    <div className="h-20 w-full bg-muted/50 rounded-md"></div>
                    <div className="h-8 w-1/4 bg-primary/20 rounded-md mx-auto"></div>
                    <div className="h-4 w-full bg-muted/30 rounded-md"></div>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  Customize Email Template
                </Button>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button className="gradient-bg text-white hover:opacity-90" onClick={handleSaveChanges}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </Layout>
  );
}