import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, MessageSquare } from 'lucide-react';

interface RemarkRule {
  id: string;
  name: string;
  minAverage: number;
  maxAverage: number;
  remark: string;
  classes: string[];
  advanced: {
    enabled: boolean;
    conditions: string[];
    priority: number;
  };
}

interface ReportRemarksModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  remarkRules: RemarkRule[];
}

export function ReportRemarksModal({ open, onClose, onSubmit, remarkRules }: ReportRemarksModalProps) {
  const [rules, setRules] = useState<RemarkRule[]>(remarkRules);
  const [editingRule, setEditingRule] = useState<RemarkRule | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const availableClasses = [
    'Primary 1', 'Primary 2', 'Primary 3', 'Primary 4', 'Primary 5', 'Primary 6',
    'JHS 1', 'JHS 2', 'JHS 3', 'SHS 1', 'SHS 2', 'SHS 3'
  ];

  const defaultRule: RemarkRule = {
    id: '',
    name: '',
    minAverage: 0,
    maxAverage: 100,
    remark: '',
    classes: [],
    advanced: {
      enabled: false,
      conditions: [],
      priority: 1
    }
  };

  const handleAddRule = () => {
    setEditingRule({ ...defaultRule, id: Date.now().toString() });
    setIsEditing(true);
  };

  const handleEditRule = (rule: RemarkRule) => {
    setEditingRule(rule);
    setIsEditing(true);
  };

  const handleSaveRule = () => {
    if (!editingRule) return;
    
    const updatedRules = rules.find(r => r.id === editingRule.id)
      ? rules.map(r => r.id === editingRule.id ? editingRule : r)
      : [...rules, editingRule];
    
    setRules(updatedRules);
    setIsEditing(false);
    setEditingRule(null);
  };

  const handleDeleteRule = (ruleId: string) => {
    setRules(rules.filter(r => r.id !== ruleId));
  };

  const handleClassToggle = (className: string, checked: boolean) => {
    if (!editingRule) return;
    
    const updatedClasses = checked
      ? [...editingRule.classes, className]
      : editingRule.classes.filter(c => c !== className);
    
    setEditingRule({
      ...editingRule,
      classes: updatedClasses
    });
  };

  const handleSelectAllClasses = (checked: boolean) => {
    if (!editingRule) return;
    
    setEditingRule({
      ...editingRule,
      classes: checked ? availableClasses : []
    });
  };

  const handleSubmit = () => {
    onSubmit({ remarkRules: rules });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Report Remarks Configuration
          </DialogTitle>
        </DialogHeader>

        {!isEditing ? (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <p className="text-sm text-slate-600">Configure remarks that appear on report cards based on student averages</p>
              <Button onClick={handleAddRule}>
                <Plus className="h-4 w-4 mr-2" />
                Add Remark Rule
              </Button>
            </div>

            <div className="space-y-4">
              {rules.map((rule) => (
                <div key={rule.id} className="border rounded-lg p-4 bg-slate-50">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-semibold text-slate-900">{rule.name}</h4>
                      <p className="text-sm text-slate-600">
                        Average: {rule.minAverage}% - {rule.maxAverage}%
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={() => handleEditRule(rule)}>
                        Edit
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDeleteRule(rule.id)} className="text-red-600">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="font-medium text-slate-700">Classes:</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {rule.classes.slice(0, 3).map((cls) => (
                          <span key={cls} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                            {cls}
                          </span>
                        ))}
                        {rule.classes.length > 3 && (
                          <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                            +{rule.classes.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                    <div>
                      <p className="font-medium text-slate-700">Remark:</p>
                      <p className="text-slate-600 mt-1">{rule.remark}</p>
                    </div>
                  </div>
                  
                  {rule.advanced.enabled && (
                    <div className="mt-3 pt-3 border-t">
                      <p className="text-xs text-purple-600 font-medium">Advanced settings enabled</p>
                    </div>
                  )}
                </div>
              ))}
              
              {rules.length === 0 && (
                <div className="text-center py-8 text-slate-500">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No remark rules configured</p>
                  <p className="text-sm">Add rules to automatically generate remarks on report cards</p>
                </div>
              )}
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={handleSubmit}>
                Save Configuration
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="ruleName">Rule Name</Label>
                <Input
                  id="ruleName"
                  value={editingRule?.name || ''}
                  onChange={(e) => setEditingRule(prev => prev ? {...prev, name: e.target.value} : null)}
                  placeholder="e.g., Excellent Performance"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="minAverage">Min Average (%)</Label>
                  <Input
                    id="minAverage"
                    type="number"
                    min="0"
                    max="100"
                    value={editingRule?.minAverage || 0}
                    onChange={(e) => setEditingRule(prev => prev ? {...prev, minAverage: parseInt(e.target.value) || 0} : null)}
                  />
                </div>
                <div>
                  <Label htmlFor="maxAverage">Max Average (%)</Label>
                  <Input
                    id="maxAverage"
                    type="number"
                    min="0"
                    max="100"
                    value={editingRule?.maxAverage || 100}
                    onChange={(e) => setEditingRule(prev => prev ? {...prev, maxAverage: parseInt(e.target.value) || 100} : null)}
                  />
                </div>
              </div>
            </div>

            <div>
              <Label>Affected Classes</Label>
              <div className="mt-2 mb-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="selectAllClasses"
                    checked={editingRule?.classes.length === availableClasses.length}
                    onCheckedChange={handleSelectAllClasses}
                  />
                  <Label htmlFor="selectAllClasses" className="text-sm font-medium">Select All Classes</Label>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-32 overflow-y-auto border rounded-lg p-3">
                {availableClasses.map((className) => (
                  <div key={className} className="flex items-center space-x-2">
                    <Checkbox
                      id={className}
                      checked={editingRule?.classes.includes(className) || false}
                      onCheckedChange={(checked) => handleClassToggle(className, checked as boolean)}
                    />
                    <Label htmlFor={className} className="text-sm">{className}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="remark">Remark Text</Label>
              <Textarea
                id="remark"
                value={editingRule?.remark || ''}
                onChange={(e) => setEditingRule(prev => prev ? {...prev, remark: e.target.value} : null)}
                placeholder="Enter the remark that will appear on report cards..."
                rows={3}
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="advanced"
                  checked={editingRule?.advanced.enabled || false}
                  onCheckedChange={(checked) => setEditingRule(prev => prev ? {
                    ...prev,
                    advanced: { ...prev.advanced, enabled: checked as boolean }
                  } : null)}
                />
                <Label htmlFor="advanced" className="font-medium">Advanced Configuration</Label>
              </div>

              {editingRule?.advanced.enabled && (
                <div className="bg-purple-50 p-4 rounded-lg space-y-4">
                  <div>
                    <Label htmlFor="priority">Priority Level</Label>
                    <Select 
                      value={editingRule.advanced.priority.toString()} 
                      onValueChange={(value) => setEditingRule(prev => prev ? {
                        ...prev,
                        advanced: { ...prev.advanced, priority: parseInt(value) }
                      } : null)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">High Priority</SelectItem>
                        <SelectItem value="2">Medium Priority</SelectItem>
                        <SelectItem value="3">Low Priority</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-purple-600 mt-1">Higher priority remarks override lower priority ones</p>
                  </div>

                  <div>
                    <Label>Additional Conditions</Label>
                    <div className="space-y-2 mt-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="attendance" />
                        <Label htmlFor="attendance" className="text-sm">Consider attendance rate</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="conduct" />
                        <Label htmlFor="conduct" className="text-sm">Consider conduct score</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="improvement" />
                        <Label htmlFor="improvement" className="text-sm">Consider improvement trend</Label>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveRule}>
                Save Rule
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}