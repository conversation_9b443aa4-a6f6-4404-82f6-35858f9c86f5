import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Menu, X, LogOut, ChevronDown, Bell, Moon, Sun, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { useTheme } from "@/contexts/ThemeContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { CollapsibleSidebar } from "./CollapsibleSidebar";
import { headmasterAPI } from "@/lib/api";

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const { theme, setTheme } = useTheme();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [schoolName, setSchoolName] = useState("School Management System");
  const [collapsed, setCollapsed] = useState(() => {
    const savedState = localStorage.getItem("sidebarCollapsed");
    return savedState ? savedState === "true" : false;
  });

  // Fetch school name from dashboard API
  const fetchSchoolName = async () => {
    try {
      if (user?.role === "super_admin") {
        setSchoolName("School Manager");
        return;
      }
      
      const response = await headmasterAPI.getDashboardStats();
      if (response.success && response.data.stats.schoolName) {
        setSchoolName(response.data.stats.schoolName);
      }
    } catch (error) {
      console.error('Error fetching school name:', error);
    }
  };

  // After mounting, we can safely show the UI that depends on the theme
  useEffect(() => {
    setMounted(true);
    if (user) {
      fetchSchoolName();
    }
  }, [user]);

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  const toggleCollapse = () => {
    const newState = !collapsed;
    setCollapsed(newState);
    localStorage.setItem("sidebarCollapsed", String(newState));
  };

  return (
    <div className="min-h-screen bg-background flex">
      {/* Sidebar - Mobile */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={closeSidebar}></div>
          <div className="relative flex flex-col w-full max-w-xs h-full">
            <div className="flex justify-end px-4 pt-5">
              <button
                className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-primary"
                onClick={closeSidebar}
              >
                <X className="h-6 w-6 text-muted-foreground" />
              </button>
            </div>
            <CollapsibleSidebar isMobile onClose={closeSidebar} schoolName={schoolName} />
          </div>
        </div>
      )}

      {/* Sidebar - Desktop */}
      <div className="hidden lg:block h-screen z-30">
        <CollapsibleSidebar
          schoolName={schoolName}
          isCollapsed={collapsed}
          onToggleCollapse={toggleCollapse}
          expandAllSubmenus={true}
        />
      </div>

      {/* Main content area with left margin to account for fixed sidebar */}
      <div className={`flex flex-col flex-1 transition-all duration-300 w-full ${collapsed ? 'lg:ml-[70px]' : 'lg:ml-[260px]'}`}>
        {/* Enhanced Top Navigation Bar */}
        <header className="bg-gradient-to-r from-white via-slate-50/50 to-white border-b border-slate-200/50 sticky top-0 z-20 shadow-lg backdrop-blur-sm">
          <div className="px-4 sm:px-6 lg:px-8 flex h-16 items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={toggleSidebar}
                className="p-2 rounded-xl text-slate-600 hover:text-slate-900 hover:bg-slate-100 transition-all duration-200 lg:hidden"
              >
                <Menu className="h-6 w-6" />
              </button>
              <div className="flex items-center gap-3">
                <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-2 rounded-xl shadow-lg">
                  <div className="w-6 h-6 bg-white rounded-md flex items-center justify-center">
                    <span className="text-xs font-bold text-blue-600">S</span>
                  </div>
                </div>
                <div>
                  <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    {schoolName}
                  </h1>
                  <p className="text-xs text-slate-500 hidden sm:block">Education Management System</p>
                </div>
              </div>
            </div>

            <div className="hidden md:flex items-center max-w-md w-full mx-6">
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search students, classes, or staff..."
                  className="pl-10 bg-slate-50/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl shadow-sm"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="icon" className="relative hover:bg-slate-100 rounded-xl transition-all duration-200">
                <Bell className="h-5 w-5 text-slate-600" />
                <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-gradient-to-r from-red-500 to-pink-500 animate-pulse shadow-lg"></span>
              </Button>

              {mounted && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                  aria-label="Toggle theme"
                  className="hover:bg-slate-100 rounded-xl transition-all duration-200"
                >
                  {theme === "dark" ? <Sun className="h-5 w-5 text-slate-600" /> : <Moon className="h-5 w-5 text-slate-600" />}
                </Button>
              )}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-3 hover:bg-slate-100 rounded-xl p-2 transition-all duration-200">
                    <Avatar className="h-9 w-9 ring-2 ring-blue-500/20">
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                        {user?.name?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex items-center">
                      <div className="hidden md:block text-left">
                        <p className="text-sm font-semibold text-slate-900">{user?.name}</p>
                        <p className="text-xs text-slate-500 capitalize">{user?.role?.replace('_', ' ')}</p>
                      </div>
                      <ChevronDown className="h-4 w-4 ml-2 text-slate-400" />
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-64 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl rounded-xl">
                  <div className="flex items-center gap-3 p-4 border-b border-slate-100">
                    <Avatar className="h-12 w-12 ring-2 ring-blue-500/20">
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold text-lg">
                        {user?.name?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-semibold text-slate-900">{user?.name}</p>
                      <p className="text-xs text-slate-500">{user?.email}</p>
                      <p className="text-xs text-blue-600 capitalize font-medium">{user?.role?.replace('_', ' ')}</p>
                    </div>
                  </div>
                  <div className="p-2">
                    <DropdownMenuItem className="rounded-lg p-3 hover:bg-slate-50 cursor-pointer">
                      <div className="flex items-center gap-3">
                        <div className="bg-blue-100 p-1.5 rounded-lg">
                          <span className="text-blue-600 text-sm">👤</span>
                        </div>
                        <span className="font-medium">Profile Settings</span>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem className="rounded-lg p-3 hover:bg-slate-50 cursor-pointer">
                      <div className="flex items-center gap-3">
                        <div className="bg-purple-100 p-1.5 rounded-lg">
                          <span className="text-purple-600 text-sm">⚙️</span>
                        </div>
                        <span className="font-medium">System Settings</span>
                      </div>
                    </DropdownMenuItem>
                  </div>
                  <div className="p-2 border-t border-slate-100">
                    <DropdownMenuItem 
                      onClick={handleLogout} 
                      className="rounded-lg p-3 text-red-600 hover:bg-red-50 cursor-pointer focus:text-red-600"
                    >
                      <div className="flex items-center gap-3">
                        <div className="bg-red-100 p-1.5 rounded-lg">
                          <LogOut className="h-4 w-4 text-red-600" />
                        </div>
                        <span className="font-medium">Sign Out</span>
                      </div>
                    </DropdownMenuItem>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Enhanced Main Content */}
        <main className="p-4 md:p-6 flex-1 overflow-auto bg-gradient-to-br from-slate-50/30 via-white to-blue-50/20 min-h-screen">
          <div className="max-w-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}