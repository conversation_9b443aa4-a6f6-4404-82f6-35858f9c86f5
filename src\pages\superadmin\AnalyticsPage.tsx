import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  GraduationCap, 
  Building2, 
  Calendar,
  BarChart3,
  Pie<PERSON><PERSON> as PieChartIcon,
  Activity
} from 'lucide-react';
import { superAdminAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { MOCK_CHART_DATA } from '@/lib/constants';

export function AnalyticsPage() {
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState<any>(null);

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const response = await superAdminAPI.getChartData();
      
      if (response.success) {
        setChartData(response.data.data);
      } else {
        // Fallback to mock data
        setChartData(MOCK_CHART_DATA);
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      setChartData(MOCK_CHART_DATA);
      showToast.error('Error', 'Failed to load analytics data, showing sample data');
    } finally {
      setLoading(false);
    }
  };

  const colors = ['#266774', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground mt-1">
            Comprehensive insights and data visualization for all schools
          </p>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Students Per School */}
          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div>
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-primary" />
                  Students Per School
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Distribution of students across schools
                </p>
              </div>
            </CardHeader>
            <CardContent>
              <div style={{ width: '100%', height: 300 }}>
                <ResponsiveContainer>
                  <BarChart data={chartData?.studentsPerSchool || []}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis 
                      dataKey="name" 
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip />
                    <Bar 
                      dataKey="students" 
                      fill="hsl(var(--primary))" 
                      radius={[4, 4, 0, 0]}
                      name="Students"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Attendance Overview */}
          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div>
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <PieChartIcon className="h-5 w-5 text-primary" />
                  Attendance Overview
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Overall attendance distribution
                </p>
              </div>
            </CardHeader>
            <CardContent>
              <div style={{ width: '100%', height: 300 }}>
                <ResponsiveContainer>
                  <PieChart>
                    <Tooltip formatter={(value) => [`${value}%`, 'Percentage']} />
                    <Legend />
                    <Pie
                      data={chartData?.attendanceSummary || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="hsl(var(--primary))"
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {(chartData?.attendanceSummary || []).map((entry: any, index: number) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={colors[index % colors.length]} 
                        />
                      ))}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Monthly Registrations */}
          <Card className="hover-lift lg:col-span-2">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div>
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-primary" />
                  Monthly Student Registrations
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Student enrollment trends over the past year
                </p>
              </div>
            </CardHeader>
            <CardContent>
              <div style={{ width: '100%', height: 350 }}>
                <ResponsiveContainer>
                  <AreaChart data={chartData?.monthlyRegistrations || []}>
                    <defs>
                      <linearGradient id="colorStudents" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="students" 
                      stroke="hsl(var(--primary))" 
                      fillOpacity={1} 
                      fill="url(#colorStudents)"
                      name="New Students"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Total Enrollment</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {chartData?.studentsPerSchool?.reduce((sum: number, school: any) => sum + school.students, 0).toLocaleString() || '0'}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Across all schools
              </p>
            </CardContent>
          </Card>

          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Average per School</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {chartData?.studentsPerSchool?.length > 0 
                  ? Math.round(chartData.studentsPerSchool.reduce((sum: number, school: any) => sum + school.students, 0) / chartData.studentsPerSchool.length).toLocaleString()
                  : '0'
                }
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Students per school
              </p>
            </CardContent>
          </Card>

          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Monthly Growth</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {chartData?.monthlyRegistrations?.reduce((sum: number, month: any) => sum + month.students, 0) || '0'}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                New students this year
              </p>
            </CardContent>
          </Card>

          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {chartData?.attendanceSummary?.find((item: any) => item.name === 'Present')?.value || 0}%
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Overall attendance
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}