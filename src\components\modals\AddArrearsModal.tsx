import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface AddArrearsModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

export function AddArrearsModal({ open, onClose, onSubmit }: AddArrearsModalProps) {
  const [formData, setFormData] = useState({
    studentName: '',
    studentClass: '',
    amountPaid: '',
    totalAmount: '',
    feeType: '',
    dueDate: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const arrears = parseFloat(formData.totalAmount) - parseFloat(formData.amountPaid);
    onSubmit({
      ...formData,
      amountPaid: parseFloat(formData.amountPaid),
      totalAmount: parseFloat(formData.totalAmount),
      arrears
    });
    setFormData({
      studentName: '',
      studentClass: '',
      amountPaid: '',
      totalAmount: '',
      feeType: '',
      dueDate: ''
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Add Student to Arrears List</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="studentName">Student Name</Label>
            <Input
              id="studentName"
              value={formData.studentName}
              onChange={(e) => setFormData({...formData, studentName: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="studentClass">Student Class</Label>
            <Input
              id="studentClass"
              value={formData.studentClass}
              onChange={(e) => setFormData({...formData, studentClass: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="totalAmount">Total Amount (GHS)</Label>
            <Input
              id="totalAmount"
              type="number"
              step="0.01"
              value={formData.totalAmount}
              onChange={(e) => setFormData({...formData, totalAmount: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="amountPaid">Amount Paid (GHS)</Label>
            <Input
              id="amountPaid"
              type="number"
              step="0.01"
              value={formData.amountPaid}
              onChange={(e) => setFormData({...formData, amountPaid: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="feeType">Fee Type</Label>
            <Select value={formData.feeType} onValueChange={(value) => setFormData({...formData, feeType: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select fee type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Tuition Fee">Tuition Fee</SelectItem>
                <SelectItem value="School Fees">School Fees</SelectItem>
                <SelectItem value="Examination Fee">Examination Fee</SelectItem>
                <SelectItem value="Activity Fee">Activity Fee</SelectItem>
                <SelectItem value="Library Fee">Library Fee</SelectItem>
                <SelectItem value="Transport Fee">Transport Fee</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="dueDate">Due Date</Label>
            <Input
              id="dueDate"
              type="date"
              value={formData.dueDate}
              onChange={(e) => setFormData({...formData, dueDate: e.target.value})}
              required
            />
          </div>
          
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              Add to Arrears
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}