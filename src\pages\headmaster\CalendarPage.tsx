import React, { useState } from 'react';
import { Layout } from '@/components/common/Layout';
import { 
  Plus, 
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  Clock,
  Users,
  MapPin,
  Tag,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format, addDays, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths } from 'date-fns';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { AddEventModal, AddEventFormData } from '@/components/modals/AddEventModal';
import { useToast } from '@/components/ui/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

// Mock event data
const MOCK_EVENTS = [
  { 
    id: '1', 
    title: 'Parent-Teacher Meeting', 
    date: '2023-11-15',
    startTime: '15:00',
    endTime: '18:00',
    location: 'School Auditorium',
    description: 'Annual parent-teacher meeting to discuss student progress.',
    type: 'meeting',
    attendees: ['teachers', 'parents']
  },
  { 
    id: '2', 
    title: 'Annual Sports Day', 
    date: '2023-11-20',
    startTime: '09:00',
    endTime: '16:00',
    location: 'School Grounds',
    description: 'Annual sports competition with various athletic events.',
    type: 'sports',
    attendees: ['students', 'teachers']
  },
  { 
    id: '3', 
    title: 'Science Exhibition', 
    date: '2023-11-25',
    startTime: '10:00',
    endTime: '14:00',
    location: 'School Hall',
    description: 'Exhibition of student science projects.',
    type: 'academic',
    attendees: ['students', 'teachers', 'parents']
  },
  { 
    id: '4', 
    title: 'Staff Meeting', 
    date: '2023-11-10',
    startTime: '14:30',
    endTime: '16:00',
    location: 'Conference Room',
    description: 'Monthly staff meeting to discuss school matters.',
    type: 'meeting',
    attendees: ['teachers', 'staff']
  },
  { 
    id: '5', 
    title: 'Math Competition', 
    date: '2023-11-18',
    startTime: '09:30',
    endTime: '12:30',
    location: 'Classroom 101',
    description: 'Inter-class mathematics competition.',
    type: 'academic',
    attendees: ['students', 'teachers']
  },
  { 
    id: '6', 
    title: 'School Holiday', 
    date: '2023-11-24',
    startTime: '00:00',
    endTime: '23:59',
    location: 'N/A',
    description: 'School closed for holiday.',
    type: 'holiday',
    attendees: ['all']
  },
  { 
    id: '7', 
    title: 'Art Exhibition', 
    date: '2023-11-28',
    startTime: '13:00',
    endTime: '16:00',
    location: 'Art Room',
    description: 'Exhibition of student artwork.',
    type: 'cultural',
    attendees: ['students', 'teachers', 'parents']
  },
  { 
    id: '8', 
    title: 'Career Counseling', 
    date: '2023-11-30',
    startTime: '10:00',
    endTime: '15:00',
    location: 'Guidance Office',
    description: 'Career counseling sessions for senior students.',
    type: 'guidance',
    attendees: ['students']
  }
];

export function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [events, setEvents] = useState(MOCK_EVENTS);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { toast } = useToast();
  
  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });
  
  // Get events for the selected date
  const selectedDateEvents = events.filter(event => 
    isSameDay(new Date(event.date), selectedDate)
  );
  
  // Get events for the current month
  const currentMonthEvents = events.filter(event => 
    isSameMonth(new Date(event.date), currentDate)
  );
  
  // Get upcoming events (next 7 days)
  const today = new Date();
  const nextWeek = addDays(today, 7);
  const upcomingEvents = events.filter(event => {
    const eventDate = new Date(event.date);
    return eventDate >= today && eventDate <= nextWeek;
  }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  const nextMonth = () => setCurrentDate(addMonths(currentDate, 1));
  const prevMonth = () => setCurrentDate(subMonths(currentDate, 1));
  
  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'meeting':
        return 'bg-primary/10 text-primary hover:bg-primary/20';
      case 'sports':
        return 'bg-warning/10 text-warning hover:bg-warning/20';
      case 'academic':
        return 'bg-success/10 text-success hover:bg-success/20';
      case 'holiday':
        return 'bg-destructive/10 text-destructive hover:bg-destructive/20';
      case 'cultural':
        return 'bg-purple-100 text-purple-600 hover:bg-purple-200';
      case 'guidance':
        return 'bg-blue-100 text-blue-600 hover:bg-blue-200';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };
  
  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'meeting':
        return <Users className="h-4 w-4" />;
      case 'sports':
        return <CalendarIcon className="h-4 w-4" />;
      case 'academic':
        return <CalendarIcon className="h-4 w-4" />;
      case 'holiday':
        return <CalendarIcon className="h-4 w-4" />;
      case 'cultural':
        return <CalendarIcon className="h-4 w-4" />;
      case 'guidance':
        return <CalendarIcon className="h-4 w-4" />;
      default:
        return <CalendarIcon className="h-4 w-4" />;
    }
  };
  
  // Check if a day has events
  const dayHasEvents = (day: Date) => {
    return events.some(event => isSameDay(new Date(event.date), day));
  };
  
  const handleAddEvent = (data: AddEventFormData) => {
    const newEvent = {
      id: (events.length + 1).toString(),
      title: data.title,
      date: format(data.date, 'yyyy-MM-dd'),
      startTime: data.startTime,
      endTime: data.endTime,
      location: data.location,
      description: data.description || '',
      type: data.type,
      attendees: data.attendees
    };
    
    setEvents([...events, newEvent]);
    
    toast({
      title: "Event Added",
      description: `"${data.title}" has been added to the calendar.`,
    });
  };
  
  const handleViewEvent = (event: any) => {
    // In a real app, this would open a view modal
    setSelectedEvent(event);
    toast({
      title: "View Event",
      description: `Viewing details for "${event.title}".`,
    });
  };
  
  const handleEditEvent = (event: any) => {
    // In a real app, this would open an edit modal
    setSelectedEvent(event);
    toast({
      title: "Edit Event",
      description: `Editing "${event.title}".`,
    });
  };
  
  const handleDeleteEvent = (event: any) => {
    setSelectedEvent(event);
    setIsDeleteDialogOpen(true);
  };
  
  const confirmDeleteEvent = () => {
    if (selectedEvent) {
      setEvents(events.filter(e => e.id !== selectedEvent.id));
      setIsDeleteDialogOpen(false);
      
      toast({
        title: "Event Deleted",
        description: `"${selectedEvent.title}" has been removed from the calendar.`,
      });
    }
  };

  return (
    <Layout>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">School Calendar</h1>
          <p className="text-muted-foreground mt-1">
            View and manage school events and activities
          </p>
        </div>
        <Button 
          className="flex items-center gap-2 gradient-bg text-white hover:opacity-90"
          onClick={() => setIsAddModalOpen(true)}
        >
          <Plus className="h-4 w-4" />
          <span>Add Event</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="app-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-4">
              <CardTitle>{format(currentDate, 'MMMM yyyy')}</CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="icon" onClick={prevMonth}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" onClick={() => setCurrentDate(new Date())}>
                  <CalendarIcon className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" onClick={nextMonth}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Calendar grid header (days of week) */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                  <div key={day} className="text-center text-sm font-medium py-2">
                    {day}
                  </div>
                ))}
              </div>
              
              {/* Calendar grid */}
              <div className="grid grid-cols-7 gap-1">
                {Array.from({ length: monthStart.getDay() }).map((_, index) => (
                  <div key={`empty-start-${index}`} className="h-24 p-1 border border-transparent"></div>
                ))}
                
                {monthDays.map((day) => {
                  const isToday = isSameDay(day, new Date());
                  const isSelected = isSameDay(day, selectedDate);
                  const hasEvents = dayHasEvents(day);
                  
                  // Get events for this day
                  const dayEvents = events.filter(event => 
                    isSameDay(new Date(event.date), day)
                  ).slice(0, 2); // Show max 2 events
                  
                  const moreEventsCount = events.filter(event => 
                    isSameDay(new Date(event.date), day)
                  ).length - 2;
                  
                  return (
                    <div 
                      key={day.toString()}
                      className={cn(
                        "h-24 p-1 border rounded-md transition-colors",
                        isToday ? "border-primary" : "border-border",
                        isSelected ? "bg-accent" : "hover:bg-muted/50 cursor-pointer"
                      )}
                      onClick={() => setSelectedDate(day)}
                    >
                      <div className="flex justify-between items-start">
                        <span 
                          className={cn(
                            "inline-flex h-6 w-6 items-center justify-center rounded-full text-sm",
                            isToday ? "bg-primary text-primary-foreground" : ""
                          )}
                        >
                          {format(day, 'd')}
                        </span>
                        {hasEvents && !isSelected && (
                          <span className="h-1.5 w-1.5 rounded-full bg-primary"></span>
                        )}
                      </div>
                      
                      <div className="mt-1 space-y-1">
                        {dayEvents.map((event) => (
                          <div 
                            key={event.id}
                            className="text-xs truncate px-1.5 py-0.5 rounded-sm bg-primary/10 text-primary"
                          >
                            {event.title}
                          </div>
                        ))}
                        
                        {moreEventsCount > 0 && (
                          <div className="text-xs text-muted-foreground px-1.5">
                            +{moreEventsCount} more
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
                
                {Array.from({ length: 6 - monthEnd.getDay() }).map((_, index) => (
                  <div key={`empty-end-${index}`} className="h-24 p-1 border border-transparent"></div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          {/* Selected day events */}
          <Card className="mt-6">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between">
                <span>Events for {format(selectedDate, 'MMMM d, yyyy')}</span>
                <Badge variant="outline" className={isSameDay(selectedDate, new Date()) ? 'bg-primary/10 text-primary' : ''}>
                  {isSameDay(selectedDate, new Date()) ? 'Today' : format(selectedDate, 'EEEE')}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedDateEvents.length > 0 ? (
                <div className="space-y-4">
                  {selectedDateEvents.map((event) => (
                    <div key={event.id} className="flex items-start gap-4 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                      <div className={cn("p-2 rounded-full", getEventTypeColor(event.type))}>
                        {getEventTypeIcon(event.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium">{event.title}</h3>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem 
                                className="flex items-center gap-2 cursor-pointer"
                                onClick={() => handleViewEvent(event)}
                              >
                                <Eye className="h-4 w-4" />
                                <span>View Details</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="flex items-center gap-2 cursor-pointer"
                                onClick={() => handleEditEvent(event)}
                              >
                                <Edit className="h-4 w-4" />
                                <span>Edit Event</span>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                className="flex items-center gap-2 text-destructive focus:text-destructive cursor-pointer"
                                onClick={() => handleDeleteEvent(event)}
                              >
                                <Trash2 className="h-4 w-4" />
                                <span>Delete Event</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Clock className="h-3.5 w-3.5 mr-1" />
                            <span>{event.startTime} - {event.endTime}</span>
                          </div>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <MapPin className="h-3.5 w-3.5 mr-1" />
                            <span>{event.location}</span>
                          </div>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Tag className="h-3.5 w-3.5 mr-1" />
                            <span>{event.type.charAt(0).toUpperCase() + event.type.slice(1)}</span>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mt-2">{event.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <CalendarIcon className="h-10 w-10 mx-auto mb-4 opacity-20" />
                  <p>No events scheduled for this day</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-4"
                    onClick={() => setIsAddModalOpen(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Event
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Upcoming events sidebar */}
        <div>
          <Card className="app-shadow">
            <CardHeader className="pb-3">
              <CardTitle>Upcoming Events</CardTitle>
            </CardHeader>
            <CardContent>
              {upcomingEvents.length > 0 ? (
                <div className="space-y-4">
                  {upcomingEvents.map((event) => (
                    <div key={event.id} className="flex items-start gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                      <div className={cn("p-2 rounded-full", getEventTypeColor(event.type))}>
                        {getEventTypeIcon(event.type)}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-sm">{event.title}</h3>
                        <div className="flex items-center mt-1">
                          <CalendarIcon className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">
                            {format(new Date(event.date), 'EEE, MMM d')}
                          </span>
                        </div>
                        <div className="flex items-center mt-1">
                          <Clock className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">
                            {event.startTime} - {event.endTime}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No upcoming events in the next 7 days</p>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Monthly summary */}
          <Card className="mt-6">
            <CardHeader className="pb-3">
              <CardTitle>Month Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Total Events</span>
                  <span className="font-medium">{currentMonthEvents.length}</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-primary mr-2"></div>
                      Meetings
                    </span>
                    <span>{currentMonthEvents.filter(e => e.type === 'meeting').length}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-success mr-2"></div>
                      Academic
                    </span>
                    <span>{currentMonthEvents.filter(e => e.type === 'academic').length}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-warning mr-2"></div>
                      Sports
                    </span>
                    <span>{currentMonthEvents.filter(e => e.type === 'sports').length}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-destructive mr-2"></div>
                      Holidays
                    </span>
                    <span>{currentMonthEvents.filter(e => e.type === 'holiday').length}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-purple-500 mr-2"></div>
                      Cultural
                    </span>
                    <span>{currentMonthEvents.filter(e => e.type === 'cultural').length}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-blue-500 mr-2"></div>
                      Guidance
                    </span>
                    <span>{currentMonthEvents.filter(e => e.type === 'guidance').length}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Modals */}
      <AddEventModal 
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddEvent}
      />
      
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the event
              {selectedEvent && ` "${selectedEvent.title}"`} from the calendar.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteEvent}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}