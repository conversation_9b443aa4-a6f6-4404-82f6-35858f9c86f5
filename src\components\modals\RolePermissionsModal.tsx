import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Settings, Save } from 'lucide-react';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface RolePermissionsModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  role: string;
  permissions: string[];
}

const availablePermissions: Permission[] = [
  // Dashboard
  { id: 'view_dashboard', name: 'View Dashboard', description: 'Access to main dashboard', category: 'Dashboard' },
  
  // Users Management
  { id: 'view_teachers', name: 'View Teachers', description: 'View teachers list', category: 'Users' },
  { id: 'create_teachers', name: 'Create Teachers', description: 'Add new teachers', category: 'Users' },
  { id: 'edit_teachers', name: 'Edit Teachers', description: 'Modify teacher details', category: 'Users' },
  { id: 'delete_teachers', name: 'Delete Teachers', description: 'Remove teachers', category: 'Users' },
  { id: 'view_students', name: 'View Students', description: 'View students list', category: 'Users' },
  { id: 'create_students', name: 'Create Students', description: 'Add new students', category: 'Users' },
  { id: 'edit_students', name: 'Edit Students', description: 'Modify student details', category: 'Users' },
  { id: 'delete_students', name: 'Delete Students', description: 'Remove students', category: 'Users' },
  { id: 'view_parents', name: 'View Parents', description: 'View parents list', category: 'Users' },
  
  // Academics
  { id: 'view_classes', name: 'View Classes', description: 'View classes list', category: 'Academics' },
  { id: 'create_classes', name: 'Create Classes', description: 'Add new classes', category: 'Academics' },
  { id: 'view_courses', name: 'View Courses', description: 'View courses list', category: 'Academics' },
  { id: 'view_attendance', name: 'View Attendance', description: 'View attendance records', category: 'Academics' },
  { id: 'manage_attendance', name: 'Manage Attendance', description: 'Mark attendance', category: 'Academics' },
  { id: 'view_assessments', name: 'View Assessments', description: 'View assessment records', category: 'Academics' },
  { id: 'view_reports', name: 'View Reports', description: 'Access academic reports', category: 'Academics' },
  
  // Finance
  { id: 'view_fees', name: 'View Fees', description: 'View fee management', category: 'Finance' },
  { id: 'manage_fees', name: 'Manage Fees', description: 'Manage fee records', category: 'Finance' },
  { id: 'view_financial_records', name: 'View Financial Records', description: 'Access financial records', category: 'Finance' },
  
  // Administration
  { id: 'view_announcements', name: 'View Announcements', description: 'View announcements', category: 'Administration' },
  { id: 'create_announcements', name: 'Create Announcements', description: 'Create announcements', category: 'Administration' },
  { id: 'view_calendar', name: 'View Calendar', description: 'Access calendar', category: 'Administration' },
  { id: 'manage_school_settings', name: 'Manage School Settings', description: 'Modify school information', category: 'Administration' },
  { id: 'manage_users', name: 'Manage Account Users', description: 'Manage dashboard users', category: 'Administration' }
];

export function RolePermissionsModal({ open, onClose, onSubmit, role, permissions }: RolePermissionsModalProps) {
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(permissions);

  useEffect(() => {
    setSelectedPermissions(permissions);
  }, [permissions]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ role, permissions: selectedPermissions });
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions([...selectedPermissions, permissionId]);
    } else {
      setSelectedPermissions(selectedPermissions.filter(p => p !== permissionId));
    }
  };

  const groupedPermissions = availablePermissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {role} Permissions
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          {Object.entries(groupedPermissions).map(([category, perms]) => (
            <div key={category} className="space-y-3">
              <h3 className="font-semibold text-lg text-slate-800 border-b pb-2">{category}</h3>
              <div className="grid grid-cols-1 gap-3">
                {perms.map((permission) => (
                  <div key={permission.id} className="flex items-start space-x-3 p-3 bg-slate-50 rounded-lg">
                    <Checkbox
                      id={permission.id}
                      checked={selectedPermissions.includes(permission.id)}
                      onCheckedChange={(checked) => handlePermissionChange(permission.id, checked as boolean)}
                    />
                    <div className="flex-1">
                      <Label htmlFor={permission.id} className="font-medium cursor-pointer">
                        {permission.name}
                      </Label>
                      <p className="text-sm text-slate-600 mt-1">{permission.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
          
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              <Save className="h-4 w-4 mr-2" />
              Save Permissions
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}