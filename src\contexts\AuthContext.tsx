import React, { createContext, useContext, useState, useEffect } from "react";
import { User } from "@/types";
import { ROLES } from "@/lib/constants";
import { authAPI } from "@/lib/api";

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isSuperAdmin: boolean;
  isHeadmaster: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in by fetching current user
    fetchCurrentUser();
  }, []);

  const fetchCurrentUser = async () => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        setLoading(false);
        return;
      }

      const response = await authAPI.getCurrentUser();
      if (response.success) {
        const userData = response.data;

        // Map backend role to frontend role
        let role;
        if (userData.role === "SUPERADMIN") {
          role = ROLES.SUPER_ADMIN;
        } else if (userData.role === "HEADMASTER") {
          role = ROLES.HEADMASTER;
        } else {
          role = userData.role.toLowerCase();
        }

        const user: User = {
          id: userData.id,
          name: `${userData.firstName} ${userData.lastName}`,
          email: userData.email,
          role: role,
          school: userData.school,
          status: userData.active ? "active" : "inactive",
        };

        setUser(user);
      }
    } catch (error) {
      console.error("Failed to fetch user:", error);
      localStorage.removeItem("token");
      // User is not logged in or token is invalid
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<void> => {
    setLoading(true);

    try {
      const response = await authAPI.login(email, password);

      if (response.success) {
        const userData = response.data.user;

        // Map backend role to frontend role
        let role;
        if (userData.role === "SUPERADMIN") {
          role = ROLES.SUPER_ADMIN;
        } else if (userData.role === "HEADMASTER") {
          role = ROLES.HEADMASTER;
        } else {
          role = userData.role.toLowerCase();
        }

        const user: User = {
          id: userData.id,
          name: `${userData.firstName} ${userData.lastName}`,
          email: userData.email,
          role: role,
          school: userData.school,
          status: userData.active ? "active" : "inactive",
        };

        setUser(user);
      } else {
        throw new Error(response.message || "Login failed");
      }
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setUser(null);
    }
  };

  const isSuperAdmin = user?.role === ROLES.SUPER_ADMIN;
  const isHeadmaster = user?.role === ROLES.HEADMASTER;

  const value = {
    user,
    loading,
    login,
    logout,
    isSuperAdmin,
    isHeadmaster,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
