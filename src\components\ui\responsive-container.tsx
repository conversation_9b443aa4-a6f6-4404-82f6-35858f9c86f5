import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export function ResponsiveContainer({
  children,
  className,
  ...props
}: ResponsiveContainerProps) {
  return (
    <div 
      className={cn(
        "w-full overflow-x-auto table-container", 
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}