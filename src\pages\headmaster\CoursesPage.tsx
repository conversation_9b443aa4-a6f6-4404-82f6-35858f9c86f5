import React, { useState, useEffect, useCallback } from "react";
import { Layout } from "@/components/common/Layout";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  MoreHorizontal,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Download,
  Filter,
  ArrowUpDown,
  BookOpen,
  Clock,
  Calendar,
  Users,
  Briefcase,
  User,
  Loader2,
  GraduationCap,
  TrendingUp,
  Award,
  Target,
  Zap,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { AddCourseModal, AddCourseFormData } from "@/components/modals/AddCourseModal";
import { EditCourseModal, EditCourseFormData } from "@/components/modals/EditCourseModal";
import { ViewCourseModal } from "@/components/modals/ViewCourseModal";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { headmasterAPI } from "@/lib/api";
import { showToast } from "@/lib/utils";

// Mock course data
const MOCK_COURSE_DATA = [
  {
    id: "1",
    name: "Mathematics 101",
    code: "MATH101",
    teacher: "John Smith",
    department: "Mathematics",
    credits: 4,
    students: 32,
    schedule: "Mon, Wed, Fri 9:00 AM",
    status: "active",
  },
  {
    id: "2",
    name: "English Literature",
    code: "ENG201",
    teacher: "Sarah Johnson",
    department: "English",
    credits: 3,
    students: 28,
    schedule: "Tue, Thu 10:30 AM",
    status: "active",
  },
  {
    id: "3",
    name: "Biology",
    code: "BIO101",
    teacher: "Michael Brown",
    department: "Science",
    credits: 4,
    students: 30,
    schedule: "Mon, Wed 1:00 PM",
    status: "active",
  },
  {
    id: "4",
    name: "World History",
    code: "HIST101",
    teacher: "Emma Davis",
    department: "History",
    credits: 3,
    students: 25,
    schedule: "Tue, Thu 2:30 PM",
    status: "active",
  },
  {
    id: "5",
    name: "Physical Education",
    code: "PE101",
    teacher: "Robert Wilson",
    department: "Physical Education",
    credits: 2,
    students: 40,
    schedule: "Fri 11:00 AM",
    status: "inactive",
  },
  {
    id: "6",
    name: "Chemistry",
    code: "CHEM101",
    teacher: "Jennifer Lee",
    department: "Science",
    credits: 4,
    students: 26,
    schedule: "Mon, Wed 3:00 PM",
    status: "active",
  },
];

export function CoursesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [courses, setCourses] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<any>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });

  // Filter states
  const [departmentFilter, setDepartmentFilter] = useState<string[]>([]);
  const [teacherFilter, setTeacherFilter] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: "asc" | "desc" } | null>(null);

  const fetchCourses = useCallback(async () => {
    try {
      setLoading(true);
      const params: any = {
        page: pagination.page,
        limit: pagination.limit,
        search: searchQuery,
      };

      const response = await headmasterAPI.getCourses(params);
      if (response.success) {
        setCourses(Array.isArray(response.data.data) ? response.data.data : []);
        setPagination((prev) => ({
          ...prev,
          total: response.data.total || 0,
        }));
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
      showToast.error("Error", "Failed to load courses");
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit, searchQuery]);

  useEffect(() => {
    fetchCourses();
  }, [fetchCourses]);

  // Get unique departments and teachers for filter
  const allDepartments = [...new Set((courses || []).map((course) => course.department))];
  const allTeachers = [...new Set((courses || []).map((course) => course.teacher))];

  const filteredCourses = (courses || []).filter((course) => {
    // Text search filter
    const matchesSearch =
      course.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.teacher.toLowerCase().includes(searchQuery.toLowerCase());

    // Status filter from tabs
    const matchesStatus =
      activeTab === "all" ||
      (activeTab === "active" && course.status === "active") ||
      (activeTab === "inactive" && course.status === "inactive");

    // Department filter
    const matchesDepartment = departmentFilter.length === 0 || departmentFilter.includes(course.department);

    // Teacher filter
    const matchesTeacher = teacherFilter.length === 0 || teacherFilter.includes(course.teacher);

    return matchesSearch && matchesStatus && matchesDepartment && matchesTeacher;
  });

  // Apply sorting
  const sortedCourses = React.useMemo(() => {
    if (!sortConfig) return filteredCourses;

    return [...filteredCourses].sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });
  }, [filteredCourses, sortConfig]);

  // Calculate summary statistics
  const activeCourses = courses.filter((course) => course.status === "active").length;
  const inactiveCourses = courses.filter((course) => course.status === "inactive").length;
  const totalStudentsEnrolled = courses.reduce((sum, course) => sum + course.students, 0);
  const departments = [...new Set(courses.map((course) => course.department))].length;

  const handleAddCourse = async (data: AddCourseFormData) => {
    try {
      const response = await headmasterAPI.createCourse({
        ...data,
        credits: parseInt(data.credits),
      });
      if (response.success) {
        showToast.success("Success", "Course added successfully");
        fetchCourses();
        setIsAddModalOpen(false);
      }
    } catch (error) {
      console.error("Error adding course:", error);
      showToast.error("Error", "Failed to add course");
    }
  };

  const handleViewCourse = (course: any) => {
    setSelectedCourse(course);
    setIsViewModalOpen(true);
  };

  const handleEditCourse = (course: any) => {
    setSelectedCourse(course);
    setIsEditModalOpen(true);
  };

  const handleUpdateCourse = async (data: EditCourseFormData) => {
    if (!selectedCourse) return;
    try {
      const updateData: any = {
        ...data,
        credits: parseInt(data.credits),
      };

      if (!data.teacher || data.teacher.trim() === "") {
        delete updateData.teacher;
      }

      const response = await headmasterAPI.updateCourse(selectedCourse.id, updateData);
      if (response.success) {
        showToast.success("Success", "Course updated successfully");
        fetchCourses();
        setIsEditModalOpen(false);
      }
    } catch (error) {
      console.error("Error updating course:", error);
      showToast.error("Error", "Failed to update course");
    }
  };

  const handleDeleteCourse = (course: any) => {
    setSelectedCourse(course);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteCourse = async () => {
    if (!selectedCourse) return;
    try {
      const response = await headmasterAPI.deleteCourse(selectedCourse.id);
      if (response.success) {
        showToast.success("Success", "Course deleted successfully");
        fetchCourses();
        setIsDeleteDialogOpen(false);
      }
    } catch (error) {
      console.error("Error deleting course:", error);
      showToast.error("Error", "Failed to delete course");
    }
  };

  const handleSort = (key: string) => {
    let direction: "asc" | "desc" = "asc";

    if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }

    setSortConfig({ key, direction });
  };

  const clearFilters = () => {
    setDepartmentFilter([]);
    setTeacherFilter([]);
    setSortConfig(null);
    setActiveTab("all");
    setSearchQuery("");
  };

  return (
    <Layout>
      {/* Enhanced Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-indigo-900 via-blue-800 to-cyan-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-40 h-40 border border-white/10 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>

        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Course Catalog</h1>
                <p className="text-indigo-200 mt-1">Manage academic courses and curriculum</p>
              </div>
            </div>
            <p className="text-indigo-100 text-lg max-w-2xl">
              Comprehensive course management with curriculum planning and academic tracking tools.
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Target className="h-4 w-4 mr-2" />
              <span>Curriculum</span>
            </Button>
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-white text-indigo-900 hover:bg-indigo-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>Add Course</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Courses</p>
                <p className="text-3xl font-bold text-blue-900">{loading ? "..." : courses.length.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <div className="flex items-center text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-green-600 font-medium">{activeCourses} active</span>
                    <span className="text-gray-500 mx-2">•</span>
                    <span className="text-gray-600">{inactiveCourses} inactive</span>
                  </div>
                </div>
              </div>
              <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-700">Departments</p>
                <p className="text-3xl font-bold text-emerald-900">{loading ? "..." : departments.toLocaleString()}</p>
                <p className="text-sm text-emerald-600 mt-1">Academic departments</p>
              </div>
              <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Total Enrollments</p>
                <p className="text-3xl font-bold text-purple-900">
                  {loading ? "..." : totalStudentsEnrolled.toLocaleString()}
                </p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-purple-600 font-medium">Students enrolled</span>
                </div>
              </div>
              <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700">Avg Class Size</p>
                <p className="text-3xl font-bold text-orange-900">
                  {activeCourses > 0 ? Math.round(totalStudentsEnrolled / activeCourses) : 0}
                </p>
                <p className="text-sm text-orange-600 mt-1">Students per course</p>
              </div>
              <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Award className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl hover:shadow-2xl transition-all duration-300">
        <div className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <Search className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-slate-900">Course Catalog</h3>
                <p className="text-sm text-slate-600">Search and manage academic courses</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search courses..."
                  className="pl-10 w-64 border-slate-200 focus:border-indigo-500 focus:ring-indigo-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="p-4 border-b border-slate-100/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-2">
            <Tabs defaultValue={activeTab} value={activeTab} className="w-full sm:w-auto" onValueChange={setActiveTab}>
              <TabsList className="bg-slate-100 border-slate-200">
                <TabsTrigger value="all" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                  All
                </TabsTrigger>
                <TabsTrigger
                  value="active"
                  className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
                >
                  Active
                </TabsTrigger>
                <TabsTrigger
                  value="inactive"
                  className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
                >
                  Inactive
                </TabsTrigger>
              </TabsList>
            </Tabs>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={`flex items-center gap-1 border-slate-200 hover:bg-slate-50 ${
                    departmentFilter.length > 0 || teacherFilter.length > 0
                      ? "bg-indigo-50 border-indigo-200 text-indigo-700"
                      : ""
                  }`}
                >
                  <Filter className="h-4 w-4" />
                  <span>Filter</span>
                  {(departmentFilter.length > 0 || teacherFilter.length > 0) && (
                    <Badge variant="secondary" className="ml-1 h-5 px-1.5 bg-indigo-600 text-white">
                      {departmentFilter.length + teacherFilter.length}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl" align="end">
                <div className="p-4 border-b">
                  <h4 className="font-medium">Filters</h4>
                  <p className="text-sm text-muted-foreground">Filter courses by department and teacher</p>
                </div>
                <div className="p-4 space-y-4">
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium">Department</h5>
                    <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                      {allDepartments.map((department) => (
                        <div key={department} className="flex items-center space-x-2">
                          <Checkbox
                            id={`department-${department}`}
                            checked={departmentFilter.includes(department)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setDepartmentFilter([...departmentFilter, department]);
                              } else {
                                setDepartmentFilter(departmentFilter.filter((d) => d !== department));
                              }
                            }}
                          />
                          <Label htmlFor={`department-${department}`} className="text-sm">
                            {department}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h5 className="text-sm font-medium">Teacher</h5>
                    <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                      {allTeachers.map((teacher) => (
                        <div key={teacher} className="flex items-center space-x-2">
                          <Checkbox
                            id={`teacher-${teacher}`}
                            checked={teacherFilter.includes(teacher)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setTeacherFilter([...teacherFilter, teacher]);
                              } else {
                                setTeacherFilter(teacherFilter.filter((t) => t !== teacher));
                              }
                            }}
                          />
                          <Label htmlFor={`teacher-${teacher}`} className="text-sm">
                            {teacher}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="p-4 border-t flex justify-between">
                  <Button variant="outline" size="sm" onClick={clearFilters}>
                    Clear Filters
                  </Button>
                  <Button size="sm" className="gradient-bg text-white hover:opacity-90">
                    Apply Filters
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="w-[250px]">
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort("name")}>
                    Course Name
                    <ArrowUpDown className="h-3 w-3" />
                    {sortConfig?.key === "name" &&
                      (sortConfig.direction === "asc" ? (
                        <span className="text-xs">↑</span>
                      ) : (
                        <span className="text-xs">↓</span>
                      ))}
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort("code")}>
                    Code
                    <ArrowUpDown className="h-3 w-3" />
                    {sortConfig?.key === "code" &&
                      (sortConfig.direction === "asc" ? (
                        <span className="text-xs">↑</span>
                      ) : (
                        <span className="text-xs">↓</span>
                      ))}
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort("teacher")}>
                    Teacher
                    <ArrowUpDown className="h-3 w-3" />
                    {sortConfig?.key === "teacher" &&
                      (sortConfig.direction === "asc" ? (
                        <span className="text-xs">↑</span>
                      ) : (
                        <span className="text-xs">↓</span>
                      ))}
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort("department")}>
                    Department
                    <ArrowUpDown className="h-3 w-3" />
                    {sortConfig?.key === "department" &&
                      (sortConfig.direction === "asc" ? (
                        <span className="text-xs">↑</span>
                      ) : (
                        <span className="text-xs">↓</span>
                      ))}
                  </div>
                </TableHead>
                <TableHead>Credits</TableHead>
                <TableHead>Students</TableHead>
                <TableHead>Schedule</TableHead>
                <TableHead>
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort("status")}>
                    Status
                    <ArrowUpDown className="h-3 w-3" />
                    {sortConfig?.key === "status" &&
                      (sortConfig.direction === "asc" ? (
                        <span className="text-xs">↑</span>
                      ) : (
                        <span className="text-xs">↓</span>
                      ))}
                  </div>
                </TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading courses...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : sortedCourses.length > 0 ? (
                sortedCourses.map((course) => (
                  <TableRow key={course.id} className="hover:bg-slate-50/50 transition-colors">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="bg-indigo-100 p-2 rounded-lg">
                          <BookOpen className="h-4 w-4 text-indigo-600" />
                        </div>
                        <div>
                          <div className="font-semibold text-slate-900">{course.name}</div>
                          <div className="text-xs text-slate-500 mt-1">{course.code}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200">
                        {course.code}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-emerald-600" />
                        <span className="font-medium text-slate-900">{course.teacher}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Briefcase className="h-4 w-4 text-purple-600" />
                        <span className="font-medium text-slate-900">{course.department}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Zap className="h-4 w-4 text-orange-600" />
                        <span className="font-medium text-slate-900">{course.credits}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-teal-600" />
                        <span className="font-medium text-slate-900">{course.students || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-amber-600" />
                        <span className="font-medium text-slate-900 text-sm">{course.schedule || "Not scheduled"}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={course.active ? "default" : "secondary"}
                        className={
                          course.active
                            ? "bg-green-100 text-green-800 border-green-200 hover:bg-green-200"
                            : "bg-gray-100 text-gray-800 border-gray-200"
                        }
                      >
                        <div
                          className={`w-2 h-2 rounded-full mr-2 ${course.active ? "bg-green-500" : "bg-gray-400"}`}
                        ></div>
                        {course.active ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="w-56 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl"
                        >
                          <DropdownMenuItem
                            className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                            onClick={() => handleViewCourse(course)}
                          >
                            <div className="bg-blue-100 p-1.5 rounded-lg">
                              <Eye className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <span className="font-medium">View Details</span>
                              <p className="text-xs text-slate-500">See full information</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                            onClick={() => handleEditCourse(course)}
                          >
                            <div className="bg-emerald-100 p-1.5 rounded-lg">
                              <Edit className="h-4 w-4 text-emerald-600" />
                            </div>
                            <div>
                              <span className="font-medium">Edit Course</span>
                              <p className="text-xs text-slate-500">Modify course details</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-slate-200" />
                          <DropdownMenuItem
                            className="flex items-center gap-3 text-red-600 focus:text-red-600 cursor-pointer hover:bg-red-50 p-3"
                            onClick={() => handleDeleteCourse(course)}
                          >
                            <div className="bg-red-100 p-1.5 rounded-lg">
                              <Trash2 className="h-4 w-4 text-red-600" />
                            </div>
                            <div>
                              <span className="font-medium">Delete Course</span>
                              <p className="text-xs text-red-500">Remove permanently</p>
                            </div>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                    No courses found matching your search criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{sortedCourses.length}</span> of{" "}
            <span className="font-medium">{courses.length}</span> courses
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              Previous
            </Button>
            <Button variant="outline" size="sm" disabled>
              Next
            </Button>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <AddCourseModal open={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} onSubmit={handleAddCourse} />

      {selectedCourse && (
        <>
          <ViewCourseModal open={isViewModalOpen} onClose={() => setIsViewModalOpen(false)} course={selectedCourse} />

          <EditCourseModal
            open={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSubmit={handleUpdateCourse}
            course={selectedCourse}
          />
        </>
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the course
              {selectedCourse && ` ${selectedCourse.name}`} from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteCourse}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}
