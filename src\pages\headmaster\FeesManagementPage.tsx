import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { 
  Search,
  Users,
  TrendingUp,
  DollarSign,
  CreditCard,
  Edit,
  MoreHorizontal,
  Plus,
  Download,
  Filter,
  Loader2
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import { AddPaymentModal } from '@/components/modals/AddPaymentModal';
import { EditPaymentModal } from '@/components/modals/EditPaymentModal';

interface FeeRecord {
  id: string;
  receiptId: string;
  studentName: string;
  studentClass: string;
  amountPaid: number;
  paymentDate: string;
  paymentMode: string;
  feeType: string;
  paidBy: string;
  payerPhone: string;
}

export function FeesManagementPage() {
  const [loading, setLoading] = useState(true);
  const [feeRecords, setFeeRecords] = useState<FeeRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<FeeRecord[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTerm, setSelectedTerm] = useState('all');
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedYear, setSelectedYear] = useState('2024/2025');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<FeeRecord | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });

  useEffect(() => {
    fetchClasses();
    fetchFeeRecords();
  }, [selectedTerm, selectedClass, selectedYear]);

  useEffect(() => {
    filterRecords();
  }, [feeRecords, searchQuery]);

  const fetchClasses = async () => {
    try {
      const response = await headmasterAPI.getClasses();
      if (response.success) {
        setClasses(response.data.classes);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchFeeRecords = async () => {
    try {
      setLoading(true);
      // Mock data for now - in real app, this would call an API
      const mockRecords: FeeRecord[] = [
        {
          id: '1',
          receiptId: 'RCP001',
          studentName: 'John Smith',
          studentClass: 'Class 10A',
          amountPaid: 1500.00,
          paymentDate: '2024-01-15',
          paymentMode: 'Cash',
          feeType: 'Tuition Fee',
          paidBy: 'James Smith',
          payerPhone: '+233 24 123 4567'
        },
        {
          id: '2',
          receiptId: 'RCP002',
          studentName: 'Sarah Johnson',
          studentClass: 'Class 9B',
          amountPaid: 1200.00,
          paymentDate: '2024-01-16',
          paymentMode: 'Mobile Money',
          feeType: 'School Fees',
          paidBy: 'Mary Johnson',
          payerPhone: '+233 20 987 6543'
        },
        {
          id: '3',
          receiptId: 'RCP003',
          studentName: 'Michael Brown',
          studentClass: 'Class 11C',
          amountPaid: 800.00,
          paymentDate: '2024-01-17',
          paymentMode: 'Bank Transfer',
          feeType: 'Examination Fee',
          paidBy: 'Robert Brown',
          payerPhone: '+233 26 456 7890'
        },
        {
          id: '4',
          receiptId: 'RCP004',
          studentName: 'Emma Davis',
          studentClass: 'Class 12A',
          amountPaid: 2000.00,
          paymentDate: '2024-01-18',
          paymentMode: 'Cash',
          feeType: 'Tuition Fee',
          paidBy: 'David Davis',
          payerPhone: '+233 24 789 0123'
        },
        {
          id: '5',
          receiptId: 'RCP005',
          studentName: 'Daniel Wilson',
          studentClass: 'Class 10B',
          amountPaid: 950.00,
          paymentDate: '2024-01-19',
          paymentMode: 'Mobile Money',
          feeType: 'Activity Fee',
          paidBy: 'Lisa Wilson',
          payerPhone: '+233 27 234 5678'
        }
      ];
      
      setFeeRecords(mockRecords);
    } catch (error) {
      console.error('Error fetching fee records:', error);
      showToast.error('Error', 'Failed to load fee records');
    } finally {
      setLoading(false);
    }
  };

  const filterRecords = () => {
    let filtered = feeRecords;

    if (searchQuery) {
      filtered = filtered.filter(record =>
        record.studentName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.receiptId.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Update pagination total
    setPagination(prev => ({ ...prev, total: filtered.length }));
    
    // Apply pagination
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    const paginatedRecords = filtered.slice(startIndex, endIndex);
    
    setFilteredRecords(paginatedRecords);
  };

  const handleEditRecord = (record: FeeRecord) => {
    setSelectedRecord(record);
    setIsEditModalOpen(true);
  };

  const handleAddPayment = (data: any) => {
    const newRecord: FeeRecord = {
      id: (feeRecords.length + 1).toString(),
      ...data
    };
    setFeeRecords([...feeRecords, newRecord]);
    setIsAddModalOpen(false);
    showToast.success('Success', 'Payment record added successfully');
  };

  const handleUpdatePayment = (data: any) => {
    if (!selectedRecord) return;
    const updatedRecords = feeRecords.map(record => 
      record.id === selectedRecord.id ? { ...record, ...data } : record
    );
    setFeeRecords(updatedRecords);
    setIsEditModalOpen(false);
    showToast.success('Success', 'Payment record updated successfully');
  };

  // Calculate summary statistics
  const totalRecords = filteredRecords.length;
  const totalAmount = filteredRecords.reduce((sum, record) => sum + record.amountPaid, 0);
  const averagePayment = totalRecords > 0 ? totalAmount / totalRecords : 0;
  const cashPayments = filteredRecords.filter(r => r.paymentMode === 'Cash').length;

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-green-900 via-emerald-800 to-teal-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Fees Management</h1>
                <p className="text-green-200 mt-1">Track and manage student fee payments</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-white text-green-900 hover:bg-green-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>Add Payment</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Total Records</p>
              <p className="text-3xl font-bold text-blue-900">{totalRecords}</p>
            </div>
            <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-emerald-700">Total Amount</p>
              <p className="text-3xl font-bold text-emerald-900">GHS {totalAmount.toLocaleString()}</p>
            </div>
            <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-700">Average Payment</p>
              <p className="text-3xl font-bold text-orange-900">GHS {averagePayment.toFixed(0)}</p>
            </div>
            <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700">Cash Payments</p>
              <p className="text-3xl font-bold text-purple-900">{cashPayments}</p>
            </div>
            <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <CreditCard className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <CreditCard className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">Fee Payment Records</CardTitle>
                <p className="text-sm text-slate-600">Track and manage student fee payments</p>
              </div>
            </div>
          </div>
        </CardHeader>
        
        {/* Filters */}
        <div className="p-6 border-b border-slate-100/50 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search by student name or receipt ID..."
              className="pl-10 border-slate-200 focus:border-green-500 focus:ring-green-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Select value={selectedTerm} onValueChange={setSelectedTerm}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Select Term" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Terms</SelectItem>
              <SelectItem value="first">First Term</SelectItem>
              <SelectItem value="second">Second Term</SelectItem>
              <SelectItem value="third">Third Term</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Select Class" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Classes</SelectItem>
              {classes.map((cls) => (
                <SelectItem key={cls.id} value={cls.id}>
                  {cls.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={selectedYear} onValueChange={setSelectedYear}>
            <SelectTrigger className="w-[150px] border-slate-200">
              <SelectValue placeholder="Academic Year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2024/2025">2024/2025</SelectItem>
              <SelectItem value="2023/2024">2023/2024</SelectItem>
              <SelectItem value="2022/2023">2022/2023</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Fee Records Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead>Receipt ID</TableHead>
                <TableHead>Student Name</TableHead>
                <TableHead>Student Class</TableHead>
                <TableHead>Amount Paid (GHS)</TableHead>
                <TableHead>Payment Date</TableHead>
                <TableHead>Payment Mode</TableHead>
                <TableHead>Fee Type</TableHead>
                <TableHead>Paid By</TableHead>
                <TableHead>Payer's Phone</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading fee records...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredRecords.length > 0 ? (
                filteredRecords.map((record) => (
                  <TableRow key={record.id} className="hover:bg-slate-50/50 transition-colors">
                    <TableCell>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                        {record.receiptId}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-semibold text-slate-900">{record.studentName}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
                        {record.studentClass}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-bold text-green-700">GHS {record.amountPaid.toLocaleString()}</div>
                    </TableCell>
                    <TableCell>
                      <span className="text-slate-600">{format(new Date(record.paymentDate), 'MMM d, yyyy')}</span>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`${
                          record.paymentMode === 'Cash' 
                            ? 'bg-green-100 text-green-800 border-green-200'
                            : record.paymentMode === 'Mobile Money'
                            ? 'bg-orange-100 text-orange-800 border-orange-200'
                            : 'bg-blue-100 text-blue-800 border-blue-200'
                        }`}
                      >
                        {record.paymentMode}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-slate-700">{record.feeType}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-slate-700">{record.paidBy}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-slate-600">{record.payerPhone}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem 
                            className="flex items-center gap-2 cursor-pointer"
                            onClick={() => handleEditRecord(record)}
                          >
                            <Edit className="h-4 w-4" />
                            <span>Edit Record</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8 text-muted-foreground">
                    No fee records found matching your criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Pagination */}
        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{Math.min((pagination.page - 1) * pagination.limit + 1, pagination.total)}</span> to{" "}
            <span className="font-medium">{Math.min(pagination.page * pagination.limit, pagination.total)}</span> of{" "}
            <span className="font-medium">{pagination.total}</span> records
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page * pagination.limit >= pagination.total}
            >
              Next
            </Button>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <AddPaymentModal 
        open={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddPayment}
      />
      
      {selectedRecord && (
        <EditPaymentModal
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={handleUpdatePayment}
          payment={selectedRecord}
        />
      )}
    </Layout>
  );
}