import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface EditPaymentModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  payment: any;
}

export function EditPaymentModal({ open, onClose, onSubmit, payment }: EditPaymentModalProps) {
  const [formData, setFormData] = useState({
    receiptId: '',
    studentName: '',
    studentClass: '',
    amountPaid: '',
    paymentDate: '',
    paymentMode: '',
    feeType: '',
    paidBy: '',
    payerPhone: ''
  });

  useEffect(() => {
    if (payment) {
      setFormData({
        receiptId: payment.receiptId || '',
        studentName: payment.studentName || '',
        studentClass: payment.studentClass || '',
        amountPaid: payment.amountPaid?.toString() || '',
        paymentDate: payment.paymentDate || '',
        paymentMode: payment.paymentMode || '',
        feeType: payment.feeType || '',
        paidBy: payment.paidBy || '',
        payerPhone: payment.payerPhone || ''
      });
    }
  }, [payment]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      amountPaid: parseFloat(formData.amountPaid)
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Payment Record</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="receiptId">Receipt ID</Label>
            <Input
              id="receiptId"
              value={formData.receiptId}
              onChange={(e) => setFormData({...formData, receiptId: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="studentName">Student Name</Label>
            <Input
              id="studentName"
              value={formData.studentName}
              onChange={(e) => setFormData({...formData, studentName: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="studentClass">Student Class</Label>
            <Input
              id="studentClass"
              value={formData.studentClass}
              onChange={(e) => setFormData({...formData, studentClass: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="amountPaid">Amount Paid (GHS)</Label>
            <Input
              id="amountPaid"
              type="number"
              step="0.01"
              value={formData.amountPaid}
              onChange={(e) => setFormData({...formData, amountPaid: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="paymentDate">Payment Date</Label>
            <Input
              id="paymentDate"
              type="date"
              value={formData.paymentDate}
              onChange={(e) => setFormData({...formData, paymentDate: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="paymentMode">Payment Mode</Label>
            <Select value={formData.paymentMode} onValueChange={(value) => setFormData({...formData, paymentMode: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select payment mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Cash">Cash</SelectItem>
                <SelectItem value="Mobile Money">Mobile Money</SelectItem>
                <SelectItem value="Bank Transfer">Bank Transfer</SelectItem>
                <SelectItem value="Cheque">Cheque</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="feeType">Fee Type</Label>
            <Select value={formData.feeType} onValueChange={(value) => setFormData({...formData, feeType: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select fee type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Tuition Fee">Tuition Fee</SelectItem>
                <SelectItem value="School Fees">School Fees</SelectItem>
                <SelectItem value="Examination Fee">Examination Fee</SelectItem>
                <SelectItem value="Activity Fee">Activity Fee</SelectItem>
                <SelectItem value="Library Fee">Library Fee</SelectItem>
                <SelectItem value="Transport Fee">Transport Fee</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="paidBy">Paid By</Label>
            <Input
              id="paidBy"
              value={formData.paidBy}
              onChange={(e) => setFormData({...formData, paidBy: e.target.value})}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="payerPhone">Payer's Phone</Label>
            <Input
              id="payerPhone"
              value={formData.payerPhone}
              onChange={(e) => setFormData({...formData, payerPhone: e.target.value})}
              placeholder="+233 XX XXX XXXX"
              required
            />
          </div>
          
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              Update Payment
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}