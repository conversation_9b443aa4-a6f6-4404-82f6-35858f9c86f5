import { useState, useEffect, useMemo, useCallback } from "react";
import { Layout } from "@/components/common/Layout";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  MoreHorizontal,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Download,
  Filter,
  Loader2,
  ArrowUpDown,
  Check,
  X,
  GraduationCap,
  Users,
  BookOpen,
  TrendingUp,
  Mail,
  Phone,
  Calendar,
  Award,
} from "lucide-react";
import { format } from "date-fns";
import { Card } from "@/components/ui/card";
import { headmasterAPI } from "@/lib/api";
import { showToast } from "@/lib/utils";
import type { Teacher, TeacherFormData, SortConfig, RequestParams, ApiResponse } from "@/types";
import { ViewTeacherModal } from "@/components/modals/ViewTeacherModal";
import { EditTeacherModal } from "@/components/modals/EditTeacherModal";
import { AddTeacherModal } from "@/components/modals/AddTeacherModal";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export function TeachersPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });
  const [sortConfig, setSortConfig] = useState<SortConfig<Teacher> | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [subjectFilter, setSubjectFilter] = useState<string>("all");
  const [subjects, setSubjects] = useState<string[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null);

  const fetchTeachers = useCallback(async () => {
    try {
      setLoading(true);
      const params: RequestParams = {
        page: pagination.page,
        limit: pagination.limit,
        search: searchQuery,
      };

      // Add filters to params
      if (statusFilter !== "all") {
        params.status = statusFilter;
      }
      if (subjectFilter !== "all") {
        params.subject = subjectFilter;
      }

      console.log("Fetching teachers with params:", params);
      const response = await headmasterAPI.getTeachers(params);
      console.log("Teachers API response:", response);

      if (response.success) {
        // Backend returns data directly in response.data, not response.data.data
        const teachersData = Array.isArray(response.data) ? response.data : [];
        console.log("Teachers API success - received", teachersData.length, "teachers");
        setTeachers(teachersData);
        setPagination((prev) => ({
          ...prev,
          total: response.total || 0,
        }));

        // Extract unique subjects for filter
        const uniqueSubjects = [...new Set(teachersData.map((t: Teacher) => t.subject).filter(Boolean))];
        setSubjects(uniqueSubjects);
      } else {
        console.error("Teachers API failed:", response);
      }
    } catch (error) {
      console.error("Error fetching teachers:", error);
      showToast.error("Error", "Failed to load teachers");
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit, searchQuery, statusFilter, subjectFilter]);

  useEffect(() => {
    fetchTeachers();
  }, [fetchTeachers]);

  const handleSort = (key: keyof Teacher) => {
    setSortConfig((current) => {
      if (!current || current.key !== key) {
        return { key, direction: "asc" };
      }
      if (current.direction === "asc") {
        return { key, direction: "desc" };
      }
      return null;
    });
  };

  const handleSearch = () => {
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
  };

  const resetFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
    setSubjectFilter("all");
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const hasActiveFilters = searchQuery || statusFilter !== "all" || subjectFilter !== "all";

  const handleExport = async () => {
    try {
      const params = {
        search: searchQuery,
        status: statusFilter !== "all" ? statusFilter : undefined,
        subject: subjectFilter !== "all" ? subjectFilter : undefined,
      };

      // Create CSV content
      const csvHeaders = ["Name", "Email", "Subject", "Phone", "Status", "Join Date"];
      const csvRows = [csvHeaders.join(",")];

      teachers.forEach((teacher) => {
        const row = [
          `"${teacher.name}"`,
          `"${teacher.email}"`,
          `"${teacher.subject}"`,
          `"${teacher.phone}"`,
          teacher.status,
          new Date(teacher.joinDate).toLocaleDateString(),
        ];
        csvRows.push(row.join(","));
      });

      const csvContent = csvRows.join("\n");
      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `teachers-${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showToast.success("Export Complete", "Teachers data exported successfully");
    } catch (error) {
      showToast.error("Export Failed", "Failed to export teachers data");
    }
  };

  const handlePrevPage = () => {
    if (pagination.page > 1) {
      setPagination((prev) => ({
        ...prev,
        page: prev.page - 1,
      }));
    }
  };

  const handleNextPage = () => {
    if (pagination.page * pagination.limit < pagination.total) {
      setPagination((prev) => ({
        ...prev,
        page: prev.page + 1,
      }));
    }
  };

  const sortedTeachers = useMemo(() => {
    const teachersArray = teachers || [];
    if (!sortConfig) return teachersArray;

    return [...teachersArray].sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });
  }, [teachers, sortConfig]);

  // Handlers
  const handleViewTeacher = (teacher: Teacher) => {
    setSelectedTeacher(teacher);
    setIsViewModalOpen(true);
  };

  const handleEditTeacher = (teacher: Teacher) => {
    setSelectedTeacher(teacher);
    setIsEditModalOpen(true);
  };

  const handleEditSubmit = async (data: TeacherFormData) => {
    if (!selectedTeacher) return;

    try {
      const response = await headmasterAPI.updateTeacher(selectedTeacher.id, data);
      if (response.success) {
        showToast.success("Success", "Teacher updated successfully");
        fetchTeachers();
        setIsEditModalOpen(false);
      }
    } catch (error) {
      console.error("Error updating teacher:", error);
      showToast.error("Error", "Failed to update teacher");
    }
  };

  const handleDeleteClick = (teacher: Teacher) => {
    setSelectedTeacher(teacher);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedTeacher) return;

    try {
      const response = await headmasterAPI.deleteTeacher(selectedTeacher.id);
      if (response.success) {
        showToast.success("Success", "Teacher deleted successfully");
        fetchTeachers();
        setIsDeleteDialogOpen(false);
      }
    } catch (error) {
      console.error("Error deleting teacher:", error);
      showToast.error("Error", "Failed to delete teacher");
    }
  };

  const handleAddSubmit = async (data: TeacherFormData) => {
    try {
      console.log("Creating teacher with data:", data);
      const response = await headmasterAPI.createTeacher(data);
      console.log("Create teacher response:", response);
      if (response.success) {
        showToast.success("Success", "Teacher added successfully");
        fetchTeachers();
        setIsAddModalOpen(false);
      } else {
        console.error("Create teacher failed:", response);
        showToast.error("Error", response.message || "Failed to add teacher");
      }
    } catch (error) {
      console.error("Error adding teacher:", error);
      showToast.error("Error", "Failed to add teacher");
    }
  };

  return (
    <Layout>
      {/* Enhanced Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-40 h-40 border border-white/10 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>

        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Faculty Management</h1>
                <p className="text-purple-200 mt-1">Manage and oversee all teaching staff</p>
              </div>
            </div>
            <p className="text-purple-100 text-lg max-w-2xl">
              Comprehensive teacher management with performance tracking and professional development tools.
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Award className="h-4 w-4 mr-2" />
              <span>Performance</span>
            </Button>
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-white text-purple-900 hover:bg-purple-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>Add Teacher</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Teachers</p>
                <p className="text-3xl font-bold text-blue-900">{loading ? "..." : teachers.length.toLocaleString()}</p>
                <p className="text-sm text-blue-600 mt-1">Active faculty members</p>
              </div>
              <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-700">Active Teachers</p>
                <p className="text-3xl font-bold text-emerald-900">
                  {loading ? "..." : teachers.filter((t) => t.status === "active").length.toLocaleString()}
                </p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-emerald-600 font-medium">Currently teaching</span>
                </div>
              </div>
              <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Subjects Taught</p>
                <p className="text-3xl font-bold text-purple-900">
                  {loading ? "..." : subjects.length.toLocaleString()}
                </p>
                <p className="text-sm text-purple-600 mt-1">Different subjects</p>
              </div>
              <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700">Avg Experience</p>
                <p className="text-3xl font-bold text-orange-900">5.2</p>
                <p className="text-sm text-orange-600 mt-1">Years of teaching</p>
              </div>
              <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Award className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl hover:shadow-2xl transition-all duration-300">
        <div className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <Search className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-slate-900">Teacher Directory</h3>
                <p className="text-sm text-slate-600">Search and manage faculty members</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search teachers..."
                  className="pl-10 w-64 border-slate-200 focus:border-purple-500 focus:ring-purple-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="p-4 border-b border-slate-100/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={`flex items-center gap-1 ${hasActiveFilters ? "bg-primary/10 border-primary" : ""}`}
                >
                  <Filter className="h-4 w-4" />
                  <span>Filter</span>
                  {hasActiveFilters && (
                    <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1">•</span>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl">
                <div className="px-2 py-1.5 text-sm font-medium">Status</div>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === "all"}
                  onCheckedChange={() => setStatusFilter("all")}
                >
                  All Teachers
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === "active"}
                  onCheckedChange={() => setStatusFilter("active")}
                >
                  Active Only
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === "inactive"}
                  onCheckedChange={() => setStatusFilter("inactive")}
                >
                  Inactive Only
                </DropdownMenuCheckboxItem>
                <DropdownMenuSeparator />
                <div className="px-2 py-1.5 text-sm font-medium">Subject</div>
                <DropdownMenuCheckboxItem
                  checked={subjectFilter === "all"}
                  onCheckedChange={() => setSubjectFilter("all")}
                >
                  All Subjects
                </DropdownMenuCheckboxItem>
                {subjects.map((subject) => (
                  <DropdownMenuCheckboxItem
                    key={subject}
                    checked={subjectFilter === subject}
                    onCheckedChange={() => setSubjectFilter(subject)}
                  >
                    {subject}
                  </DropdownMenuCheckboxItem>
                ))}
                {hasActiveFilters && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={resetFilters} className="text-destructive">
                      <X className="h-4 w-4 mr-2" />
                      Reset Filters
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="outline" size="sm" className="flex items-center gap-1" onClick={handleExport}>
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="w-[250px]">
                  <Button variant="ghost" onClick={() => handleSort("name")} className="flex items-center gap-2">
                    Name
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort("email")} className="flex items-center gap-2">
                    Email
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort("subject")} className="flex items-center gap-2">
                    Subject
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>Classes</TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort("phone")} className="flex items-center gap-2">
                    Phone
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort("joinDate")} className="flex items-center gap-2">
                    Join Date
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {(() => {
                if (loading) {
                  return (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex justify-center items-center">
                          <Loader2 className="h-6 w-6 animate-spin mr-2" />
                          <span>Loading teachers...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                }

                if (!sortedTeachers || sortedTeachers.length === 0) {
                  return (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        No teachers found matching your search criteria
                      </TableCell>
                    </TableRow>
                  );
                }

                return sortedTeachers.map((teacher) => (
                  <TableRow key={teacher.id} className="hover:bg-slate-50/50 transition-colors">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="bg-purple-100 p-2 rounded-lg">
                          <GraduationCap className="h-4 w-4 text-purple-600" />
                        </div>
                        <div>
                          <div className="font-semibold text-slate-900">{`${teacher.firstName} ${teacher.lastName}`}</div>
                          <div className="flex items-center text-xs text-slate-500 mt-1">
                            <Mail className="h-3 w-3 mr-1" />
                            <span>{teacher.email}</span>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-slate-900">{teacher.email}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <BookOpen className="h-4 w-4 text-emerald-600" />
                          <span className="font-medium text-slate-900">{teacher.subject}</span>
                        </div>
                        <Badge
                          variant={teacher.status === "active" ? "default" : "secondary"}
                          className={
                            teacher.status === "active"
                              ? "bg-green-100 text-green-800 border-green-200 hover:bg-green-200"
                              : "bg-gray-100 text-gray-800 border-gray-200"
                          }
                        >
                          <div
                            className={`w-2 h-2 rounded-full mr-2 ${
                              teacher.status === "active" ? "bg-green-500" : "bg-gray-400"
                            }`}
                          ></div>
                          {teacher.status?.charAt(0).toUpperCase() + teacher.status?.slice(1)}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {teacher.classes && teacher.classes.length > 0 ? (
                          teacher.classes.slice(0, 2).map((className: string, index: number) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {className}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-sm text-muted-foreground">No classes</span>
                        )}
                        {teacher.classes && teacher.classes.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{teacher.classes.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-orange-600" />
                        <span className="font-medium text-slate-900">{teacher.phone}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2 text-slate-600">
                        <Calendar className="h-4 w-4" />
                        <span>{format(new Date(teacher.joinDate), "MMM d, yyyy")}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="w-56 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl"
                        >
                          <DropdownMenuItem
                            className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                            onSelect={() => handleViewTeacher(teacher)}
                          >
                            <div className="bg-blue-100 p-1.5 rounded-lg">
                              <Eye className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <span className="font-medium">View Details</span>
                              <p className="text-xs text-slate-500">See full information</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                            onSelect={() => handleEditTeacher(teacher)}
                          >
                            <div className="bg-emerald-100 p-1.5 rounded-lg">
                              <Edit className="h-4 w-4 text-emerald-600" />
                            </div>
                            <div>
                              <span className="font-medium">Edit Teacher</span>
                              <p className="text-xs text-slate-500">Modify teacher details</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-slate-200" />
                          <DropdownMenuItem
                            className="flex items-center gap-3 text-red-600 focus:text-red-600 cursor-pointer hover:bg-red-50 p-3"
                            onSelect={() => handleDeleteClick(teacher)}
                          >
                            <div className="bg-red-100 p-1.5 rounded-lg">
                              <Trash2 className="h-4 w-4 text-red-600" />
                            </div>
                            <div>
                              <span className="font-medium">Delete Teacher</span>
                              <p className="text-xs text-red-500">Remove permanently</p>
                            </div>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ));
              })()}
            </TableBody>
          </Table>
        </div>

        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{teachers.length}</span> of{" "}
            <span className="font-medium">{pagination.total}</span> teachers
            {hasActiveFilters && (
              <span className="ml-2">
                • Filtered
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetFilters}
                  className="ml-1 h-auto p-1 text-xs text-destructive hover:text-destructive"
                >
                  Clear
                </Button>
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handlePrevPage} disabled={pagination.page <= 1}>
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={pagination.page * pagination.limit >= pagination.total}
            >
              Next
            </Button>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <AddTeacherModal open={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} onSubmit={handleAddSubmit} />

      {selectedTeacher && (
        <>
          <ViewTeacherModal
            open={isViewModalOpen}
            onClose={() => setIsViewModalOpen(false)}
            teacher={selectedTeacher}
          />
          <EditTeacherModal
            open={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            teacher={selectedTeacher}
            onSubmit={handleEditSubmit}
          />
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this teacher? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteDialogOpen(false)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm} className="bg-red-500 text-white hover:bg-red-600">
              Delete Teacher
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}
