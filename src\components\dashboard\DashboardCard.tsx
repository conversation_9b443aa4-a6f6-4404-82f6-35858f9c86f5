import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { HelpCircle, TrendingDown, TrendingUp, Loader2, ArrowUpRight } from 'lucide-react';

interface DashboardCardProps {
  title: string;
  value: number;
  description: string;
  icon: React.ReactNode;
  color: 'primary' | 'success' | 'warning' | 'error';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  tooltipText?: string;
  loading?: boolean;
  onClick?: () => void;
}

export function DashboardCard({
  title,
  value,
  description,
  icon,
  color,
  trend,
  tooltipText,
  loading = false,
  onClick
}: DashboardCardProps) {
  const colorClasses = {
    primary: 'bg-primary/10 text-primary',
    success: 'bg-success/10 text-success',
    warning: 'bg-warning/10 text-warning',
    error: 'bg-error/10 text-error',
  };

  const trendColorClasses = {
    positive: 'text-success',
    negative: 'text-error',
  };

  const borderClasses = {
    primary: 'border-l-primary',
    success: 'border-l-success',
    warning: 'border-l-warning',
    error: 'border-l-error',
  };

  return (
    <Card 
      className={`hover-lift blue-card border-l-4 ${borderClasses[color]} overflow-hidden ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
              {tooltipText && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">{tooltipText}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            {loading ? (
              <div className="flex items-center h-8">
                <Loader2 className="h-5 w-5 animate-spin text-primary/50" />
              </div>
            ) : (
              <div className="text-2xl font-bold flex items-center">
                {typeof value === 'number' ? value.toLocaleString() : value}
                {trend && trend.isPositive && (
                  <ArrowUpRight className={`h-5 w-5 ml-1 ${trendColorClasses.positive}`} />
                )}
              </div>
            )}
            <p className="text-xs text-muted-foreground">
              {trend && (
                <span className={`inline-flex items-center mr-1 ${trend.isPositive ? trendColorClasses.positive : trendColorClasses.negative}`}>
                  {trend.isPositive ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {trend.value}%
                </span>
              )}
              {description}
            </p>
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            {icon}
          </div>
        </div>
        
        {/* Progress indicator */}
        <div className="mt-4 pt-4 border-t border-muted/30">
          <div className="w-full bg-muted/30 h-1.5 rounded-full overflow-hidden">
            <div 
              className={`h-full bg-${color} rounded-full`} 
              style={{ width: `${Math.min(100, value > 1000 ? 85 : (value / 10))}%` }}
            ></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}