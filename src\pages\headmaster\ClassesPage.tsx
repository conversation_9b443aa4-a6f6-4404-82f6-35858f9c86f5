import React, { useState, useEffect } from "react";
import { Layout } from "@/components/common/Layout";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  MoreHorizontal,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Users,
  Loader2,
  CheckCircle,
  XCircle,
  ArrowUpDown,
  Filter,
  X,
  Download,
  GraduationCap,
  TrendingUp,
  Award,
  Target,
  School as SchoolIcon,
  BookOpen,
  UserCheck
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { headmaster<PERSON><PERSON> } from "@/lib/api";
import { showToast } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AddClassModal } from "@/components/modals/AddClassModal";
import { ViewClassModal } from "@/components/modals/ViewClassModal";
import { EditClassModal } from "@/components/modals/EditClassModal";
import { ManageStudentsModal } from "@/components/modals/ManageStudentsModal";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Class } from "@/types";

interface SortConfig {
  key: keyof Class;
  direction: "asc" | "desc";
}

export function ClassesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [classes, setClasses] = useState<Class[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isManageStudentsModalOpen, setIsManageStudentsModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState<any>(null);

  // Filter states
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "inactive">("all");
  const [gradeFilter, setGradeFilter] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });

  const fetchClasses = React.useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await headmasterAPI.getClasses({
        page: pagination.page,
        limit: pagination.limit,
        search: searchQuery,
        status: statusFilter,
      });

      if (response.success) {
        setClasses(response.data.classes);
        setPagination((prev) => ({
          ...prev,
          total: response.data.total,
        }));
      } else {
        setError(response.message || "Failed to load classes");
        showToast.error("Error", "Failed to load classes");
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to load classes";
      setError(message);
      console.error("Error fetching classes:", error);
      showToast.error("Error", message);
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit, searchQuery, statusFilter]);

  useEffect(() => {
    fetchClasses();
  }, [fetchClasses]);

  // Handler for search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handler for status filter change
  const handleStatusFilter = (status: "all" | "active" | "inactive") => {
    setStatusFilter(status);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handler for sorting
  const handleSort = (key: keyof Class) => {
    let direction: "asc" | "desc" = "asc";

    if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }

    setSortConfig({ key, direction });
  };

  const clearFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
    setGradeFilter([]);
    setSortConfig(null);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const hasActiveFilters = searchQuery || statusFilter !== 'all' || gradeFilter.length > 0;

  const handleAddSubmit = async (data: any) => {
    try {
      const classData: any = {
        ...data,
        grade: parseInt(data.grade),
        capacity: parseInt(data.capacity),
        academicYear: new Date().getFullYear(),
        active: true,
      };
      
      // Only include teacher if it's not empty
      if (!data.teacher || data.teacher.trim() === '') {
        delete classData.teacher;
      }
      
      const response = await headmasterAPI.createClass(classData);
      if (response.success) {
        showToast.success("Success", "Class added successfully");
        fetchClasses();
        setIsAddModalOpen(false);
      }
    } catch (error) {
      console.error("Error adding class:", error);
      showToast.error("Error", "Failed to add class");
    }
  };

  const handleExport = async () => {
    try {
      const csvHeaders = ['Class Name', 'Grade', 'Section', 'Teacher', 'Students', 'Capacity', 'Status'];
      const csvRows = [csvHeaders.join(',')];
      
      sortedClasses.forEach(cls => {
        const row = [
          `"${cls.name}"`,
          cls.grade,
          cls.section,
          `"${cls.teacher}"`,
          cls.studentCount,
          cls.capacity,
          cls.active ? 'Active' : 'Inactive'
        ];
        csvRows.push(row.join(','));
      });
      
      const csvContent = csvRows.join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `classes-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      showToast.success('Export Complete', 'Classes data exported successfully');
    } catch (error) {
      showToast.error('Export Failed', 'Failed to export classes data');
    }
  };

  const handleViewClass = (classData: any) => {
    setSelectedClass(classData);
    setIsViewModalOpen(true);
  };

  const handleEditClass = (classData: any) => {
    setSelectedClass(classData);
    setIsEditModalOpen(true);
  };

  const handleManageStudents = (classData: any) => {
    setSelectedClass(classData);
    setIsManageStudentsModalOpen(true);
  };

  const handleEditSubmit = async (data: any) => {
    if (!selectedClass) return;
    try {
      const updateData: any = {
        ...data,
        grade: parseInt(data.grade),
        capacity: parseInt(data.capacity),
      };
      
      if (!data.teacher || data.teacher.trim() === '') {
        delete updateData.teacher;
      }
      
      const response = await headmasterAPI.updateClass(selectedClass.id, updateData);
      if (response.success) {
        showToast.success("Success", "Class updated successfully");
        fetchClasses();
        setIsEditModalOpen(false);
      }
    } catch (error) {
      console.error("Error updating class:", error);
      showToast.error("Error", "Failed to update class");
    }
  };

  const handleToggleStatus = async (classData: any) => {
    try {
      const response = await headmasterAPI.updateClass(classData.id, {
        active: !classData.active
      });
      if (response.success) {
        showToast.success("Success", `Class ${classData.active ? 'deactivated' : 'activated'} successfully`);
        fetchClasses();
      }
    } catch (error) {
      console.error("Error toggling class status:", error);
      showToast.error("Error", "Failed to update class status");
    }
  };

  const handleDeleteClick = (classData: any) => {
    setSelectedClass(classData);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedClass) return;
    try {
      const response = await headmasterAPI.deleteClass(selectedClass.id);
      if (response.success) {
        showToast.success("Success", "Class deleted successfully");
        fetchClasses();
        setIsDeleteDialogOpen(false);
      }
    } catch (error) {
      console.error("Error deleting class:", error);
      showToast.error("Error", "Failed to delete class");
    }
  };

  const handlePrevPage = () => {
    if (pagination.page > 1) {
      setPagination((prev) => ({
        ...prev,
        page: prev.page - 1,
      }));
    }
  };

  const handleNextPage = () => {
    if (pagination.page * pagination.limit < pagination.total) {
      setPagination((prev) => ({
        ...prev,
        page: prev.page + 1,
      }));
    }
  };

  // Get unique grades for filter
  const availableGrades = [...new Set(classes.map(cls => cls.grade.toString()))].sort();

  // Filter and sort logic
  const filteredClasses = React.useMemo(() => {
    return classes.filter((cls) => {
      // Text search
      const matchesSearch =
        cls.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cls.teacher.toLowerCase().includes(searchQuery.toLowerCase()) ||
        `${cls.grade} ${cls.section}`.toLowerCase().includes(searchQuery.toLowerCase());

      // Status filter
      const matchesStatus =
        statusFilter === "all" ||
        (statusFilter === "active" && cls.active) ||
        (statusFilter === "inactive" && !cls.active);

      // Grade filter
      const matchesGrade = gradeFilter.length === 0 || gradeFilter.includes(cls.grade.toString());

      return matchesSearch && matchesStatus && matchesGrade;
    });
  }, [classes, searchQuery, statusFilter, gradeFilter]);

  // Apply sorting
  const sortedClasses = React.useMemo(() => {
    if (!sortConfig) return filteredClasses;

    return [...filteredClasses].sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });
  }, [filteredClasses, sortConfig]);

  return (
    <Layout>
      {error && <div className="bg-destructive/10 text-destructive rounded-md p-3 mb-6">{error}</div>}

      {/* Enhanced Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-emerald-900 via-green-800 to-teal-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-40 h-40 border border-white/10 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <SchoolIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Class Management</h1>
                <p className="text-emerald-200 mt-1">Organize and manage classroom environments</p>
              </div>
            </div>
            <p className="text-emerald-100 text-lg max-w-2xl">
              Comprehensive class management with student enrollment and academic organization tools.
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Target className="h-4 w-4 mr-2" />
              <span>Analytics</span>
            </Button>
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-white text-emerald-900 hover:bg-emerald-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>Add Class</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Classes</p>
                <p className="text-3xl font-bold text-blue-900">{loading ? "..." : (pagination?.total || 0).toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <div className="flex items-center text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-green-600 font-medium">{classes.filter((c) => c.active).length} active</span>
                  </div>
                </div>
              </div>
              <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <SchoolIcon className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
        
        <div className="relative overflow-hidden bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-700">Total Students</p>
                <p className="text-3xl font-bold text-emerald-900">{loading ? "..." : (classes?.reduce((sum, cls) => sum + (cls?.studentCount || 0), 0) || 0).toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-emerald-600 font-medium">Enrolled students</span>
                </div>
              </div>
              <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
        
        <div className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Avg Class Size</p>
                <p className="text-3xl font-bold text-purple-900">
                  {(classes?.length || 0) > 0 ? Math.round((classes?.reduce((sum, cls) => sum + (cls?.studentCount || 0), 0) || 0) / classes.length) : 0}
                </p>
                <p className="text-sm text-purple-600 mt-1">Students per class</p>
              </div>
              <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
        
        <div className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700">Capacity Usage</p>
                <p className="text-3xl font-bold text-orange-900">
                  {(classes?.length || 0) > 0 ? Math.round(((classes?.reduce((sum, cls) => sum + (cls?.studentCount || 0), 0) || 0) / (classes?.reduce((sum, cls) => sum + (cls?.capacity || 0), 0) || 1)) * 100) : 0}%
                </p>
                <p className="text-sm text-orange-600 mt-1">Overall utilization</p>
              </div>
              <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Award className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

        <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl hover:shadow-2xl transition-all duration-300">
          <div className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="bg-slate-700 p-2 rounded-lg">
                  <Search className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-slate-900">Class Directory</h3>
                  <p className="text-sm text-slate-600">Search and manage classroom environments</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="Search classes..."
                    className="pl-10 w-64 border-slate-200 focus:border-emerald-500 focus:ring-emerald-500"
                    value={searchQuery}
                    onChange={handleSearch}
                  />
                </div>
                <Select value={statusFilter} onValueChange={handleStatusFilter}>
                  <SelectTrigger className="w-[180px] border-slate-200 focus:border-emerald-500 focus:ring-emerald-500">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Classes</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          <div className="p-4 border-b border-slate-100/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className={`flex items-center gap-1 border-slate-200 hover:bg-slate-50 ${hasActiveFilters ? 'bg-emerald-50 border-emerald-200 text-emerald-700' : ''}`}
                  >
                    <Filter className="h-4 w-4" />
                    <span>Filter</span>
                    {hasActiveFilters && <span className="ml-1 text-xs bg-emerald-600 text-white rounded-full px-1.5 py-0.5">•</span>}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl">
                  <div className="px-2 py-1.5 text-sm font-medium">Grade</div>
                  {availableGrades.map(grade => (
                    <DropdownMenuCheckboxItem
                      key={grade}
                      checked={gradeFilter.includes(grade)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setGradeFilter(prev => [...prev, grade]);
                        } else {
                          setGradeFilter(prev => prev.filter(g => g !== grade));
                        }
                      }}
                    >
                      Grade {grade}
                    </DropdownMenuCheckboxItem>
                  ))}
                  {hasActiveFilters && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={clearFilters} className="text-destructive">
                        <X className="h-4 w-4 mr-2" />
                        Reset Filters
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
              <Button 
                variant="outline" 
                size="sm" 
                className="flex items-center gap-1"
                onClick={handleExport}
              >
                <Download className="h-4 w-4" />
                <span>Export</span>
              </Button>
            </div>
          </div>

          {/* Classes Table */}
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent">
                  <TableHead>
                    <button className="flex items-center gap-1" onClick={() => handleSort("name")}>
                      Class Name
                      <ArrowUpDown className="h-3 w-3" />
                      {sortConfig?.key === "name" && (
                        <span className="text-xs">{sortConfig.direction === "asc" ? "↑" : "↓"}</span>
                      )}
                    </button>
                  </TableHead>
                  <TableHead>
                    <button className="flex items-center gap-1" onClick={() => handleSort("grade")}>
                      Grade
                      <ArrowUpDown className="h-3 w-3" />
                      {sortConfig?.key === "grade" && (
                        <span className="text-xs">{sortConfig.direction === "asc" ? "↑" : "↓"}</span>
                      )}
                    </button>
                  </TableHead>
                  <TableHead>Teacher</TableHead>
                  <TableHead>Students</TableHead>
                  <TableHead>Capacity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>

              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex justify-center items-center">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        <span>Loading classes...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : sortedClasses.length > 0 ? (
                  sortedClasses.map((cls) => (
                    <TableRow key={cls.id} className="hover:bg-slate-50/50 transition-colors">
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="bg-emerald-100 p-2 rounded-lg">
                            <SchoolIcon className="h-4 w-4 text-emerald-600" />
                          </div>
                          <div>
                            <div className="font-semibold text-slate-900">{cls.name}</div>
                            <div className="text-xs text-slate-500 mt-1">Grade {cls.grade} - Section {cls.section}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <BookOpen className="h-4 w-4 text-blue-600" />
                          <span className="font-medium text-slate-900">{cls.grade} {cls.section}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <UserCheck className="h-4 w-4 text-purple-600" />
                          <span className="font-medium text-slate-900">{cls.teacher}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-teal-600" />
                          <div>
                            <div className="flex items-center">
                              <span className="font-medium text-slate-900">{cls.studentCount}</span>
                              <span className="text-slate-500 ml-1">/ {cls.capacity}</span>
                            </div>
                            <div className="w-full bg-slate-200 h-1.5 rounded-full mt-1">
                              <div
                                className="bg-emerald-500 h-full rounded-full transition-all duration-300"
                                style={{ width: `${(cls.studentCount / cls.capacity) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Target className="h-4 w-4 text-orange-600" />
                          <span className="font-medium text-slate-900">{cls.capacity}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={cls.active ? "default" : "secondary"}
                          className={cls.active ? 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200' : 'bg-gray-100 text-gray-800 border-gray-200'}
                        >
                          <div className={`w-2 h-2 rounded-full mr-2 ${cls.active ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                          {cls.active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-56 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl">
                            <DropdownMenuItem 
                              className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                              onSelect={() => handleViewClass(cls)}
                            >
                              <div className="bg-blue-100 p-1.5 rounded-lg">
                                <Eye className="h-4 w-4 text-blue-600" />
                              </div>
                              <div>
                                <span className="font-medium">View Details</span>
                                <p className="text-xs text-slate-500">See full information</p>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                              onSelect={() => handleEditClass(cls)}
                            >
                              <div className="bg-emerald-100 p-1.5 rounded-lg">
                                <Edit className="h-4 w-4 text-emerald-600" />
                              </div>
                              <div>
                                <span className="font-medium">Edit Class</span>
                                <p className="text-xs text-slate-500">Modify class details</p>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                              onSelect={() => handleManageStudents(cls)}
                            >
                              <div className="bg-purple-100 p-1.5 rounded-lg">
                                <Users className="h-4 w-4 text-purple-600" />
                              </div>
                              <div>
                                <span className="font-medium">Manage Students</span>
                                <p className="text-xs text-slate-500">Add or remove students</p>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator className="bg-slate-200" />
                            <DropdownMenuItem 
                              className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                              onSelect={() => handleToggleStatus(cls)}
                            >
                              {cls.active ? (
                                <>
                                  <div className="bg-orange-100 p-1.5 rounded-lg">
                                    <XCircle className="h-4 w-4 text-orange-600" />
                                  </div>
                                  <div>
                                    <span className="font-medium">Deactivate</span>
                                    <p className="text-xs text-slate-500">Make class inactive</p>
                                  </div>
                                </>
                              ) : (
                                <>
                                  <div className="bg-green-100 p-1.5 rounded-lg">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                  </div>
                                  <div>
                                    <span className="font-medium">Activate</span>
                                    <p className="text-xs text-slate-500">Make class active</p>
                                  </div>
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator className="bg-slate-200" />
                            <DropdownMenuItem 
                              className="flex items-center gap-3 text-red-600 focus:text-red-600 cursor-pointer hover:bg-red-50 p-3"
                              onSelect={() => handleDeleteClick(cls)}
                            >
                              <div className="bg-red-100 p-1.5 rounded-lg">
                                <Trash2 className="h-4 w-4 text-red-600" />
                              </div>
                              <div>
                                <span className="font-medium">Delete Class</span>
                                <p className="text-xs text-red-500">Remove permanently</p>
                              </div>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      No classes found matching your search criteria
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination Controls */}
          <div className="p-4 border-t flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing <span className="font-medium">{sortedClasses.length}</span> of{" "}
              <span className="font-medium">{pagination.total}</span> classes
              {hasActiveFilters && (
                <span className="ml-2">
                  • Filtered
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={clearFilters}
                    className="ml-1 h-auto p-1 text-xs text-destructive hover:text-destructive"
                  >
                    Clear
                  </Button>
                </span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handlePrevPage} disabled={pagination.page <= 1}>
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleNextPage}
                disabled={pagination.page * pagination.limit >= pagination.total}
              >
                Next
              </Button>
            </div>
          </div>
        </Card>

        {/* Modals */}
        <AddClassModal
          open={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onSubmit={handleAddSubmit}
        />
        
        {selectedClass && (
          <>
            <ViewClassModal
              open={isViewModalOpen}
              onClose={() => setIsViewModalOpen(false)}
              classData={selectedClass}
            />
            <EditClassModal
              open={isEditModalOpen}
              onClose={() => setIsEditModalOpen(false)}
              classData={selectedClass}
              onSubmit={handleEditSubmit}
            />
            <ManageStudentsModal
              open={isManageStudentsModalOpen}
              onClose={() => setIsManageStudentsModalOpen(false)}
              classData={selectedClass}
            />
          </>
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this class? This action cannot be undone and will affect all students in this class.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setIsDeleteDialogOpen(false)}>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteConfirm} className="bg-red-500 text-white hover:bg-red-600">
                Delete Class
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
    </Layout>
  );
}
