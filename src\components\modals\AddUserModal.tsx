import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { UserPlus } from 'lucide-react';

interface AddUserModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

export function AddUserModal({ open, onClose, onSubmit }: AddUserModalProps) {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    role: ''
  });
  const [isExistingTeacher, setIsExistingTeacher] = useState(false);
  const [selectedTeacher, setSelected<PERSON>eacher] = useState('');
  
  // Mock teachers data
  const teachers = [
    { id: '1', name: '<PERSON>', email: '<EMAIL>' },
    { id: '2', name: '<PERSON> <PERSON>', email: '<EMAIL>' },
    { id: '3', name: 'Carol <PERSON>', email: '<EMAIL>' },
    { id: '4', name: '<PERSON>', email: '<EMAIL>' }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    let submitData = { ...formData };
    
    if (isExistingTeacher && selectedTeacher) {
      const teacher = teachers.find(t => t.id === selectedTeacher);
      if (teacher) {
        const [firstName, ...lastNameParts] = teacher.name.split(' ');
        submitData = {
          ...submitData,
          firstName,
          lastName: lastNameParts.join(' '),
          email: teacher.email,
          role: 'teacher'
        };
      }
    }
    
    onSubmit(submitData);
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      role: ''
    });
    setIsExistingTeacher(false);
    setSelectedTeacher('');
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Add New User
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                placeholder="Enter first name"
                required
              />
            </div>
            <div>
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                placeholder="Enter last name"
                required
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              placeholder="Enter email address"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => setFormData({...formData, phone: e.target.value})}
              placeholder="+233 XX XXX XXXX"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({...formData, password: e.target.value})}
              placeholder="Enter password"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="role">Role</Label>
            <Select 
              value={formData.role} 
              onValueChange={(value) => {
                setFormData({...formData, role: value});
                if (value !== 'teacher') {
                  setIsExistingTeacher(false);
                  setSelectedTeacher('');
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="administrator">Administrator</SelectItem>
                <SelectItem value="teacher">Teacher</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {formData.role === 'teacher' && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="existingTeacher" 
                  checked={isExistingTeacher}
                  onCheckedChange={(checked) => {
                    setIsExistingTeacher(checked as boolean);
                    if (!checked) {
                      setSelectedTeacher('');
                    }
                  }}
                />
                <Label htmlFor="existingTeacher" className="text-sm">
                  This user is already a teacher in the system
                </Label>
              </div>
              
              {isExistingTeacher && (
                <div>
                  <Label htmlFor="teacherSelect">Select Teacher</Label>
                  <Select value={selectedTeacher} onValueChange={setSelectedTeacher}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a teacher" />
                    </SelectTrigger>
                    <SelectContent>
                      {teachers.map((teacher) => (
                        <SelectItem key={teacher.id} value={teacher.id}>
                          {teacher.name} - {teacher.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          )}
          
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-700">
              <strong>Note:</strong> This user will have dashboard access with the selected role permissions.
            </p>
          </div>
          
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              <UserPlus className="h-4 w-4 mr-2" />
              Create User
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}