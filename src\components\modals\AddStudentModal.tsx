import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { headmasterAPI } from "@/lib/api";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { X, Search, Check, ChevronsUpDown, Camera, Upload, User, Loader2 } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

const studentSchema = z.object({
  firstName: z.string().min(2, { message: "First name must be at least 2 characters" }),
  lastName: z.string().min(2, { message: "Last name must be at least 2 characters" }),
  classId: z.string().min(1, { message: "Class is required" }),
  parentId: z.string().min(1, { message: "Parent is required" }),
  subjects: z.array(z.string()).min(1, { message: "Please select at least one subject" }),
});

export type AddStudentFormData = z.infer<typeof studentSchema> & {
  image?: File;
};

interface AddStudentModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: AddStudentFormData) => Promise<void>;
}

export function AddStudentModal({ open, onClose, onSubmit }: AddStudentModalProps) {
  const [classes, setClasses] = useState<any[]>([]);
  const [parents, setParents] = useState<any[]>([]);
  const [courses, setCourses] = useState<any[]>([]);
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);
  const [parentSearchOpen, setParentSearchOpen] = useState(false);
  const [selectedParentName, setSelectedParentName] = useState("");
  const [parentSearchTerm, setParentSearchTerm] = useState("");
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [cameraStream, setCameraStream] = useState<MediaStream | null>(null);

  const form = useForm<AddStudentFormData>({
    resolver: zodResolver(studentSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      classId: "",
      parentId: "",
      subjects: [],
    },
  });

  useEffect(() => {
    if (open) {
      fetchClasses();
      fetchParents();
      fetchCourses();
    }
  }, [open]);

  const fetchClasses = async () => {
    try {
      const response = await headmasterAPI.getClasses();
      if (response.success) {
        setClasses(response.data.classes);
      }
    } catch (error) {
      console.error("Error fetching classes:", error);
    }
  };

  const fetchParents = async () => {
    try {
      const response = await headmasterAPI.getParents({ limit: 1000 });
      if (response.success) {
        setParents(Array.isArray(response.data.data) ? response.data.data : []);
      }
    } catch (error) {
      console.error("Error fetching parents:", error);
    }
  };

  const fetchCourses = async () => {
    try {
      const response = await headmasterAPI.getCourses({ limit: 1000 });
      if (response.success) {
        setCourses(Array.isArray(response.data.data) ? response.data.data : []);
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
    }
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        alert("Please select an image file");
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        alert("Image size should be less than 5MB");
        return;
      }

      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCameraCapture = () => {
    // Simple file input approach that won't interfere with modal
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.capture = "environment"; // This will prefer camera on mobile devices

    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        // Validate file type
        if (!file.type.startsWith("image/")) {
          alert("Please select an image file");
          return;
        }

        // Validate file size (5MB max)
        if (file.size > 5 * 1024 * 1024) {
          alert("Image size should be less than 5MB");
          return;
        }

        setSelectedImage(file);
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      }
    };

    input.click();
  };


  };

  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
  };

  const handleSubmit = async (data: AddStudentFormData) => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      // Include image in the submission
      const formData = { ...data, image: selectedImage };
      await onSubmit(formData);

      // Only reset and close if submission was successful
      form.reset();
      setSelectedSubjects([]);
      setSelectedImage(null);
      setImagePreview(null);
      onClose();
    } catch (error) {
      console.error("Error submitting student:", error);
      // Don't close modal on error
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubjectToggle = (subjectId: string) => {
    setSelectedSubjects((prev) => {
      const newSelection = prev.includes(subjectId) ? prev.filter((id) => id !== subjectId) : [...prev, subjectId];
      // Update the form field value as well
      form.setValue("subjects", newSelection);
      return newSelection;
    });
  };

  const removeSelectedSubject = (subjectId: string) => {
    setSelectedSubjects((prev) => {
      const newSelection = prev.filter((id) => id !== subjectId);
      // Update the form field value as well
      form.setValue("subjects", newSelection);
      return newSelection;
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add New Student</DialogTitle>
          <DialogDescription>Enter the details of the new student to add them to the system.</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Alex" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Turner" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Student Image Upload */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Student Photo (Optional)</label>
              <div className="flex items-center gap-4">
                {imagePreview ? (
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="Student preview"
                      className="w-20 h-20 rounded-full object-cover border-2 border-gray-200"
                    />
                    <button
                      type="button"
                      onClick={removeImage}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ) : (
                  <div className="w-20 h-20 rounded-full bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center">
                    <User className="w-8 h-8 text-gray-400" />
                  </div>
                )}

                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => document.getElementById("image-upload")?.click()}
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload
                  </Button>
                  <Button type="button" variant="outline" size="sm" onClick={handleCameraCapture}>
                    <Camera className="w-4 h-4 mr-2" />
                    Camera
                  </Button>
                </div>

                <input id="image-upload" type="file" accept="image/*" onChange={handleImageChange} className="hidden" />
              </div>
            </div>

            <FormField
              control={form.control}
              name="subjects"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Subjects *</FormLabel>
                  <FormControl>
                    <div className="space-y-3">
                      {/* Selected Subjects Display */}
                      {selectedSubjects.length > 0 && (
                        <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-muted/20">
                          {selectedSubjects.map((subjectId) => {
                            const course = courses.find((c) => c.id === subjectId);
                            return (
                              <Badge key={subjectId} variant="secondary" className="flex items-center gap-1">
                                {course?.name}
                                <X
                                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                                  onClick={() => removeSelectedSubject(subjectId)}
                                />
                              </Badge>
                            );
                          })}
                        </div>
                      )}

                      {/* Available Subjects */}
                      <div className="max-h-32 overflow-y-auto border rounded-md p-2 space-y-2">
                        {courses.length > 0 ? (
                          courses.map((course) => (
                            <div key={course.id} className="flex items-center space-x-2">
                              <Checkbox
                                id={`subject-${course.id}`}
                                checked={selectedSubjects.includes(course.id)}
                                onCheckedChange={() => handleSubjectToggle(course.id)}
                              />
                              <label htmlFor={`subject-${course.id}`} className="text-sm cursor-pointer flex-1">
                                {course.name} ({course.code})
                              </label>
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-muted-foreground">No courses available</p>
                        )}
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="classId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Class</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a class" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {classes.map((cls) => (
                        <SelectItem key={cls.id} value={cls.id}>
                          {cls.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Parent</FormLabel>
                  <Popover open={parentSearchOpen} onOpenChange={setParentSearchOpen}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button variant="outline" role="combobox" className="w-full justify-between">
                          {selectedParentName || "Select a parent"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <div className="p-2">
                        <div className="relative">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search parents..."
                            className="pl-8"
                            value={parentSearchTerm}
                            onChange={(e) => setParentSearchTerm(e.target.value)}
                          />
                        </div>
                        <div className="max-h-64 overflow-y-auto mt-2">
                          {parents
                            .filter((parent) => parent.name.toLowerCase().includes(parentSearchTerm.toLowerCase()))
                            .map((parent) => (
                              <div
                                key={parent.id}
                                className="flex items-center space-x-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                                onClick={() => {
                                  field.onChange(parent.id);
                                  setSelectedParentName(parent.name);
                                  setParentSearchOpen(false);
                                  setParentSearchTerm("");
                                }}
                              >
                                <Check
                                  className={`h-4 w-4 ${field.value === parent.id ? "opacity-100" : "opacity-0"}`}
                                />
                                {parent.name}
                              </div>
                            ))}
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" className="gradient-bg text-white hover:opacity-90" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding Student...
                  </>
                ) : (
                  "Add Student"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
