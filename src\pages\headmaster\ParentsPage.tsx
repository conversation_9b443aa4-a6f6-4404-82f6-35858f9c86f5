import React, { useState } from "react";
import { Layout } from "@/components/common/Layout";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  MoreHorizontal,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Download,
  Filter,
  ArrowUpDown,
  Users,
  Mail,
  Phone,
  Briefcase,
  Loader2,
  UserPlus,
  TrendingUp,
  Calendar,
  Award,
  Heart,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { format } from "date-fns";
import { AddParentModal, AddParentFormData } from "@/components/modals/AddParentModal";
import { EditParentModal } from "@/components/modals/EditParentModal";
import { ViewParentModal } from "@/components/modals/ViewParentModal";
import { headmasterAPI } from "@/lib/api";
import { showToast } from "@/lib/utils";
import type { Parent, ParentFormData } from "@/types";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

export function ParentsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [parents, setParents] = useState<Parent[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedParent, setSelectedParent] = useState<Parent | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });

  // Filter states
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [occupationFilter, setOccupationFilter] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: "asc" | "desc" } | null>(null);

  // Get unique occupations for filter
  const allOccupations = [
    ...new Set(Array.isArray(parents) ? parents.map((parent) => parent.occupation).filter(Boolean) : []),
  ];

  const fetchParents = React.useCallback(async () => {
    try {
      setLoading(true);
      const params: any = {
        page: pagination.page,
        limit: pagination.limit,
        search: searchQuery,
      };

      const response = await headmasterAPI.getParents(params);

      if (response.success) {
        // Backend returns data in response.data.data structure for parents
        const parentsData = Array.isArray(response.data.data) ? response.data.data : [];
        console.log("Parents API success - received", parentsData.length, "parents");
        setParents(parentsData);
        setPagination((prev) => ({
          ...prev,
          total: response.data.total || 0,
        }));
      }
    } catch (error) {
      console.error("Error fetching parents:", error);
      showToast.error("Error", "Failed to load parents");
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit, searchQuery]);

  React.useEffect(() => {
    fetchParents();
  }, [fetchParents]);

  const filteredParents = Array.isArray(parents)
    ? parents.filter((parent) => {
        // Text search filter
        const matchesSearch =
          parent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          parent.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          parent.children.some((child) => child.toLowerCase().includes(searchQuery.toLowerCase()));

        // Status filter
        const matchesStatus = statusFilter.length === 0 || statusFilter.includes(parent.status);

        // Occupation filter
        const matchesOccupation = occupationFilter.length === 0 || occupationFilter.includes(parent.occupation);

        return matchesSearch && matchesStatus && matchesOccupation;
      })
    : [];

  // Apply sorting
  const sortedParents = React.useMemo(() => {
    if (!sortConfig) return filteredParents;

    return [...filteredParents].sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });
  }, [filteredParents, sortConfig]);

  // Calculate summary statistics
  const activeParents = Array.isArray(parents) ? parents.filter((parent) => parent.status === "active").length : 0;
  const inactiveParents = Array.isArray(parents) ? parents.filter((parent) => parent.status === "inactive").length : 0;
  const totalChildren = Array.isArray(parents) ? parents.reduce((sum, parent) => sum + parent.children.length, 0) : 0;

  const handleAddParent = async (data: AddParentFormData) => {
    try {
      // Send firstName and lastName directly as the backend expects
      const parentData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        occupation: data.occupation,
        password: data.password, // Include password for account creation
      };

      const response = await headmasterAPI.createParent(parentData);
      if (response.success) {
        showToast.success("Success", "Parent added successfully");
        fetchParents();
        setIsAddModalOpen(false);
      }
    } catch (error) {
      console.error("Error adding parent:", error);
      showToast.error("Error", "Failed to add parent");
    }
  };

  const handleViewParent = (parent: Parent) => {
    setSelectedParent(parent);
    setIsViewModalOpen(true);
  };

  const handleEditParent = (parent: Parent) => {
    setSelectedParent(parent);
    setIsEditModalOpen(true);
  };

  const handleUpdateParent = async (data: ParentFormData) => {
    if (!selectedParent) return;
    try {
      const response = await headmasterAPI.updateParent(selectedParent.id, data);
      if (response.success) {
        showToast.success("Success", "Parent updated successfully");
        fetchParents();
        setIsEditModalOpen(false);
      }
    } catch (error) {
      console.error("Error updating parent:", error);
      showToast.error("Error", "Failed to update parent");
    }
  };

  const handleDeleteParent = (parent: Parent) => {
    setSelectedParent(parent);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteParent = async () => {
    if (!selectedParent) return;
    try {
      const response = await headmasterAPI.deleteParent(selectedParent.id);
      if (response.success) {
        showToast.success("Success", "Parent deleted successfully");
        fetchParents();
        setIsDeleteDialogOpen(false);
      }
    } catch (error) {
      console.error("Error deleting parent:", error);
      showToast.error("Error", "Failed to delete parent");
    }
  };

  const handleSort = (key: string) => {
    let direction: "asc" | "desc" = "asc";

    if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }

    setSortConfig({ key, direction });
  };

  const clearFilters = () => {
    setStatusFilter([]);
    setOccupationFilter([]);
    setSortConfig(null);
    setSearchQuery("");
  };

  const handleExport = async () => {
    try {
      const csvHeaders = ["Name", "Email", "Phone", "Children", "Occupation", "Status"];
      const csvRows = [csvHeaders.join(",")];

      sortedParents.forEach((parent) => {
        const row = [
          `"${parent.name}"`,
          `"${parent.email}"`,
          `"${parent.phone}"`,
          `"${Array.isArray(parent.children) ? parent.children.join(", ") : parent.children}"`,
          `"${parent.occupation}"`,
          parent.status || "active",
        ];
        csvRows.push(row.join(","));
      });

      const csvContent = csvRows.join("\n");
      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `parents-${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showToast.success("Export Complete", "Parents data exported successfully");
    } catch (error) {
      showToast.error("Export Failed", "Failed to export parents data");
    }
  };

  return (
    <Layout>
      {/* Enhanced Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-rose-900 via-pink-800 to-purple-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-40 h-40 border border-white/10 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>

        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Parent Community</h1>
                <p className="text-rose-200 mt-1">Connect and manage parent relationships</p>
              </div>
            </div>
            <p className="text-rose-100 text-lg max-w-2xl">
              Foster strong parent-school partnerships with comprehensive parent management and engagement tools.
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Award className="h-4 w-4 mr-2" />
              <span>Engagement</span>
            </Button>
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-white text-rose-900 hover:bg-rose-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              <span>Add Parent</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 mb-8">
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Parents</p>
                <p className="text-3xl font-bold text-blue-900">
                  {loading ? "..." : filteredParents.length.toLocaleString()}
                </p>
                <div className="flex items-center mt-2">
                  <div className="flex items-center text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-green-600 font-medium">{activeParents} active</span>
                    <span className="text-gray-500 mx-2">•</span>
                    <span className="text-gray-600">{inactiveParents} inactive</span>
                  </div>
                </div>
              </div>
              <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-700">Total Children</p>
                <p className="text-3xl font-bold text-emerald-900">
                  {loading ? "..." : totalChildren.toLocaleString()}
                </p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-emerald-600 font-medium">Enrolled in school</span>
                </div>
              </div>
              <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Heart className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Parent Engagement</p>
                <p className="text-3xl font-bold text-purple-900">78%</p>
                <p className="text-sm text-purple-600 mt-1">Meeting attendance rate</p>
              </div>
              <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Award className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl hover:shadow-2xl transition-all duration-300">
        <div className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <Search className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-slate-900">Parent Directory</h3>
                <p className="text-sm text-slate-600">Search and manage parent relationships</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search parents..."
                  className="pl-10 w-64 border-slate-200 focus:border-rose-500 focus:ring-rose-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="p-4 border-b border-slate-100/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={`flex items-center gap-1 border-slate-200 hover:bg-slate-50 ${
                    statusFilter.length > 0 || occupationFilter.length > 0
                      ? "bg-rose-50 border-rose-200 text-rose-700"
                      : ""
                  }`}
                >
                  <Filter className="h-4 w-4" />
                  <span>Filter</span>
                  {(statusFilter.length > 0 || occupationFilter.length > 0) && (
                    <Badge variant="secondary" className="ml-1 h-5 px-1.5 bg-rose-600 text-white">
                      {statusFilter.length + occupationFilter.length}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl" align="end">
                <div className="p-4 border-b">
                  <h4 className="font-medium">Filters</h4>
                  <p className="text-sm text-muted-foreground">Filter parents by status and occupation</p>
                </div>
                <div className="p-4 space-y-4">
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium">Status</h5>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="status-active"
                          checked={statusFilter.includes("active")}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setStatusFilter([...statusFilter, "active"]);
                            } else {
                              setStatusFilter(statusFilter.filter((s) => s !== "active"));
                            }
                          }}
                        />
                        <Label htmlFor="status-active" className="text-sm">
                          Active
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="status-inactive"
                          checked={statusFilter.includes("inactive")}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setStatusFilter([...statusFilter, "inactive"]);
                            } else {
                              setStatusFilter(statusFilter.filter((s) => s !== "inactive"));
                            }
                          }}
                        />
                        <Label htmlFor="status-inactive" className="text-sm">
                          Inactive
                        </Label>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h5 className="text-sm font-medium">Occupation</h5>
                    <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                      {allOccupations.map((occupation) => (
                        <div key={occupation} className="flex items-center space-x-2">
                          <Checkbox
                            id={`occupation-${occupation}`}
                            checked={occupationFilter.includes(occupation)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setOccupationFilter([...occupationFilter, occupation]);
                              } else {
                                setOccupationFilter(occupationFilter.filter((o) => o !== occupation));
                              }
                            }}
                          />
                          <Label htmlFor={`occupation-${occupation}`} className="text-sm">
                            {occupation}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="p-4 border-t flex justify-between">
                  <Button variant="outline" size="sm" onClick={clearFilters}>
                    Clear Filters
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
            <Button variant="outline" size="sm" className="flex items-center gap-1" onClick={handleExport}>
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="w-[200px]">
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort("name")}>
                    Name
                    <ArrowUpDown className="h-3 w-3" />
                    {sortConfig?.key === "name" &&
                      (sortConfig.direction === "asc" ? (
                        <span className="text-xs">↑</span>
                      ) : (
                        <span className="text-xs">↓</span>
                      ))}
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort("email")}>
                    Email
                    <ArrowUpDown className="h-3 w-3" />
                    {sortConfig?.key === "email" &&
                      (sortConfig.direction === "asc" ? (
                        <span className="text-xs">↑</span>
                      ) : (
                        <span className="text-xs">↓</span>
                      ))}
                  </div>
                </TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Children</TableHead>
                <TableHead>
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort("occupation")}>
                    Occupation
                    <ArrowUpDown className="h-3 w-3" />
                    {sortConfig?.key === "occupation" &&
                      (sortConfig.direction === "asc" ? (
                        <span className="text-xs">↑</span>
                      ) : (
                        <span className="text-xs">↓</span>
                      ))}
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort("status")}>
                    Status
                    <ArrowUpDown className="h-3 w-3" />
                    {sortConfig?.key === "status" &&
                      (sortConfig.direction === "asc" ? (
                        <span className="text-xs">↑</span>
                      ) : (
                        <span className="text-xs">↓</span>
                      ))}
                  </div>
                </TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading parents...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : sortedParents.length > 0 ? (
                sortedParents.map((parent) => (
                  <TableRow key={parent.id} className="hover:bg-slate-50/50 transition-colors">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="bg-rose-100 p-2 rounded-lg">
                          <Heart className="h-4 w-4 text-rose-600" />
                        </div>
                        <div>
                          <div className="font-semibold text-slate-900">{parent.name}</div>
                          <div className="flex items-center text-xs text-slate-500 mt-1">
                            <Mail className="h-3 w-3 mr-1" />
                            <span>{parent.email}</span>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-slate-900">{parent.email}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-emerald-600" />
                        <span className="font-medium text-slate-900">{parent.phone}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {(Array.isArray(parent.children) ? parent.children : [parent.children]).map((child, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200"
                          >
                            {child}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Briefcase className="h-4 w-4 text-orange-600" />
                        <span className="font-medium text-slate-900">{parent.occupation}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={parent.status === "active" ? "default" : "secondary"}
                        className={
                          parent.status === "active"
                            ? "bg-green-100 text-green-800 border-green-200 hover:bg-green-200"
                            : "bg-gray-100 text-gray-800 border-gray-200"
                        }
                      >
                        <div
                          className={`w-2 h-2 rounded-full mr-2 ${
                            parent.status === "active" ? "bg-green-500" : "bg-gray-400"
                          }`}
                        ></div>
                        {parent.status === "active" ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="w-56 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl"
                        >
                          <DropdownMenuItem
                            className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                            onClick={() => handleViewParent(parent)}
                          >
                            <div className="bg-blue-100 p-1.5 rounded-lg">
                              <Eye className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <span className="font-medium">View Profile</span>
                              <p className="text-xs text-slate-500">See full information</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                            onClick={() => handleEditParent(parent)}
                          >
                            <div className="bg-emerald-100 p-1.5 rounded-lg">
                              <Edit className="h-4 w-4 text-emerald-600" />
                            </div>
                            <div>
                              <span className="font-medium">Edit Parent</span>
                              <p className="text-xs text-slate-500">Modify parent details</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-slate-200" />
                          <DropdownMenuItem
                            className="flex items-center gap-3 text-red-600 focus:text-red-600 cursor-pointer hover:bg-red-50 p-3"
                            onClick={() => handleDeleteParent(parent)}
                          >
                            <div className="bg-red-100 p-1.5 rounded-lg">
                              <Trash2 className="h-4 w-4 text-red-600" />
                            </div>
                            <div>
                              <span className="font-medium">Delete Parent</span>
                              <p className="text-xs text-red-500">Remove permanently</p>
                            </div>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No parents found matching your search criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{sortedParents.length}</span> of{" "}
            <span className="font-medium">{pagination.total}</span> parents
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPagination((prev) => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPagination((prev) => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page * pagination.limit >= pagination.total}
            >
              Next
            </Button>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <AddParentModal open={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} onSubmit={handleAddParent} />

      {selectedParent && (
        <>
          <ViewParentModal open={isViewModalOpen} onClose={() => setIsViewModalOpen(false)} parent={selectedParent} />

          <EditParentModal
            open={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSubmit={handleUpdateParent}
            parent={selectedParent}
          />
        </>
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the parent
              {selectedParent && ` ${selectedParent.name}`} from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteParent}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}
