import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { toast } from "@/components/ui/use-toast";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Toast utility functions
export const showToast = {
  success: (title: string, description?: string) => {
    toast({
      variant: "success",
      title,
      description,
    });
  },
  error: (title: string, description?: string) => {
    toast({
      variant: "destructive",
      title,
      description,
    });
  },
  warning: (title: string, description?: string) => {
    toast({
      variant: "warning",
      title,
      description,
    });
  },
  info: (title: string, description?: string) => {
    toast({
      variant: "info",
      title,
      description,
    });
  }
};