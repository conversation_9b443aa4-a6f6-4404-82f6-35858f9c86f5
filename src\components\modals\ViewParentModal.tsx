import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Mail, Phone, Briefcase, Users, MapPin, Calendar } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface Parent {
  id: string;
  name: string;
  email: string;
  phone: string;
  children: string[];
  occupation: string;
  status: string;
}

interface ViewParentModalProps {
  open: boolean;
  onClose: () => void;
  parent: Parent | null;
}

export function ViewParentModal({ open, onClose, parent }: ViewParentModalProps) {
  if (!parent) return null;

  // Mock additional data
  const parentDetails = {
    address: "123 Main Street, Springfield",
    lastVisit: "2023-10-15",
    emergencyContact: "<PERSON> (******-987-6543)",
    relationship: "Father",
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Parent Profile</DialogTitle>
          <DialogDescription>Detailed information about {parent.name}</DialogDescription>
        </DialogHeader>

        <div className="flex flex-col md:flex-row gap-6 py-4">
          <div className="flex flex-col items-center space-y-3">
            <Avatar className="h-24 w-24">
              <AvatarFallback className="text-2xl bg-primary/20 text-primary">{parent.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <Badge
              variant={parent.status === "active" ? "success" : "secondary"}
              className={parent.status === "active" ? "status-active" : "status-inactive"}
            >
              {parent.status === "active" ? "Active" : "Inactive"}
            </Badge>
          </div>

          <div className="flex-1 space-y-4">
            <div>
              <h3 className="text-xl font-bold">{parent.name}</h3>
              <div className="flex items-center mt-1">
                <Briefcase className="h-4 w-4 mr-2 text-primary" />
                <span className="text-muted-foreground">{parent.occupation}</span>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{parent.email}</span>
              </div>
              <div className="flex items-center">
                <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{parent.phone}</span>
              </div>
              <div className="flex items-center">
                <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{parent.occupation || "Not specified"}</span>
              </div>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{parentDetails.address}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>Last Visit: {parentDetails.lastVisit}</span>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="text-sm font-medium mb-2">Children</h4>
              <div className="flex flex-wrap gap-2">
                {parent.children.map((child, index) => (
                  <Badge key={index} variant="outline" className="bg-primary/10 text-primary hover:bg-primary/20">
                    {child}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">Emergency Contact</h4>
              <p className="text-sm">{parentDetails.emergencyContact}</p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button className="gradient-bg text-white hover:opacity-90">Send Message</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
