import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, Award } from 'lucide-react';

interface GradeScale {
  grade: string;
  minScore: number;
  maxScore: number;
  gpa: number;
  remark: string;
}

interface CreateGradingSystemModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

export function CreateGradingSystemModal({ open, onClose, onSubmit }: CreateGradingSystemModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    active: false
  });
  const [selectedClasses, setSelectedClasses] = useState<string[]>([]);
  const [gradeScales, setGradeScales] = useState<GradeScale[]>([
    { grade: 'A', minScore: 80, maxScore: 100, gpa: 4.0, remark: 'Excellent' }
  ]);

  // Mock classes data
  const availableClasses = [
    'Primary 1', 'Primary 2', 'Primary 3', 'Primary 4', 'Primary 5', 'Primary 6',
    'JHS 1', 'JHS 2', 'JHS 3', 'SHS 1', 'SHS 2', 'SHS 3'
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      classes: selectedClasses,
      scales: gradeScales
    });
    resetForm();
  };

  const resetForm = () => {
    setFormData({ name: '', active: false });
    setSelectedClasses([]);
    setGradeScales([{ grade: 'A', minScore: 80, maxScore: 100, gpa: 4.0, remark: 'Excellent' }]);
  };

  const handleClassChange = (className: string, checked: boolean) => {
    if (checked) {
      setSelectedClasses([...selectedClasses, className]);
    } else {
      setSelectedClasses(selectedClasses.filter(c => c !== className));
    }
  };

  const addGradeScale = () => {
    setGradeScales([...gradeScales, { grade: '', minScore: 0, maxScore: 0, gpa: 0, remark: '' }]);
  };

  const removeGradeScale = (index: number) => {
    setGradeScales(gradeScales.filter((_, i) => i !== index));
  };

  const updateGradeScale = (index: number, field: keyof GradeScale, value: string | number) => {
    const updated = gradeScales.map((scale, i) => 
      i === index ? { ...scale, [field]: value } : scale
    );
    setGradeScales(updated);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Create Custom Grading System
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">System Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="e.g., Primary School Grading"
                required
              />
            </div>
            <div className="flex items-center space-x-2 pt-6">
              <Checkbox
                id="active"
                checked={formData.active}
                onCheckedChange={(checked) => setFormData({...formData, active: checked as boolean})}
              />
              <Label htmlFor="active" className="text-sm">Set as active system</Label>
            </div>
          </div>

          {/* Class Selection */}
          <div>
            <Label>Select Classes</Label>
            <p className="text-sm text-slate-600 mb-3">Choose which classes this grading system will apply to</p>
            <div className="mb-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="selectAll"
                  checked={selectedClasses.length === availableClasses.length}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedClasses(availableClasses);
                    } else {
                      setSelectedClasses([]);
                    }
                  }}
                />
                <Label htmlFor="selectAll" className="text-sm font-medium">Select All Classes</Label>
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-40 overflow-y-auto border rounded-lg p-4">
              {availableClasses.map((className) => (
                <div key={className} className="flex items-center space-x-2">
                  <Checkbox
                    id={className}
                    checked={selectedClasses.includes(className)}
                    onCheckedChange={(checked) => handleClassChange(className, checked as boolean)}
                  />
                  <Label htmlFor={className} className="text-sm">{className}</Label>
                </div>
              ))}
            </div>
          </div>

          {/* Grade Scales */}
          <div>
            <div className="flex justify-between items-center mb-3">
              <Label>Grade Scales</Label>
              <Button type="button" variant="outline" size="sm" onClick={addGradeScale}>
                <Plus className="h-4 w-4 mr-2" />
                Add Grade
              </Button>
            </div>
            
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {gradeScales.map((scale, index) => (
                <div key={index} className="grid grid-cols-6 gap-3 p-3 border rounded-lg">
                  <div>
                    <Label className="text-xs">Grade</Label>
                    <Input
                      value={scale.grade}
                      onChange={(e) => updateGradeScale(index, 'grade', e.target.value)}
                      placeholder="A"
                      required
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Min Score</Label>
                    <Input
                      type="number"
                      value={scale.minScore}
                      onChange={(e) => updateGradeScale(index, 'minScore', parseInt(e.target.value) || 0)}
                      placeholder="80"
                      required
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Max Score</Label>
                    <Input
                      type="number"
                      value={scale.maxScore}
                      onChange={(e) => updateGradeScale(index, 'maxScore', parseInt(e.target.value) || 0)}
                      placeholder="100"
                      required
                    />
                  </div>
                  <div>
                    <Label className="text-xs">GPA</Label>
                    <Input
                      type="number"
                      step="0.1"
                      value={scale.gpa}
                      onChange={(e) => updateGradeScale(index, 'gpa', parseFloat(e.target.value) || 0)}
                      placeholder="4.0"
                      required
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Remark</Label>
                    <Input
                      value={scale.remark}
                      onChange={(e) => updateGradeScale(index, 'remark', e.target.value)}
                      placeholder="Excellent"
                      required
                    />
                  </div>
                  <div className="flex items-end">
                    {gradeScales.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeGradeScale(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-amber-50 p-4 rounded-lg">
            <p className="text-sm text-amber-700">
              <strong>Note:</strong> This custom grading system will only apply to the selected classes. 
              You can modify or deactivate it at any time.
            </p>
          </div>
          
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              <Award className="h-4 w-4 mr-2" />
              Create Grading System
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}