import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { 
  TrendingUp,
  Settings,
  Save,
  RotateCcw,
  ArrowRight,
  GraduationCap,
  Target,
  Users
} from 'lucide-react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

interface PromotionCriteria {
  id: string;
  name: string;
  minGPA: number;
  minAttendance: number;
  requiredSubjects: string[];
  active: boolean;
}

interface ClassFlow {
  id: string;
  name: string;
  order: number;
  nextClass?: string;
}

export function StudentsPromotionPage() {
  const [loading, setLoading] = useState(true);
  const [promotionCriteria, setPromotionCriteria] = useState<PromotionCriteria>({
    id: '1',
    name: 'Standard Promotion',
    minGPA: 2.0,
    minAttendance: 75,
    requiredSubjects: [],
    active: true
  });
  const [classFlow, setClassFlow] = useState<ClassFlow[]>([]);
  const [availableSubjects] = useState([
    'Mathematics', 'English', 'Science', 'Social Studies', 'ICT', 'French'
  ]);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      // Mock data
      const mockClassFlow: ClassFlow[] = [
        { id: '1', name: 'Primary 1', order: 1 },
        { id: '2', name: 'Primary 2', order: 2 },
        { id: '3', name: 'Primary 3', order: 3 },
        { id: '4', name: 'Primary 4', order: 4 },
        { id: '5', name: 'Primary 5', order: 5 },
        { id: '6', name: 'Primary 6', order: 6 },
        { id: '7', name: 'JHS 1', order: 7 },
        { id: '8', name: 'JHS 2', order: 8 },
        { id: '9', name: 'JHS 3', order: 9 }
      ];
      setClassFlow(mockClassFlow);
    } catch (error) {
      console.error('Error fetching data:', error);
      showToast.error('Error', 'Failed to load promotion data');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveCriteria = async () => {
    try {
      // Mock save
      showToast.success('Success', 'Promotion criteria saved successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to save promotion criteria');
    }
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(classFlow);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order numbers
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index + 1
    }));

    setClassFlow(updatedItems);
  };

  const resetClassFlow = () => {
    const defaultFlow: ClassFlow[] = [
      { id: '1', name: 'Primary 1', order: 1 },
      { id: '2', name: 'Primary 2', order: 2 },
      { id: '3', name: 'Primary 3', order: 3 },
      { id: '4', name: 'Primary 4', order: 4 },
      { id: '5', name: 'Primary 5', order: 5 },
      { id: '6', name: 'Primary 6', order: 6 },
      { id: '7', name: 'JHS 1', order: 7 },
      { id: '8', name: 'JHS 2', order: 8 },
      { id: '9', name: 'JHS 3', order: 9 }
    ];
    setClassFlow(defaultFlow);
    showToast.success('Success', 'Class flow reset to default order');
  };

  const saveClassFlow = async () => {
    try {
      // Mock save
      showToast.success('Success', 'Class promotion flow saved successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to save class flow');
    }
  };

  const handleSubjectToggle = (subject: string) => {
    const updatedSubjects = promotionCriteria.requiredSubjects.includes(subject)
      ? promotionCriteria.requiredSubjects.filter(s => s !== subject)
      : [...promotionCriteria.requiredSubjects, subject];
    
    setPromotionCriteria({
      ...promotionCriteria,
      requiredSubjects: updatedSubjects
    });
  };

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-violet-900 via-purple-800 to-indigo-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Students Promotion</h1>
                <p className="text-violet-200 mt-1">Configure promotion criteria and class progression flow</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-3 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Min GPA Required</p>
              <p className="text-3xl font-bold text-blue-900">{promotionCriteria.minGPA}</p>
            </div>
            <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Target className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-green-100/50 border-green-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-700">Min Attendance</p>
              <p className="text-3xl font-bold text-green-900">{promotionCriteria.minAttendance}%</p>
            </div>
            <div className="bg-green-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700">Class Levels</p>
              <p className="text-3xl font-bold text-purple-900">{classFlow.length}</p>
            </div>
            <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <GraduationCap className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex items-center gap-3">
            <div className="bg-slate-700 p-2 rounded-lg">
              <TrendingUp className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-slate-900">Promotion Configuration</CardTitle>
              <p className="text-sm text-slate-600">Set criteria and class progression flow</p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <Tabs defaultValue="criteria" className="w-full">
            <div className="px-6 pt-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="criteria">Promotion Criteria</TabsTrigger>
                <TabsTrigger value="flow">Class Flow</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="criteria" className="mt-0">
              <div className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="criteriaName">Criteria Name</Label>
                    <Input
                      id="criteriaName"
                      value={promotionCriteria.name}
                      onChange={(e) => setPromotionCriteria({...promotionCriteria, name: e.target.value})}
                      placeholder="e.g., Standard Promotion"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="minGPA">Minimum GPA Required</Label>
                    <Input
                      id="minGPA"
                      type="number"
                      step="0.1"
                      min="0"
                      max="4"
                      value={promotionCriteria.minGPA}
                      onChange={(e) => setPromotionCriteria({...promotionCriteria, minGPA: parseFloat(e.target.value) || 0})}
                      placeholder="2.0"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="minAttendance">Minimum Attendance Percentage</Label>
                  <Input
                    id="minAttendance"
                    type="number"
                    min="0"
                    max="100"
                    value={promotionCriteria.minAttendance}
                    onChange={(e) => setPromotionCriteria({...promotionCriteria, minAttendance: parseInt(e.target.value) || 0})}
                    placeholder="75"
                  />
                </div>

                <div>
                  <Label>Required Subjects (Optional)</Label>
                  <p className="text-sm text-slate-600 mb-3">Select subjects that must be passed for promotion</p>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {availableSubjects.map((subject) => (
                      <div
                        key={subject}
                        onClick={() => handleSubjectToggle(subject)}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          promotionCriteria.requiredSubjects.includes(subject)
                            ? 'bg-violet-50 border-violet-200 text-violet-800'
                            : 'bg-white border-slate-200 hover:bg-slate-50'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{subject}</span>
                          {promotionCriteria.requiredSubjects.includes(subject) && (
                            <Badge variant="secondary" className="bg-violet-600 text-white">Required</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-violet-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-violet-900 mb-2">Promotion Summary</h4>
                  <div className="text-sm text-violet-700 space-y-1">
                    <p>• Students need a minimum GPA of <strong>{promotionCriteria.minGPA}</strong></p>
                    <p>• Students need at least <strong>{promotionCriteria.minAttendance}%</strong> attendance</p>
                    {promotionCriteria.requiredSubjects.length > 0 && (
                      <p>• Students must pass: <strong>{promotionCriteria.requiredSubjects.join(', ')}</strong></p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button onClick={handleSaveCriteria}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Criteria
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="flow" className="mt-0">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h3 className="text-lg font-semibold">Class Progression Flow</h3>
                    <p className="text-sm text-slate-600">Drag and drop to reorder class progression</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={resetClassFlow}>
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Reset
                    </Button>
                    <Button onClick={saveClassFlow}>
                      <Save className="h-4 w-4 mr-2" />
                      Save Flow
                    </Button>
                  </div>
                </div>

                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="classFlow">
                    {(provided) => (
                      <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        className="space-y-3"
                      >
                        {classFlow.map((classItem, index) => (
                          <Draggable key={classItem.id} draggableId={classItem.id} index={index}>
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className={`flex items-center justify-between p-4 bg-white border rounded-lg shadow-sm transition-all ${
                                  snapshot.isDragging ? 'shadow-lg scale-105' : 'hover:shadow-md'
                                }`}
                              >
                                <div className="flex items-center gap-4">
                                  <div className="bg-violet-100 text-violet-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold">
                                    {classItem.order}
                                  </div>
                                  <div>
                                    <h4 className="font-semibold text-slate-900">{classItem.name}</h4>
                                    <p className="text-sm text-slate-600">Level {classItem.order}</p>
                                  </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                  {index < classFlow.length - 1 && (
                                    <>
                                      <ArrowRight className="h-4 w-4 text-slate-400" />
                                      <span className="text-sm text-slate-600">{classFlow[index + 1]?.name}</span>
                                    </>
                                  )}
                                  {index === classFlow.length - 1 && (
                                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                      Graduate
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>

                <div className="mt-6 bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-2">Flow Instructions</h4>
                  <div className="text-sm text-blue-700 space-y-1">
                    <p>• Drag and drop classes to reorder the promotion flow</p>
                    <p>• Students will progress from one level to the next based on this order</p>
                    <p>• The last class in the flow will be marked as graduation level</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </Layout>
  );
}