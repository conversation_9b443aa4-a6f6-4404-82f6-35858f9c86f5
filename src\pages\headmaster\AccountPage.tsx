import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { 
  School,
  Save,
  MapPin,
  Phone,
  Mail,
  Calendar,
  FileText,
  Info
} from 'lucide-react';

interface SchoolInfo {
  id: string;
  name: string;
  dateEstablished: string;
  slogan: string;
  phone: string;
  email: string;
  about: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  website: string;
  principalMessage: string;
  mission: string;
  vision: string;
}

export function AccountPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo>({
    id: '',
    name: '',
    dateEstablished: '',
    slogan: '',
    phone: '',
    email: '',
    about: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    website: '',
    principalMessage: '',
    mission: '',
    vision: ''
  });

  useEffect(() => {
    fetchSchoolInfo();
  }, []);

  const fetchSchoolInfo = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockData: SchoolInfo = {
        id: '1',
        name: 'Greenfield Academy',
        dateEstablished: '1995-09-15',
        slogan: 'Excellence in Education',
        phone: '+233 24 123 4567',
        email: '<EMAIL>',
        about: 'Greenfield Academy is a premier educational institution committed to providing quality education and nurturing young minds for future success.',
        address: '123 Education Street',
        city: 'Accra',
        state: 'Greater Accra',
        zipCode: 'GA-123-4567',
        website: 'www.greenfieldacademy.edu.gh',
        principalMessage: 'Welcome to Greenfield Academy, where we believe every student has the potential to excel.',
        mission: 'To provide quality education that empowers students to become responsible global citizens.',
        vision: 'To be the leading educational institution in Ghana, recognized for academic excellence and character development.'
      };
      setSchoolInfo(mockData);
    } catch (error) {
      console.error('Error fetching school info:', error);
      showToast.error('Error', 'Failed to load school information');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof SchoolInfo, value: string) => {
    setSchoolInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      // Mock API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
      showToast.success('Success', 'School information updated successfully');
    } catch (error) {
      console.error('Error saving school info:', error);
      showToast.error('Error', 'Failed to update school information');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-900 via-indigo-800 to-purple-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <School className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">School Account</h1>
                <p className="text-blue-200 mt-1">Manage school information and settings</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-white text-blue-900 hover:bg-blue-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Save className="h-4 w-4 mr-2" />
              <span>{saving ? 'Saving...' : 'Save Changes'}</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="grid gap-8">
        {/* Basic Information */}
        <Card className="shadow-lg">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
            <CardTitle className="flex items-center gap-2">
              <School className="h-5 w-5 text-blue-600" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="name">School Name</Label>
                <Input
                  id="name"
                  value={schoolInfo.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter school name"
                />
              </div>
              <div>
                <Label htmlFor="dateEstablished">Date Established</Label>
                <Input
                  id="dateEstablished"
                  type="date"
                  value={schoolInfo.dateEstablished}
                  onChange={(e) => handleInputChange('dateEstablished', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="slogan">School Slogan</Label>
                <Input
                  id="slogan"
                  value={schoolInfo.slogan}
                  onChange={(e) => handleInputChange('slogan', e.target.value)}
                  placeholder="Enter school slogan"
                />
              </div>
              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={schoolInfo.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="www.yourschool.com"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card className="shadow-lg">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5 text-green-600" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={schoolInfo.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+233 XX XXX XXXX"
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={schoolInfo.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Address Information */}
        <Card className="shadow-lg">
          <CardHeader className="bg-gradient-to-r from-orange-50 to-red-50 border-b">
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-orange-600" />
              Address Details
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 gap-6">
              <div>
                <Label htmlFor="address">Street Address</Label>
                <Input
                  id="address"
                  value={schoolInfo.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Enter street address"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={schoolInfo.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="Enter city"
                  />
                </div>
                <div>
                  <Label htmlFor="state">State/Region</Label>
                  <Input
                    id="state"
                    value={schoolInfo.state}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    placeholder="Enter state/region"
                  />
                </div>
                <div>
                  <Label htmlFor="zipCode">Postal Code</Label>
                  <Input
                    id="zipCode"
                    value={schoolInfo.zipCode}
                    onChange={(e) => handleInputChange('zipCode', e.target.value)}
                    placeholder="Enter postal code"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* School Information */}
        <Card className="shadow-lg">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b">
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5 text-purple-600" />
              School Information
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid gap-6">
              <div>
                <Label htmlFor="about">About the School</Label>
                <Textarea
                  id="about"
                  value={schoolInfo.about}
                  onChange={(e) => handleInputChange('about', e.target.value)}
                  placeholder="Describe your school..."
                  rows={4}
                />
              </div>
              <div>
                <Label htmlFor="mission">Mission Statement</Label>
                <Textarea
                  id="mission"
                  value={schoolInfo.mission}
                  onChange={(e) => handleInputChange('mission', e.target.value)}
                  placeholder="Enter school mission..."
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="vision">Vision Statement</Label>
                <Textarea
                  id="vision"
                  value={schoolInfo.vision}
                  onChange={(e) => handleInputChange('vision', e.target.value)}
                  placeholder="Enter school vision..."
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="principalMessage">Principal's Message</Label>
                <Textarea
                  id="principalMessage"
                  value={schoolInfo.principalMessage}
                  onChange={(e) => handleInputChange('principalMessage', e.target.value)}
                  placeholder="Enter principal's message..."
                  rows={4}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}