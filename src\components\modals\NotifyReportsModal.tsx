import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Bell } from 'lucide-react';

interface NotifyReportsModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  classes: any[];
}

export function NotifyReportsModal({ open, onClose, onSubmit, classes }: NotifyReportsModalProps) {
  const [formData, setFormData] = useState({
    selectedClass: '',
    message: 'Dear Parent/Guardian,\n\nWe are pleased to inform you that your child\'s academic report for this term is now ready for collection. Please visit the school office during working hours to collect the report.\n\nThank you for your continued support.\n\nBest regards,\nSchool Administration'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    setFormData({
      selectedClass: '',
      message: 'Dear Parent/Guardian,\n\nWe are pleased to inform you that your child\'s academic report for this term is now ready for collection. Please visit the school office during working hours to collect the report.\n\nThank you for your continued support.\n\nBest regards,\nSchool Administration'
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notify Parents About Reports
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="selectedClass">Select Class</Label>
            <Select value={formData.selectedClass} onValueChange={(value) => setFormData({...formData, selectedClass: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Choose class to notify" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Classes</SelectItem>
                {classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.name}>
                    {cls.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="message">Notification Message</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => setFormData({...formData, message: e.target.value})}
              placeholder="Enter message to send to parents..."
              className="min-h-[150px]"
              required
            />
          </div>
          
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-700">
              <strong>Note:</strong> This notification will be sent to all parents of students in the selected class to inform them that their child's academic report is ready for collection.
            </p>
          </div>
          
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
              <Bell className="h-4 w-4 mr-2" />
              Send Notifications
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}