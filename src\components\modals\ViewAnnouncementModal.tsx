import React from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  Di<PERSON>Footer, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Megaphone, Users, Pin, MessageSquare, Share2, Printer } from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface Announcement {
  id: string;
  title: string;
  content: string;
  author: string;
  date: string;
  type: string;
  audience: string[];
  isPinned: boolean;
  comments: number;
}

interface ViewAnnouncementModalProps {
  isOpen: boolean;
  onClose: () => void;
  announcement: Announcement | null;
}

export function ViewAnnouncementModal({ isOpen, onClose, announcement }: ViewAnnouncementModalProps) {
  if (!announcement) return null;

  const getAnnouncementTypeIcon = (type: string) => {
    switch (type) {
      case 'event':
        return <Calendar className="h-5 w-5 text-primary" />;
      case 'notice':
        return <Megaphone className="h-5 w-5 text-warning" />;
      case 'academic':
        return <Users className="h-5 w-5 text-success" />;
      default:
        return null;
    }
  };

  const getAnnouncementTypeColor = (type: string) => {
    switch (type) {
      case 'event':
        return 'bg-primary/10 text-primary hover:bg-primary/20';
      case 'notice':
        return 'bg-warning/10 text-warning hover:bg-warning/20';
      case 'academic':
        return 'bg-success/10 text-success hover:bg-success/20';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const formatAudience = (audience: string[]) => {
    if (audience.includes('all')) return 'Everyone';
    return audience.map(a => a.charAt(0).toUpperCase() + a.slice(1)).join(', ');
  };

  // Mock comments for the announcement
  const mockComments = [
    {
      id: '1',
      author: 'Sarah Johnson',
      content: 'Thank you for the information. Will there be any preparation required for the event?',
      date: new Date(new Date(announcement.date).getTime() + 3600000).toISOString(),
    },
    {
      id: '2',
      author: 'Robert Wilson',
      content: 'Looking forward to this event. Is there a specific dress code?',
      date: new Date(new Date(announcement.date).getTime() + 7200000).toISOString(),
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-md bg-muted">
              {getAnnouncementTypeIcon(announcement.type)}
            </div>
            <DialogTitle className="text-xl">{announcement.title}</DialogTitle>
            {announcement.isPinned && (
              <Pin className="h-4 w-4 text-primary" />
            )}
          </div>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className={getAnnouncementTypeColor(announcement.type)}>
              {announcement.type.charAt(0).toUpperCase() + announcement.type.slice(1)}
            </Badge>
            <DialogDescription>
              Posted {formatDistanceToNow(new Date(announcement.date), { addSuffix: true })}
            </DialogDescription>
          </div>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="prose prose-sm max-w-none">
            <p className="text-foreground whitespace-pre-line">
              {announcement.content}
            </p>
          </div>
          
          <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <span className="font-medium">Date:</span>
              <span>{format(new Date(announcement.date), 'MMMM d, yyyy')}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-medium">Time:</span>
              <span>{format(new Date(announcement.date), 'h:mm a')}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-medium">Author:</span>
              <span>{announcement.author}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-medium">Audience:</span>
              <span>{formatAudience(announcement.audience)}</span>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Comments ({announcement.comments})</h3>
              <Button variant="ghost" size="sm" className="text-xs">
                View all
              </Button>
            </div>
            
            <div className="space-y-4">
              {mockComments.map(comment => (
                <div key={comment.id} className="flex gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-primary/20 text-primary text-xs">
                      {comment.author.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">{comment.author}</p>
                      <span className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(comment.date), { addSuffix: true })}
                      </span>
                    </div>
                    <p className="text-sm mt-1">{comment.content}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
          <div className="flex gap-2 w-full sm:w-auto">
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <Share2 className="h-4 w-4" />
              <span>Share</span>
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <Printer className="h-4 w-4" />
              <span>Print</span>
            </Button>
          </div>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}