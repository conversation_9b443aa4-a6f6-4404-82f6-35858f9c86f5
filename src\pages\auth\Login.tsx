import React, { useState,useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, GraduationCap } from 'lucide-react';
import { ROLES } from '@/lib/constants';
import { showToast } from '@/lib/utils';

const loginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(1, { message: 'Password is required' }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export function Login() {
  const { login, isSuperAdmin, isHeadmaster, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  // Effect to handle navigation after successful login
  useEffect(() => {
    if (user) {
      if (isSuperAdmin) {
        navigate('/admin/dashboard');
      } else if (isHeadmaster) {
        navigate('/school/dashboard');
      } else {
        navigate('/');
      }
    }
  }, [user, isSuperAdmin, isHeadmaster, navigate]);

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    try {
      await login(data.email, data.password);
      showToast.success('Login successful', 'Welcome back!');
    } catch (error) {
      console.error('Login error:', error);
      showToast.error('Login failed', 'Invalid email or password');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex">
      {/* Left Side - Enhanced Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 p-12 flex-col justify-center relative overflow-hidden">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 right-20 w-32 h-32 border border-white rounded-full"></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 border border-white rounded-full"></div>
        </div>
        
        <div className="text-center relative z-10">
          <div className="bg-white/15 p-5 rounded-2xl backdrop-blur-sm mb-8 mx-auto w-fit shadow-lg">
            <GraduationCap className="h-14 w-14 text-white" />
          </div>
          <h1 className="text-5xl font-bold text-white mb-4">EduManage</h1>
          <p className="text-blue-100 text-xl mb-8 font-medium">School Management System</p>
          <p className="text-blue-100 text-center max-w-md mx-auto text-lg leading-relaxed">
            Comprehensive school management solution for modern educational institutions.
          </p>
        </div>
      </div>
      
      {/* Right Side - Login Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Mobile Logo */}
          <div className="lg:hidden text-center mb-10">
            <div className="bg-gradient-to-br from-blue-600 to-indigo-600 p-4 rounded-2xl mx-auto w-fit mb-4 shadow-lg">
              <GraduationCap className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">EduManage</h1>
            <p className="text-gray-600 text-lg">School Management System</p>
          </div>
          
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="text-center pb-8 pt-8">
              <CardTitle className="text-3xl font-bold text-gray-900 mb-2">Welcome Back</CardTitle>
              <CardDescription className="text-gray-600 text-lg">
                Sign in to access your dashboard
              </CardDescription>
            </CardHeader>
            
            <CardContent className="px-8">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium">Email Address</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your email address" 
                            className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium">Password</FormLabel>
                        <FormControl>
                          <Input 
                            type="password" 
                            placeholder="Enter your password" 
                            className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button 
                    type="submit" 
                    className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200" 
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Signing in...
                      </>
                    ) : (
                      'Sign In to Dashboard'
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
            
            {/* <CardFooter className="px-8 pb-8">
              <div className="w-full">
                <div className="border-t border-gray-200 pt-6">
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-700 mb-4">Demo Credentials</p>
                    <div className="space-y-3 text-sm">
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                        <p className="font-semibold text-blue-800">Super Admin</p>
                        <p className="text-blue-600"><EMAIL> • password</p>
                      </div>
                      <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-100">
                        <p className="font-semibold text-emerald-800">Headmaster</p>
                        <p className="text-emerald-600"><EMAIL> • password</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardFooter> */}
          </Card>
          
          <div className="text-center mt-8">
            <p className="text-sm text-gray-500">
              © 2024 EduManage. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}