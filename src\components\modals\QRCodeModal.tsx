import React from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Download,
  Printer,
  Share2,
  QrCode
} from 'lucide-react';

interface QRCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export function QRCodeModal({ isOpen, onClose, className }: QRCodeModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Attendance QR Code</DialogTitle>
          <DialogDescription>
            Scan this QR code with the mobile app to take attendance
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="class" className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="class">Class QR Code</TabsTrigger>
            <TabsTrigger value="student">Student QR Code</TabsTrigger>
          </TabsList>
          
          <TabsContent value="class" className="flex flex-col items-center space-y-4">
            <div className="border-2 border-dashed border-muted-foreground/20 rounded-lg p-8 flex items-center justify-center">
              <div className="w-48 h-48 bg-muted/20 rounded-md flex items-center justify-center">
                <QrCode className="h-24 w-24 text-primary" />
              </div>
            </div>
            <div className="text-center">
              <p className="font-medium">Class 10A</p>
              <p className="text-sm text-muted-foreground">Valid for today's attendance</p>
            </div>
            <p className="text-xs text-muted-foreground text-center max-w-xs">
              Display this QR code in your classroom. Students can scan it with the mobile app to mark their attendance.
            </p>
          </TabsContent>
          
          <TabsContent value="student" className="flex flex-col items-center space-y-4">
            <div className="border-2 border-dashed border-muted-foreground/20 rounded-lg p-8 flex items-center justify-center">
              <div className="w-48 h-48 bg-muted/20 rounded-md flex items-center justify-center">
                <QrCode className="h-24 w-24 text-primary" />
              </div>
            </div>
            <div className="text-center">
              <p className="font-medium">Generate Student QR Codes</p>
              <p className="text-sm text-muted-foreground">For student ID cards</p>
            </div>
            <p className="text-xs text-muted-foreground text-center max-w-xs">
              Generate QR codes for individual students to be printed on their ID cards. Teachers can scan these codes to mark attendance.
            </p>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-center gap-2 mt-4">
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            <span>Download</span>
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Printer className="h-4 w-4" />
            <span>Print</span>
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Share2 className="h-4 w-4" />
            <span>Share</span>
          </Button>
        </div>
        
        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button className="gradient-bg text-white hover:opacity-90">
            Generate New Code
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}