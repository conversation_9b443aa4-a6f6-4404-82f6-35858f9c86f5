import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { 
  Award,
  Plus,
  Search,
  Edit,
  Trash2,
  MoreHorizontal,
  Loader2,
  Settings,
  CheckCircle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CreateGradingSystemModal } from '@/components/modals/CreateGradingSystemModal';
import { EditGradingSystemModal } from '@/components/modals/EditGradingSystemModal';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

interface GradeScale {
  grade: string;
  minScore: number;
  maxScore: number;
  gpa: number;
  remark: string;
}

interface GradingSystem {
  id: string;
  name: string;
  type: 'predefined' | 'custom';
  scales: GradeScale[];
  classes: string[];
  active: boolean;
  createdAt: string;
}

export function GradingSystemPage() {
  const [loading, setLoading] = useState(true);
  const [gradingSystems, setGradingSystems] = useState<GradingSystem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedSystem, setSelectedSystem] = useState<GradingSystem | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  useEffect(() => {
    fetchGradingSystems();
  }, []);

  const fetchGradingSystems = async () => {
    try {
      setLoading(true);
      // Mock data with GES predefined system
      const mockSystems: GradingSystem[] = [
        {
          id: '1',
          name: 'GES Grading System',
          type: 'predefined',
          scales: [
            { grade: 'A', minScore: 80, maxScore: 100, gpa: 4.0, remark: 'Excellent' },
            { grade: 'B', minScore: 70, maxScore: 79, gpa: 3.0, remark: 'Very Good' },
            { grade: 'C', minScore: 60, maxScore: 69, gpa: 2.0, remark: 'Good' },
            { grade: 'D', minScore: 50, maxScore: 59, gpa: 1.0, remark: 'Satisfactory' },
            { grade: 'E', minScore: 40, maxScore: 49, gpa: 0.5, remark: 'Pass' },
            { grade: 'F', minScore: 0, maxScore: 39, gpa: 0.0, remark: 'Fail' }
          ],
          classes: ['All Classes'],
          active: true,
          createdAt: '2024-01-01'
        },
        {
          id: '2',
          name: 'Primary School Grading',
          type: 'custom',
          scales: [
            { grade: 'Excellent', minScore: 90, maxScore: 100, gpa: 4.0, remark: 'Outstanding Performance' },
            { grade: 'Very Good', minScore: 80, maxScore: 89, gpa: 3.5, remark: 'Very Good Performance' },
            { grade: 'Good', minScore: 70, maxScore: 79, gpa: 3.0, remark: 'Good Performance' },
            { grade: 'Satisfactory', minScore: 60, maxScore: 69, gpa: 2.0, remark: 'Satisfactory Performance' },
            { grade: 'Needs Improvement', minScore: 0, maxScore: 59, gpa: 1.0, remark: 'Needs Improvement' }
          ],
          classes: ['Primary 1', 'Primary 2', 'Primary 3'],
          active: false,
          createdAt: '2024-02-15'
        }
      ];
      setGradingSystems(mockSystems);
    } catch (error) {
      console.error('Error fetching grading systems:', error);
      showToast.error('Error', 'Failed to load grading systems');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSystem = async (data: any) => {
    try {
      // Check for class conflicts if system is set to active
      if (data.active) {
        const activeSystemsWithConflicts = gradingSystems.filter(system => 
          system.active &&
          system.classes.some(cls => data.classes.includes(cls))
        );
        
        if (activeSystemsWithConflicts.length > 0) {
          const conflictingClasses = data.classes.filter(cls => 
            activeSystemsWithConflicts.some(system => system.classes.includes(cls))
          );
          showToast.error('Error', `Cannot create active system: Classes ${conflictingClasses.join(', ')} are already assigned to other active systems`);
          return;
        }
      }
      
      const newSystem: GradingSystem = {
        id: (gradingSystems.length + 1).toString(),
        name: data.name,
        type: 'custom',
        scales: data.scales,
        classes: data.classes,
        active: data.active || false,
        createdAt: new Date().toISOString().split('T')[0]
      };
      setGradingSystems([...gradingSystems, newSystem]);
      setIsCreateModalOpen(false);
      showToast.success('Success', 'Grading system created successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to create grading system');
    }
  };

  const handleEditSystem = (system: GradingSystem) => {
    setSelectedSystem(system);
    setIsEditModalOpen(true);
  };

  const handleUpdateSystem = async (data: any) => {
    if (!selectedSystem) return;
    try {
      // Check for class conflicts if system is set to active
      if (data.active) {
        const activeSystemsWithConflicts = gradingSystems.filter(system => 
          system.active &&
          system.id !== selectedSystem.id &&
          system.classes.some(cls => data.classes.includes(cls))
        );
        
        if (activeSystemsWithConflicts.length > 0) {
          const conflictingClasses = data.classes.filter(cls => 
            activeSystemsWithConflicts.some(system => system.classes.includes(cls))
          );
          showToast.error('Error', `Cannot update to active: Classes ${conflictingClasses.join(', ')} are already assigned to other active systems`);
          return;
        }
      }
      
      const updatedSystems = gradingSystems.map(system => 
        system.id === selectedSystem.id ? { ...system, ...data } : system
      );
      setGradingSystems(updatedSystems);
      setIsEditModalOpen(false);
      showToast.success('Success', 'Grading system updated successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to update grading system');
    }
  };

  const handleDeleteSystem = (system: GradingSystem) => {
    setSelectedSystem(system);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedSystem) return;
    try {
      const updatedSystems = gradingSystems.filter(system => system.id !== selectedSystem.id);
      setGradingSystems(updatedSystems);
      setIsDeleteDialogOpen(false);
      showToast.success('Success', 'Grading system deleted successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to delete grading system');
    }
  };

  const handleActivateSystem = async (systemId: string) => {
    try {
      const systemToActivate = gradingSystems.find(s => s.id === systemId);
      if (!systemToActivate) return;
      
      // Check for class conflicts with other active systems
      const activeSystemsWithConflicts = gradingSystems.filter(system => 
        system.active && 
        system.id !== systemId &&
        system.classes.some(cls => systemToActivate.classes.includes(cls))
      );
      
      if (activeSystemsWithConflicts.length > 0) {
        const conflictingClasses = systemToActivate.classes.filter(cls => 
          activeSystemsWithConflicts.some(system => system.classes.includes(cls))
        );
        showToast.error('Error', `Cannot activate: Classes ${conflictingClasses.join(', ')} are already assigned to other active systems`);
        return;
      }
      
      const updatedSystems = gradingSystems.map(system => 
        system.id === systemId ? { ...system, active: true } : system
      );
      setGradingSystems(updatedSystems);
      showToast.success('Success', 'Grading system activated successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to activate grading system');
    }
  };

  const filteredSystems = gradingSystems.filter(system =>
    system.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    system.type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const activeSystems = gradingSystems.filter(system => system.active);

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-amber-900 via-orange-800 to-red-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <Award className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Grading System</h1>
                <p className="text-amber-200 mt-1">Configure exam grading and assessment scales</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={() => setIsCreateModalOpen(true)}
              className="bg-white text-amber-900 hover:bg-amber-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>Create Custom System</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-3 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Total Systems</p>
              <p className="text-3xl font-bold text-blue-900">{gradingSystems.length}</p>
            </div>
            <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Settings className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-green-100/50 border-green-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-700">Active Systems</p>
              <p className="text-3xl font-bold text-green-900">{activeSystems.length}</p>
            </div>
            <div className="bg-green-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700">Custom Systems</p>
              <p className="text-3xl font-bold text-purple-900">{gradingSystems.filter(s => s.type === 'custom').length}</p>
            </div>
            <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Award className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <Award className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">Grading Systems</CardTitle>
                <p className="text-sm text-slate-600">Manage exam grading configurations</p>
              </div>
            </div>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search systems..."
                className="pl-10 w-64 border-slate-200 focus:border-amber-500 focus:ring-amber-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <Tabs defaultValue="systems" className="w-full">
            <div className="px-6 pt-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="systems">All Systems</TabsTrigger>
                <TabsTrigger value="active">Active System Details</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="systems" className="mt-0">
              <div className="p-6">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Classes</TableHead>
                        <TableHead>Grade Scales</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex justify-center items-center">
                              <Loader2 className="h-6 w-6 animate-spin mr-2" />
                              <span>Loading...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredSystems.length > 0 ? (
                        filteredSystems.map((system) => (
                          <TableRow key={system.id}>
                            <TableCell className="font-semibold">{system.name}</TableCell>
                            <TableCell>
                              <Badge variant={system.type === 'predefined' ? 'default' : 'outline'}>
                                {system.type === 'predefined' ? 'Pre-configured' : 'Custom'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {system.classes.slice(0, 2).map((cls, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {cls}
                                  </Badge>
                                ))}
                                {system.classes.length > 2 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{system.classes.length - 2} more
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>{system.scales.length} grades</TableCell>
                            <TableCell>
                              <Badge variant={system.active ? 'default' : 'secondary'}>
                                {system.active ? 'Active' : 'Inactive'}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  {!system.active && (
                                    <DropdownMenuItem onClick={() => handleActivateSystem(system.id)}>
                                      <CheckCircle className="h-4 w-4 mr-2" />
                                      Activate
                                    </DropdownMenuItem>
                                  )}
                                  <DropdownMenuItem onClick={() => handleEditSystem(system)}>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                  {system.type === 'custom' && (
                                    <DropdownMenuItem 
                                      onClick={() => handleDeleteSystem(system)}
                                      className="text-red-600"
                                    >
                                      <Trash2 className="h-4 w-4 mr-2" />
                                      Delete
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                            No grading systems found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="active" className="mt-0">
              <div className="p-6">
                {activeSystems.length > 0 ? (
                  <div className="space-y-8">
                    {activeSystems.map((system) => (
                      <div key={system.id} className="border rounded-lg p-6">
                        <div className="flex justify-between items-center mb-4">
                          <div>
                            <h3 className="text-xl font-semibold">{system.name}</h3>
                            <p className="text-sm text-slate-600">Active grading system</p>
                          </div>
                          <Badge variant="default" className="bg-green-600">Active</Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                          <div>
                            <h4 className="font-semibold mb-3">Applied Classes</h4>
                            <div className="flex flex-wrap gap-2">
                              {system.classes.map((cls, index) => (
                                <Badge key={index} variant="outline">{cls}</Badge>
                              ))}
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold mb-3">System Type</h4>
                            <Badge variant={system.type === 'predefined' ? 'default' : 'outline'}>
                              {system.type === 'predefined' ? 'Pre-configured (GES)' : 'Custom System'}
                            </Badge>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-semibold mb-3">Grade Scale</h4>
                          <div className="overflow-x-auto">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Grade</TableHead>
                                  <TableHead>Score Range</TableHead>
                                  <TableHead>GPA</TableHead>
                                  <TableHead>Remark</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {system.scales.map((scale, index) => (
                                  <TableRow key={index}>
                                    <TableCell className="font-semibold">{scale.grade}</TableCell>
                                    <TableCell>{scale.minScore} - {scale.maxScore}</TableCell>
                                    <TableCell>{scale.gpa}</TableCell>
                                    <TableCell>{scale.remark}</TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Award className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No active grading systems</p>
                    <p className="text-sm">Activate grading systems to see details here</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Modals */}
      <CreateGradingSystemModal 
        open={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateSystem}
      />
      
      {selectedSystem && (
        <EditGradingSystemModal
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={handleUpdateSystem}
          system={selectedSystem}
        />
      )}
      
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the grading system
              {selectedSystem && ` "${selectedSystem.name}"`}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}