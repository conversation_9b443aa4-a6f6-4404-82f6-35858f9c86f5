import React from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Mail, Phone, Calendar, BookOpen, Award } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface ViewTeacherModalProps {
  open: boolean;
  onClose: () => void;
  teacher: any;
}

export function ViewTeacherModal({ open, onClose, teacher }: ViewTeacherModalProps) {
  if (!teacher) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Teacher Profile</DialogTitle>
          <DialogDescription>
            Detailed information about {teacher.name}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex flex-col md:flex-row gap-6 py-4">
          <div className="flex flex-col items-center space-y-3">
            <Avatar className="h-24 w-24">
              <AvatarFallback className="text-2xl bg-primary/20 text-primary">
                {teacher.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <Badge 
              variant={teacher.status === 'active' ? 'success' : 'secondary'}
              className={
                teacher.status === 'active' 
                  ? 'status-active' 
                  : 'status-inactive'
              }
            >
              {teacher.status === 'active' ? 'Active' : 'Inactive'}
            </Badge>
          </div>
          
          <div className="flex-1 space-y-4">
            <div>
              <h3 className="text-xl font-bold">{teacher.name}</h3>
              <div className="flex items-center mt-1">
                <BookOpen className="h-4 w-4 mr-2 text-primary" />
                <span className="text-muted-foreground">
                  {Array.isArray(teacher.subjects) ? teacher.subjects.join(', ') : teacher.subject} Teacher
                </span>
              </div>
            </div>
            
            <Separator />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{teacher.email}</span>
              </div>
              <div className="flex items-center">
                <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{teacher.phone}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>Joined {format(new Date(teacher.joinDate), 'MMMM d, yyyy')}</span>
              </div>
              <div className="flex items-center">
                <Award className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>4 years experience</span>
              </div>
            </div>
            
            <Separator />
            
            <div>
              <h4 className="text-sm font-medium mb-2">Subjects</h4>
              <div className="flex flex-wrap gap-2 mb-4">
                {Array.isArray(teacher.subjects) ? (
                  teacher.subjects.map((subject: string, index: number) => (
                    <Badge key={index} variant="outline" className="bg-success/10 text-success hover:bg-success/20">
                      {subject}
                    </Badge>
                  ))
                ) : (
                  <Badge variant="outline" className="bg-success/10 text-success hover:bg-success/20">
                    {teacher.subject}
                  </Badge>
                )}
              </div>
              
              <h4 className="text-sm font-medium mb-2">Classes</h4>
              <div className="flex flex-wrap gap-2">
                {teacher.classes && teacher.classes.length > 0 ? (
                  teacher.classes.map((className: string, index: number) => (
                    <Badge key={index} variant="outline" className="bg-primary/10 text-primary hover:bg-primary/20">
                      {className}
                    </Badge>
                  ))
                ) : (
                  <span className="text-sm text-muted-foreground">No classes assigned</span>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button className="gradient-bg text-white hover:opacity-90">
            Edit Profile
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}