import React from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BookOpen, User, Users, Calendar, Clock, Award } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface ViewCourseModalProps {
  open: boolean;
  onClose: () => void;
  course: any;
}

export function ViewCourseModal({ open, onClose, course }: ViewCourseModalProps) {
  if (!course) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Course Details</DialogTitle>
          <DialogDescription>
            Detailed information about {course.name}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold">{course.name}</h3>
              <p className="text-muted-foreground">{course.code}</p>
            </div>
            <Badge 
              variant={course.active ? 'success' : 'secondary'}
              className={course.active ? 'status-active' : 'status-inactive'}
            >
              {course.active ? 'Active' : 'Inactive'}
            </Badge>
          </div>
          
          <Separator />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Teacher</p>
                  <p className="text-sm text-muted-foreground">{course.teacher || 'Not assigned'}</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <BookOpen className="h-4 w-4 mr-2 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Department</p>
                  <p className="text-sm text-muted-foreground">{course.department}</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <Award className="h-4 w-4 mr-2 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Credits</p>
                  <p className="text-sm text-muted-foreground">{course.credits}</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Enrolled Students</p>
                  <p className="text-sm text-muted-foreground">{course.students || 0}</p>
                </div>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div>
            <h4 className="text-sm font-medium mb-2">Course Description</h4>
            <p className="text-sm text-muted-foreground">
              {course.description || 'No description available for this course.'}
            </p>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}