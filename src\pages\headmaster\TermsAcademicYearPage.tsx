import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/common/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { 
  Calendar,
  Plus,
  Search,
  Edit,
  Trash2,
  MoreHorizontal,
  Loader2,
  CalendarDays,
  Clock
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AddAcademicYearModal } from '@/components/modals/AddAcademicYearModal';
import { AddTermModal } from '@/components/modals/AddTermModal';
import { EditAcademicYearModal } from '@/components/modals/EditAcademicYearModal';
import { EditTermModal } from '@/components/modals/EditTermModal';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

interface AcademicYear {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  terms: string[];
  active: boolean;
  createdAt: string;
}

interface Term {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  academicYear: string;
  active: boolean;
  createdAt: string;
}

export function TermsAcademicYearPage() {
  const [loading, setLoading] = useState(true);
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([]);
  const [terms, setTerms] = useState<Term[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddYearModalOpen, setIsAddYearModalOpen] = useState(false);
  const [isAddTermModalOpen, setIsAddTermModalOpen] = useState(false);
  const [isEditYearModalOpen, setIsEditYearModalOpen] = useState(false);
  const [isEditTermModalOpen, setIsEditTermModalOpen] = useState(false);
  const [selectedYear, setSelectedYear] = useState<AcademicYear | null>(null);
  const [selectedTerm, setSelectedTerm] = useState<Term | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deleteType, setDeleteType] = useState<'year' | 'term'>('year');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      // Mock data
      const mockYears: AcademicYear[] = [
        {
          id: '1',
          name: '2024/2025',
          startDate: '2024-09-01',
          endDate: '2025-07-31',
          terms: ['First Term', 'Second Term', 'Third Term'],
          active: true,
          createdAt: '2024-01-15'
        },
        {
          id: '2',
          name: '2023/2024',
          startDate: '2023-09-01',
          endDate: '2024-07-31',
          terms: ['First Term', 'Second Term', 'Third Term'],
          active: false,
          createdAt: '2023-01-15'
        }
      ];

      const mockTerms: Term[] = [
        {
          id: '1',
          name: 'First Term',
          startDate: '2024-09-01',
          endDate: '2024-12-15',
          academicYear: '2024/2025',
          active: true,
          createdAt: '2024-01-15'
        },
        {
          id: '2',
          name: 'Second Term',
          startDate: '2025-01-08',
          endDate: '2025-04-15',
          academicYear: '2024/2025',
          active: false,
          createdAt: '2024-01-15'
        },
        {
          id: '3',
          name: 'Third Term',
          startDate: '2025-04-22',
          endDate: '2025-07-31',
          academicYear: '2024/2025',
          active: false,
          createdAt: '2024-01-15'
        }
      ];

      setAcademicYears(mockYears);
      setTerms(mockTerms);
    } catch (error) {
      console.error('Error fetching data:', error);
      showToast.error('Error', 'Failed to load academic data');
    } finally {
      setLoading(false);
    }
  };

  const handleAddYear = async (data: any) => {
    try {
      const newYear: AcademicYear = {
        id: (academicYears.length + 1).toString(),
        name: data.name,
        startDate: data.startDate,
        endDate: data.endDate,
        terms: data.terms || [],
        active: data.active || false,
        createdAt: new Date().toISOString().split('T')[0]
      };
      setAcademicYears([...academicYears, newYear]);
      setIsAddYearModalOpen(false);
      showToast.success('Success', 'Academic year created successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to create academic year');
    }
  };

  const handleAddTerm = async (data: any) => {
    try {
      const newTerm: Term = {
        id: (terms.length + 1).toString(),
        name: data.name,
        startDate: data.startDate,
        endDate: data.endDate,
        academicYear: data.academicYear,
        active: data.active || false,
        createdAt: new Date().toISOString().split('T')[0]
      };
      setTerms([...terms, newTerm]);
      setIsAddTermModalOpen(false);
      showToast.success('Success', 'Term created successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to create term');
    }
  };

  const handleEditYear = (year: AcademicYear) => {
    setSelectedYear(year);
    setIsEditYearModalOpen(true);
  };

  const handleEditTerm = (term: Term) => {
    setSelectedTerm(term);
    setIsEditTermModalOpen(true);
  };

  const handleUpdateYear = async (data: any) => {
    if (!selectedYear) return;
    try {
      const updatedYears = academicYears.map(year => 
        year.id === selectedYear.id ? { ...year, ...data } : year
      );
      setAcademicYears(updatedYears);
      setIsEditYearModalOpen(false);
      showToast.success('Success', 'Academic year updated successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to update academic year');
    }
  };

  const handleUpdateTerm = async (data: any) => {
    if (!selectedTerm) return;
    try {
      const updatedTerms = terms.map(term => 
        term.id === selectedTerm.id ? { ...term, ...data } : term
      );
      setTerms(updatedTerms);
      setIsEditTermModalOpen(false);
      showToast.success('Success', 'Term updated successfully');
    } catch (error) {
      showToast.error('Error', 'Failed to update term');
    }
  };

  const handleDelete = (item: AcademicYear | Term, type: 'year' | 'term') => {
    if (type === 'year') {
      setSelectedYear(item as AcademicYear);
    } else {
      setSelectedTerm(item as Term);
    }
    setDeleteType(type);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    try {
      if (deleteType === 'year' && selectedYear) {
        const updatedYears = academicYears.filter(year => year.id !== selectedYear.id);
        setAcademicYears(updatedYears);
      } else if (deleteType === 'term' && selectedTerm) {
        const updatedTerms = terms.filter(term => term.id !== selectedTerm.id);
        setTerms(updatedTerms);
      }
      setIsDeleteDialogOpen(false);
      showToast.success('Success', `${deleteType === 'year' ? 'Academic year' : 'Term'} deleted successfully`);
    } catch (error) {
      showToast.error('Error', `Failed to delete ${deleteType}`);
    }
  };

  const filteredYears = academicYears.filter(year =>
    year.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredTerms = terms.filter(term =>
    term.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    term.academicYear.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Layout>
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-emerald-900 via-teal-800 to-cyan-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Terms & Academic Year</h1>
                <p className="text-emerald-200 mt-1">Manage school calendar and academic periods</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-3 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Academic Years</p>
              <p className="text-3xl font-bold text-blue-900">{academicYears.length}</p>
            </div>
            <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <CalendarDays className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-green-100/50 border-green-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-700">Total Terms</p>
              <p className="text-3xl font-bold text-green-900">{terms.length}</p>
            </div>
            <div className="bg-green-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700">Active Year</p>
              <p className="text-2xl font-bold text-purple-900">{academicYears.find(y => y.active)?.name || 'None'}</p>
            </div>
            <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Calendar className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl">
        <CardHeader className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">Academic Calendar</CardTitle>
                <p className="text-sm text-slate-600">Manage academic years and terms</p>
              </div>
            </div>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search..."
                className="pl-10 w-64 border-slate-200 focus:border-emerald-500 focus:ring-emerald-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <Tabs defaultValue="years" className="w-full">
            <div className="px-6 pt-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="years">Academic Years</TabsTrigger>
                <TabsTrigger value="terms">Terms</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="years" className="mt-0">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">Academic Years</h3>
                  <Button onClick={() => setIsAddYearModalOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Academic Year
                  </Button>
                </div>
                
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>End Date</TableHead>
                        <TableHead>Terms</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex justify-center items-center">
                              <Loader2 className="h-6 w-6 animate-spin mr-2" />
                              <span>Loading...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredYears.length > 0 ? (
                        filteredYears.map((year) => (
                          <TableRow key={year.id}>
                            <TableCell className="font-semibold">{year.name}</TableCell>
                            <TableCell>{new Date(year.startDate).toLocaleDateString()}</TableCell>
                            <TableCell>{new Date(year.endDate).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {year.terms.map((term, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {term}
                                  </Badge>
                                ))}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant={year.active ? 'default' : 'secondary'}>
                                {year.active ? 'Active' : 'Inactive'}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => handleEditYear(year)}>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuItem 
                                    onClick={() => handleDelete(year, 'year')}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                            No academic years found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="terms" className="mt-0">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">Terms</h3>
                  <Button onClick={() => setIsAddTermModalOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Term
                  </Button>
                </div>
                
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Academic Year</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>End Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex justify-center items-center">
                              <Loader2 className="h-6 w-6 animate-spin mr-2" />
                              <span>Loading...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredTerms.length > 0 ? (
                        filteredTerms.map((term) => (
                          <TableRow key={term.id}>
                            <TableCell className="font-semibold">{term.name}</TableCell>
                            <TableCell>{term.academicYear}</TableCell>
                            <TableCell>{new Date(term.startDate).toLocaleDateString()}</TableCell>
                            <TableCell>{new Date(term.endDate).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <Badge variant={term.active ? 'default' : 'secondary'}>
                                {term.active ? 'Active' : 'Inactive'}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => handleEditTerm(term)}>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuItem 
                                    onClick={() => handleDelete(term, 'term')}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                            No terms found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Modals */}
      <AddAcademicYearModal 
        open={isAddYearModalOpen}
        onClose={() => setIsAddYearModalOpen(false)}
        onSubmit={handleAddYear}
      />
      
      <AddTermModal 
        open={isAddTermModalOpen}
        onClose={() => setIsAddTermModalOpen(false)}
        onSubmit={handleAddTerm}
        academicYears={academicYears}
      />
      
      {selectedYear && (
        <EditAcademicYearModal
          open={isEditYearModalOpen}
          onClose={() => setIsEditYearModalOpen(false)}
          onSubmit={handleUpdateYear}
          year={selectedYear}
        />
      )}
      
      {selectedTerm && (
        <EditTermModal
          open={isEditTermModalOpen}
          onClose={() => setIsEditTermModalOpen(false)}
          onSubmit={handleUpdateTerm}
          term={selectedTerm}
          academicYears={academicYears}
        />
      )}
      
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the {deleteType === 'year' ? 'academic year' : 'term'}
              {deleteType === 'year' && selectedYear && ` ${selectedYear.name}`}
              {deleteType === 'term' && selectedTerm && ` ${selectedTerm.name}`}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}