import React, { useState } from 'react';
import { Layout } from '@/components/common/Layout';
import { 
  Plus, 
  Search, 
  Filter,
  BellRing,
  Calendar,
  Users,
  Megaphone,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Pin,
  MessageSquare,
  ArrowUpDown
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { format, formatDistanceToNow } from 'date-fns';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { AddAnnouncementModal, AddAnnouncementFormData } from '@/components/modals/AddAnnouncementModal';
import { EditAnnouncementModal, EditAnnouncementFormData } from '@/components/modals/EditAnnouncementModal';
import { ViewAnnouncementModal } from '@/components/modals/ViewAnnouncementModal';
import { useToast } from '@/components/ui/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

// Mock announcement data
const MOCK_ANNOUNCEMENTS = [
  { 
    id: '1', 
    title: 'Parent-Teacher Meeting', 
    content: 'The annual parent-teacher meeting will be held on November 15th from 3:00 PM to 6:00 PM. All parents are requested to attend.',
    author: 'Dr. Michael Johnson',
    date: '2023-11-01T10:30:00.000Z',
    type: 'event',
    audience: ['parents', 'teachers'],
    isPinned: true,
    comments: 5
  },
  { 
    id: '2', 
    title: 'Annual Sports Day', 
    content: 'The annual sports day will be held on November 20th. All students are expected to participate in at least one event.',
    author: 'Robert Wilson',
    date: '2023-11-02T09:15:00.000Z',
    type: 'event',
    audience: ['students', 'teachers'],
    isPinned: false,
    comments: 12
  },
  { 
    id: '3', 
    title: 'Science Exhibition', 
    content: 'The science exhibition will be held on November 25th. Students interested in participating should register with their science teachers by November 10th.',
    author: 'Sarah Johnson',
    date: '2023-11-03T14:45:00.000Z',
    type: 'academic',
    audience: ['students', 'teachers'],
    isPinned: false,
    comments: 8
  },
  { 
    id: '4', 
    title: 'Holiday Notice', 
    content: 'The school will remain closed from December 20th to January 5th for winter break. Classes will resume on January 6th.',
    author: 'Dr. Michael Johnson',
    date: '2023-11-05T11:00:00.000Z',
    type: 'notice',
    audience: ['all'],
    isPinned: true,
    comments: 3
  },
  { 
    id: '5', 
    title: 'Exam Schedule', 
    content: 'The final exams for this semester will begin on December 5th. The detailed schedule has been posted on the school notice board.',
    author: 'Emma Davis',
    date: '2023-11-07T13:20:00.000Z',
    type: 'academic',
    audience: ['students', 'parents'],
    isPinned: false,
    comments: 15
  },
  { 
    id: '6', 
    title: 'School Maintenance', 
    content: 'The school gymnasium will be closed for maintenance from November 10th to November 15th. Physical education classes will be held in the outdoor field during this period.',
    author: 'Robert Wilson',
    date: '2023-11-08T16:30:00.000Z',
    type: 'notice',
    audience: ['teachers', 'students'],
    isPinned: false,
    comments: 2
  },
];

export function AnnouncementsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [announcements, setAnnouncements] = useState(MOCK_ANNOUNCEMENTS);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<any>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { toast } = useToast();
  
  // Filter states
  const [typeFilter, setTypeFilter] = useState<string[]>([]);
  const [audienceFilter, setAudienceFilter] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<{key: string, direction: 'asc' | 'desc'} | null>(null);

  const filteredAnnouncements = announcements.filter(announcement => {
    // Text search filter
    const matchesSearch = announcement.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         announcement.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Tab filter
    const matchesTab = 
      activeTab === 'all' || 
      (activeTab === 'pinned' && announcement.isPinned) ||
      (activeTab === 'events' && announcement.type === 'event') ||
      (activeTab === 'notices' && announcement.type === 'notice') ||
      (activeTab === 'academic' && announcement.type === 'academic');
    
    // Type filter
    const matchesType = typeFilter.length === 0 || typeFilter.includes(announcement.type);
    
    // Audience filter
    const matchesAudience = audienceFilter.length === 0 || 
      announcement.audience.some(a => audienceFilter.includes(a)) ||
      (audienceFilter.includes('all') && announcement.audience.includes('all'));
    
    return matchesSearch && matchesTab && matchesType && matchesAudience;
  });
  
  // Apply sorting
  const sortedAnnouncements = React.useMemo(() => {
    let sorted = [...filteredAnnouncements];
    
    // First sort by pinned status
    sorted.sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      return 0;
    });
    
    // Then apply additional sorting if configured
    if (sortConfig) {
      sorted.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    } else {
      // Default sort by date if no sort config
      sorted.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    }
    
    return sorted;
  }, [filteredAnnouncements, sortConfig]);

  // Calculate summary statistics
  const totalAnnouncements = announcements.length;
  const pinnedAnnouncements = announcements.filter(a => a.isPinned).length;
  const eventAnnouncements = announcements.filter(a => a.type === 'event').length;
  const noticeAnnouncements = announcements.filter(a => a.type === 'notice').length;
  const academicAnnouncements = announcements.filter(a => a.type === 'academic').length;
  
  const getAnnouncementTypeIcon = (type: string) => {
    switch (type) {
      case 'event':
        return <Calendar className="h-5 w-5 text-primary" />;
      case 'notice':
        return <Megaphone className="h-5 w-5 text-warning" />;
      case 'academic':
        return <Users className="h-5 w-5 text-success" />;
      default:
        return <BellRing className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const getAnnouncementTypeColor = (type: string) => {
    switch (type) {
      case 'event':
        return 'bg-primary/10 text-primary hover:bg-primary/20';
      case 'notice':
        return 'bg-warning/10 text-warning hover:bg-warning/20';
      case 'academic':
        return 'bg-success/10 text-success hover:bg-success/20';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };
  
  const handleAddAnnouncement = (data: AddAnnouncementFormData) => {
    const newAnnouncement = {
      id: (announcements.length + 1).toString(),
      title: data.title,
      content: data.content,
      author: 'Dr. Michael Johnson', // Current user would be used in a real app
      date: new Date().toISOString(),
      type: data.type,
      audience: data.audience,
      isPinned: data.isPinned,
      comments: 0
    };
    
    setAnnouncements([newAnnouncement, ...announcements]);
    
    toast({
      title: "Announcement Published",
      description: `"${data.title}" has been successfully published.`,
    });
  };

  const handleViewAnnouncement = (announcement: any) => {
    setSelectedAnnouncement(announcement);
    setIsViewModalOpen(true);
  };
  
  const handleEditAnnouncement = (announcement: any) => {
    setSelectedAnnouncement(announcement);
    setIsEditModalOpen(true);
  };
  
  const handleUpdateAnnouncement = (data: EditAnnouncementFormData) => {
    if (!selectedAnnouncement) return;
    
    const updatedAnnouncements = announcements.map(announcement => {
      if (announcement.id === selectedAnnouncement.id) {
        return { 
          ...announcement, 
          title: data.title,
          content: data.content,
          type: data.type,
          audience: data.audience,
          isPinned: data.isPinned
        };
      }
      return announcement;
    });
    
    setAnnouncements(updatedAnnouncements);
    
    toast({
      title: "Announcement Updated",
      description: `"${data.title}" has been updated successfully.`,
    });
  };

  const handleDeleteAnnouncement = (announcement: any) => {
    setSelectedAnnouncement(announcement);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteAnnouncement = () => {
    if (selectedAnnouncement) {
      setAnnouncements(announcements.filter(a => a.id !== selectedAnnouncement.id));
      setIsDeleteDialogOpen(false);
      
      toast({
        title: "Announcement Deleted",
        description: `"${selectedAnnouncement.title}" has been deleted.`,
      });
    }
  };
  
  const handleTogglePin = (announcement: any) => {
    const updatedAnnouncements = announcements.map(a => {
      if (a.id === announcement.id) {
        return { ...a, isPinned: !a.isPinned };
      }
      return a;
    });
    
    setAnnouncements(updatedAnnouncements);
    
    toast({
      title: announcement.isPinned ? "Announcement Unpinned" : "Announcement Pinned",
      description: `"${announcement.title}" has been ${announcement.isPinned ? 'unpinned' : 'pinned'}.`,
    });
  };
  
  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    
    setSortConfig({ key, direction });
  };
  
  const clearFilters = () => {
    setTypeFilter([]);
    setAudienceFilter([]);
    setSortConfig(null);
    setActiveTab('all');
    setSearchQuery('');
  };

  return (
    <Layout>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Announcements</h1>
          <p className="text-muted-foreground mt-1">
            Manage and publish school announcements
          </p>
        </div>
        <Button 
          className="flex items-center gap-2 gradient-bg text-white hover:opacity-90"
          onClick={() => setIsAddModalOpen(true)}
        >
          <Plus className="h-4 w-4" />
          <span>New Announcement</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card className="hover-lift">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <BellRing className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAnnouncements}</div>
            <p className="text-xs text-muted-foreground mt-1">
              All announcements
            </p>
          </CardContent>
        </Card>
        <Card className="hover-lift">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Pinned</CardTitle>
            <Pin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pinnedAnnouncements}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Important announcements
            </p>
          </CardContent>
        </Card>
        <Card className="hover-lift">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Events</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{eventAnnouncements}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Upcoming school events
            </p>
          </CardContent>
        </Card>
        <Card className="hover-lift">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Notices</CardTitle>
            <Megaphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{noticeAnnouncements + academicAnnouncements}</div>
            <p className="text-xs text-muted-foreground mt-1">
              General and academic notices
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search announcements..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto justify-between sm:justify-end">
          <Tabs defaultValue={activeTab} value={activeTab} className="w-full sm:w-auto" onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="pinned">Pinned</TabsTrigger>
              <TabsTrigger value="events">Events</TabsTrigger>
              <TabsTrigger value="notices">Notices</TabsTrigger>
              <TabsTrigger value="academic">Academic</TabsTrigger>
            </TabsList>
          </Tabs>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Filter className="h-4 w-4" />
                <span>Filter</span>
                {(typeFilter.length > 0 || audienceFilter.length > 0) && (
                  <Badge variant="secondary" className="ml-1 h-5 px-1.5">
                    {typeFilter.length + audienceFilter.length}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="end">
              <div className="p-4 border-b">
                <h4 className="font-medium">Filters</h4>
                <p className="text-sm text-muted-foreground">
                  Filter announcements by type and audience
                </p>
              </div>
              <div className="p-4 space-y-4">
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">Type</h5>
                  <div className="grid grid-cols-3 gap-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="type-event" 
                        checked={typeFilter.includes('event')}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setTypeFilter([...typeFilter, 'event']);
                          } else {
                            setTypeFilter(typeFilter.filter(t => t !== 'event'));
                          }
                        }}
                      />
                      <Label htmlFor="type-event" className="text-sm">Events</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="type-notice" 
                        checked={typeFilter.includes('notice')}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setTypeFilter([...typeFilter, 'notice']);
                          } else {
                            setTypeFilter(typeFilter.filter(t => t !== 'notice'));
                          }
                        }}
                      />
                      <Label htmlFor="type-notice" className="text-sm">Notices</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="type-academic" 
                        checked={typeFilter.includes('academic')}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setTypeFilter([...typeFilter, 'academic']);
                          } else {
                            setTypeFilter(typeFilter.filter(t => t !== 'academic'));
                          }
                        }}
                      />
                      <Label htmlFor="type-academic" className="text-sm">Academic</Label>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">Audience</h5>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="audience-all" 
                        checked={audienceFilter.includes('all')}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setAudienceFilter([...audienceFilter, 'all']);
                          } else {
                            setAudienceFilter(audienceFilter.filter(a => a !== 'all'));
                          }
                        }}
                      />
                      <Label htmlFor="audience-all" className="text-sm">Everyone</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="audience-students" 
                        checked={audienceFilter.includes('students')}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setAudienceFilter([...audienceFilter, 'students']);
                          } else {
                            setAudienceFilter(audienceFilter.filter(a => a !== 'students'));
                          }
                        }}
                      />
                      <Label htmlFor="audience-students" className="text-sm">Students</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="audience-teachers" 
                        checked={audienceFilter.includes('teachers')}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setAudienceFilter([...audienceFilter, 'teachers']);
                          } else {
                            setAudienceFilter(audienceFilter.filter(a => a !== 'teachers'));
                          }
                        }}
                      />
                      <Label htmlFor="audience-teachers" className="text-sm">Teachers</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="audience-parents" 
                        checked={audienceFilter.includes('parents')}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setAudienceFilter([...audienceFilter, 'parents']);
                          } else {
                            setAudienceFilter(audienceFilter.filter(a => a !== 'parents'));
                          }
                        }}
                      />
                      <Label htmlFor="audience-parents" className="text-sm">Parents</Label>
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-4 border-t flex justify-between">
                <Button variant="outline" size="sm" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button size="sm" className="gradient-bg text-white hover:opacity-90">
                  Apply Filters
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {sortedAnnouncements.length > 0 ? (
          sortedAnnouncements.map((announcement) => (
            <Card key={announcement.id} className={`hover-lift ${announcement.isPinned ? 'border-l-4 border-l-primary' : ''}`}>
              <CardHeader className="flex flex-row items-start justify-between pb-2 space-y-0">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 rounded-md bg-muted">
                      {getAnnouncementTypeIcon(announcement.type)}
                    </div>
                    <CardTitle className="text-lg">{announcement.title}</CardTitle>
                    {announcement.isPinned && (
                      <Pin className="h-4 w-4 text-primary" />
                    )}
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className={getAnnouncementTypeColor(announcement.type)}>
                      {announcement.type.charAt(0).toUpperCase() + announcement.type.slice(1)}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(announcement.date), { addSuffix: true })}
                    </span>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem 
                      className="flex items-center gap-2 cursor-pointer"
                      onClick={() => handleViewAnnouncement(announcement)}
                    >
                      <Eye className="h-4 w-4" />
                      <span>View Details</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      className="flex items-center gap-2 cursor-pointer"
                      onClick={() => handleEditAnnouncement(announcement)}
                    >
                      <Edit className="h-4 w-4" />
                      <span>Edit Announcement</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      className="flex items-center gap-2 cursor-pointer"
                      onClick={() => handleTogglePin(announcement)}
                    >
                      <Pin className="h-4 w-4" />
                      <span>{announcement.isPinned ? 'Unpin' : 'Pin'} Announcement</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      className="flex items-center gap-2 text-destructive focus:text-destructive cursor-pointer"
                      onClick={() => handleDeleteAnnouncement(announcement)}
                    >
                      <Trash2 className="h-4 w-4" />
                      <span>Delete Announcement</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent className="pt-2">
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {announcement.content}
                </p>
              </CardContent>
              <CardFooter className="flex justify-between pt-2 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <span>By {announcement.author}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MessageSquare className="h-3.5 w-3.5" />
                  <span>{announcement.comments} comments</span>
                </div>
              </CardFooter>
            </Card>
          ))
        ) : (
          <div className="col-span-full flex items-center justify-center p-8 text-center text-muted-foreground">
            <div>
              <BellRing className="h-10 w-10 mx-auto mb-4 opacity-20" />
              <p>No announcements found matching your search criteria</p>
            </div>
          </div>
        )}
      </div>
      
      {/* Modals */}
      <AddAnnouncementModal 
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddAnnouncement}
      />
      
      <ViewAnnouncementModal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        announcement={selectedAnnouncement}
      />
      
      <EditAnnouncementModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSubmit={handleUpdateAnnouncement}
        announcement={selectedAnnouncement}
      />
      
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the announcement
              {selectedAnnouncement && ` "${selectedAnnouncement.title}"`} from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteAnnouncement}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}