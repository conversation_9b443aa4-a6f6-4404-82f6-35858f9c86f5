// Color constants
export const COLORS = {
  primary: {
    50: "#e6f0f2",
    100: "#cce1e5",
    200: "#99c3cb",
    300: "#66a5b1",
    400: "#338697",
    500: "#266774", // #266774
    600: "#1e525d",
    700: "#173e46",
    800: "#0f292e",
    900: "#081517",
    950: "#040a0b",
  },
  success: {
    50: "#ecfdf5",
    100: "#d1fae5",
    200: "#a7f3d0",
    300: "#6ee7b7",
    400: "#34d399",
    500: "#10b981",
    600: "#059669",
    700: "#047857",
    800: "#065f46",
    900: "#064e3b",
    950: "#022c22",
  },
  warning: {
    50: "#fffbeb",
    100: "#fef3c7",
    200: "#fde68a",
    300: "#fcd34d",
    400: "#fbbf24",
    500: "#f59e0b",
    600: "#d97706",
    700: "#b45309",
    800: "#92400e",
    900: "#78350f",
    950: "#451a03",
  },
  error: {
    50: "#fef2f2",
    100: "#fee2e2",
    200: "#fecaca",
    300: "#fca5a5",
    400: "#f87171",
    500: "#ef4444",
    600: "#dc2626",
    700: "#b91c1c",
    800: "#991b1b",
    900: "#7f1d1d",
    950: "#450a0a",
  },
  gray: {
    50: "#f8fafc",
    100: "#f1f5f9",
    200: "#e2e8f0",
    300: "#cbd5e1",
    400: "#94a3b8",
    500: "#64748b",
    600: "#475569",
    700: "#334155",
    800: "#1e293b",
    900: "#0f172a",
    950: "#020617",
  },
};

// Role constants
export const ROLES = {
  SUPER_ADMIN: "super_admin",
  HEADMASTER: "headmaster",
};

// Navigation items for different roles
export const SUPER_ADMIN_NAV_ITEMS = [
  { name: "Dashboard", path: "/admin/dashboard", icon: "LayoutDashboard" },
  { name: "Schools", path: "/admin/schools", icon: "Building2" },
  { name: "Analytics", path: "/admin/analytics", icon: "BarChart3" },
  { name: "Settings", path: "/admin/settings", icon: "Settings" },
];

export const HEADMASTER_NAV_ITEMS = [
  {
    name: "Dashboard",
    icon: "LayoutDashboard",
    path: "/headmaster/dashboard",
  },
  {
    name: "Users",
    path: "/headmaster/users",
    icon: "Users",
    subItems: [
      { name: "Teachers", path: "/headmaster/teachers", icon: "UserCog" },
      { name: "Students", path: "/headmaster/students", icon: "GraduationCap" },
      { name: "Parents", path: "/headmaster/parents", icon: "UserPlus" },
    ],
  },
  {
    name: "Academics",
    path: "/headmaster/academics",
    icon: "BookOpen",
    subItems: [
      { name: "Courses", path: "/headmaster/courses", icon: "BookOpen" },
      { name: "Classes", path: "/headmaster/classes", icon: "School" },
      { name: "Attendance", path: "/headmaster/attendance", icon: "ClipboardCheck" },
      { name: "Assessments", path: "/headmaster/assessments", icon: "BarChart3" },
      { name: "Students Promotion", path: "/headmaster/students-promotion", icon: "TrendingUp" },
      { name: "Reports", path: "/headmaster/reports", icon: "FileText" },
    ],
  },
  {
    name: "Finance",
    path: "/headmaster/finance",
    icon: "DollarSign",
    subItems: [
      { name: "Fees Management", path: "/headmaster/fees", icon: "CreditCard" },
      { name: "Arrears List", path: "/headmaster/arrears", icon: "AlertTriangle" },
      { name: "Financial Records", path: "/headmaster/financial-records", icon: "BarChart3" },
    ],
  },
  {
    name: "Administration",
    path: "/headmaster/administration",
    icon: "Settings",
    subItems: [
      { name: "Account", path: "/headmaster/account", icon: "School" },
      { name: "Account Users", path: "/headmaster/account-users", icon: "Users" },
      { name: "Terms & Academic Year", path: "/headmaster/terms-academic-year", icon: "Calendar" },
      { name: "Grading System", path: "/headmaster/grading-system", icon: "Award" },
    ],
  },
  {
    name: "Announcements",
    icon: "BellRing",
    path: "/headmaster/announcements",
  },
  {
    name: "Calendar",
    icon: "Calendar",
    path: "/headmaster/calendar",
  },
  {
    name: "Settings",
    icon: "Settings",
    path: "/headmaster/settings",
  },
];

// Mock data for development
export const MOCK_SCHOOLS = [
  {
    id: "1",
    name: "Eastwood High School",
    address: "123 Education Lane, Springfield",
    contactInfo: "+****************",
    headmasterName: "Dr. Michael Johnson",
    email: "<EMAIL>",
    status: "active",
    students: 1250,
    teachers: 85,
    createdAt: "2023-01-15T08:00:00.000Z",
  },
  {
    id: "2",
    name: "Westfield Academy",
    address: "456 Learning Street, Riverdale",
    contactInfo: "+1 (555) 987-6543",
    headmasterName: "Dr. Sarah Williams",
    email: "<EMAIL>",
    status: "active",
    students: 980,
    teachers: 67,
    createdAt: "2023-03-22T10:30:00.000Z",
  },
  {
    id: "3",
    name: "Northside Preparatory School",
    address: "789 Knowledge Avenue, Lakeside",
    contactInfo: "+****************",
    headmasterName: "Prof. David Thompson",
    email: "<EMAIL>",
    status: "inactive",
    students: 750,
    teachers: 52,
    createdAt: "2023-06-05T14:15:00.000Z",
  },
  {
    id: "4",
    name: "Southern Heights College",
    address: "321 Wisdom Road, Hillcrest",
    contactInfo: "+1 (555) 789-0123",
    headmasterName: "Dr. Emily Clark",
    email: "<EMAIL>",
    status: "active",
    students: 1430,
    teachers: 96,
    createdAt: "2022-11-30T09:45:00.000Z",
  },
  {
    id: "5",
    name: "Central Valley Institute",
    address: "654 Scholars Drive, Meadowbrook",
    contactInfo: "+****************",
    headmasterName: "Prof. Robert Martinez",
    email: "<EMAIL>",
    status: "active",
    students: 825,
    teachers: 60,
    createdAt: "2023-08-12T11:20:00.000Z",
  },
];

export const MOCK_ACTIVITIES = [
  {
    id: "1",
    type: "school_added",
    name: "Eastwood High School",
    date: "2023-01-15T08:00:00.000Z",
    by: "Admin User",
  },
  {
    id: "2",
    type: "headmaster_added",
    name: "Dr. Sarah Williams",
    date: "2023-03-22T10:30:00.000Z",
    by: "Admin User",
  },
  {
    id: "3",
    type: "school_added",
    name: "Northside Preparatory School",
    date: "2023-06-05T14:15:00.000Z",
    by: "Admin User",
  },
  {
    id: "4",
    type: "headmaster_added",
    name: "Dr. Emily Clark",
    date: "2022-11-30T09:45:00.000Z",
    by: "Admin User",
  },
  {
    id: "5",
    type: "school_updated",
    name: "Central Valley Institute",
    date: "2023-08-12T11:20:00.000Z",
    by: "Admin User",
  },
];

export const MOCK_TEACHER_DATA = [
  {
    id: "1",
    name: "John Smith",
    subject: "Mathematics",
    email: "<EMAIL>",
    phone: "+****************",
    joinDate: "2022-08-15",
    status: "active",
  },
  {
    id: "2",
    name: "Sarah Johnson",
    subject: "English",
    email: "<EMAIL>",
    phone: "+****************",
    joinDate: "2021-09-10",
    status: "active",
  },
  {
    id: "3",
    name: "Michael Brown",
    subject: "Science",
    email: "<EMAIL>",
    phone: "+****************",
    joinDate: "2023-01-05",
    status: "active",
  },
  {
    id: "4",
    name: "Emma Davis",
    subject: "History",
    email: "<EMAIL>",
    phone: "+****************",
    joinDate: "2022-03-20",
    status: "active",
  },
  {
    id: "5",
    name: "Robert Wilson",
    subject: "Physical Education",
    email: "<EMAIL>",
    phone: "+****************",
    joinDate: "2021-08-30",
    status: "inactive",
  },
];

export const MOCK_STUDENT_DATA = [
  {
    id: "1",
    name: "Alex Turner",
    class: "10A",
    email: "<EMAIL>",
    parent: "James Turner",
    joinDate: "2022-09-01",
    status: "active",
  },
  {
    id: "2",
    name: "Sophia Martinez",
    class: "9B",
    email: "<EMAIL>",
    parent: "Maria Martinez",
    joinDate: "2023-09-01",
    status: "active",
  },
  {
    id: "3",
    name: "Ethan Williams",
    class: "11C",
    email: "<EMAIL>",
    parent: "Thomas Williams",
    joinDate: "2021-09-01",
    status: "active",
  },
  {
    id: "4",
    name: "Olivia Johnson",
    class: "12A",
    email: "<EMAIL>",
    parent: "Patricia Johnson",
    joinDate: "2020-09-01",
    status: "inactive",
  },
  {
    id: "5",
    name: "Noah Brown",
    class: "10B",
    email: "<EMAIL>",
    parent: "Robert Brown",
    joinDate: "2022-09-01",
    status: "active",
  },
];

export const MOCK_CHART_DATA = {
  studentsPerSchool: [
    { name: "Eastwood High", students: 1250 },
    { name: "Westfield Academy", students: 980 },
    { name: "Northside Prep", students: 750 },
    { name: "Southern Heights", students: 1430 },
    { name: "Central Valley", students: 825 },
  ],
  monthlyRegistrations: [
    { name: "Jan", students: 45 },
    { name: "Feb", students: 32 },
    { name: "Mar", students: 28 },
    { name: "Apr", students: 39 },
    { name: "May", students: 55 },
    { name: "Jun", students: 22 },
    { name: "Jul", students: 18 },
    { name: "Aug", students: 102 },
    { name: "Sep", students: 120 },
    { name: "Oct", students: 35 },
    { name: "Nov", students: 28 },
    { name: "Dec", students: 15 },
  ],
  attendanceSummary: [
    { name: "Present", value: 82 },
    { name: "Absent", value: 8 },
    { name: "Late", value: 10 },
  ],
};
