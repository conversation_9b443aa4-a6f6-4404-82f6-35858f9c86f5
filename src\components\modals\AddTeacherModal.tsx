import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { X, Loader2 } from "lucide-react";
import { PHONE_PATTERN, SubjectOption, ClassOption, TeacherFormData } from "@/types";
import { headmaster<PERSON><PERSON> } from "@/lib/api";

const teacherSchema = z.object({
  firstName: z.string().min(2, { message: "First name must be at least 2 characters" }),
  lastName: z.string().min(2, { message: "Last name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  phone: z.string().regex(PHONE_PATTERN, { message: "Please enter a valid phone number. Example: +1234567890" }),
  subjectIds: z.array(z.string()).min(1, { message: "Please select at least one subject" }),
  classId: z.string().optional(),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

type AddTeacherFormData = z.infer<typeof teacherSchema>;

interface AddTeacherModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: TeacherFormData) => Promise<void>;
}

export function AddTeacherModal({ open, onClose, onSubmit }: AddTeacherModalProps) {
  const [subjects, setSubjects] = useState<SubjectOption[]>([]);
  const [classes, setClasses] = useState<ClassOption[]>([]);
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(false);

  const form = useForm<AddTeacherFormData>({
    resolver: zodResolver(teacherSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      subjectIds: [],
      password: "",
      classId: "",
    },
  });

  // Fetch dropdown data when modal opens
  useEffect(() => {
    if (open) {
      fetchDropdownData();
    }
  }, [open]);

  const fetchDropdownData = async () => {
    setLoadingData(true);
    try {
      const [subjectsResponse, classesResponse] = await Promise.all([
        headmasterAPI.getSubjectsForDropdown(),
        headmasterAPI.getClassesForDropdown(),
      ]);

      if (subjectsResponse.success) {
        setSubjects(subjectsResponse.data);
      }
      if (classesResponse.success) {
        setClasses(classesResponse.data);
      }
    } catch (error) {
      console.error("Failed to fetch dropdown data:", error);
    } finally {
      setLoadingData(false);
    }
  };

  const handleSubjectToggle = (subjectId: string) => {
    const currentSubjects = form.getValues("subjectIds");
    if (selectedSubjects.includes(subjectId)) {
      const newSubjects = currentSubjects.filter((s) => s !== subjectId);
      form.setValue("subjectIds", newSubjects);
      setSelectedSubjects((prev) => prev.filter((s) => s !== subjectId));
    } else {
      const newSubjects = [...currentSubjects, subjectId];
      form.setValue("subjectIds", newSubjects);
      setSelectedSubjects((prev) => [...prev, subjectId]);
    }
  };

  const removeSelectedSubject = (subjectId: string) => {
    const currentSubjects = form.getValues("subjectIds");
    const newSubjects = currentSubjects.filter((s) => s !== subjectId);
    form.setValue("subjectIds", newSubjects);
    setSelectedSubjects((prev) => prev.filter((s) => s !== subjectId));
  };

  const handleSubmit = async (data: AddTeacherFormData) => {
    setLoading(true);
    try {
      const teacherData: TeacherFormData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        subjectIds: data.subjectIds,
        classId: data.classId,
        password: data.password,
      };

      await onSubmit(teacherData);
      form.reset();
      setSelectedSubjects([]);
      onClose();
    } catch (error) {
      console.error("Failed to create teacher:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    form.reset();
    setSelectedSubjects([]);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add New Teacher</DialogTitle>
          <DialogDescription>Enter the details of the new teacher to add them to the system.</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Smith" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input placeholder="+1234567890" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-3">
              <FormLabel>Subjects to Teach *</FormLabel>

              {/* Selected Subjects Display */}
              {selectedSubjects.length > 0 && (
                <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-muted/20">
                  {selectedSubjects.map((subjectId) => {
                    const subject = subjects.find((s) => s.id === subjectId);
                    return (
                      <Badge key={subjectId} variant="secondary" className="flex items-center gap-1">
                        {subject ? `${subject.name} (${subject.code})` : subjectId}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-destructive"
                          onClick={() => removeSelectedSubject(subjectId)}
                        />
                      </Badge>
                    );
                  })}
                </div>
              )}

              {/* Available Subjects */}
              <div className="max-h-32 overflow-y-auto border rounded-md p-2 space-y-2">
                {loadingData ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="ml-2 text-sm text-gray-500">Loading subjects...</span>
                  </div>
                ) : subjects.length === 0 ? (
                  <div className="text-sm text-gray-500 text-center py-4">No subjects available</div>
                ) : (
                  subjects.map((subject) => (
                    <div key={subject.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`subject-${subject.id}`}
                        checked={selectedSubjects.includes(subject.id)}
                        onCheckedChange={() => handleSubjectToggle(subject.id)}
                      />
                      <label htmlFor={`subject-${subject.id}`} className="text-sm cursor-pointer flex-1">
                        {subject.name} ({subject.code})
                      </label>
                    </div>
                  ))
                )}
              </div>
              {selectedSubjects.length === 0 && (
                <p className="text-sm text-destructive">Please select at least one subject</p>
              )}
            </div>

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="Enter password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="classId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Primary Class (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a class" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {loadingData ? (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="ml-2 text-sm text-gray-500">Loading classes...</span>
                        </div>
                      ) : classes.length === 0 ? (
                        <div className="text-sm text-gray-500 text-center py-4">No classes available</div>
                      ) : (
                        classes.map((classItem) => (
                          <SelectItem key={classItem.id} value={classItem.id}>
                            {classItem.displayName || classItem.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" className="gradient-bg text-white hover:opacity-90" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Add Teacher"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
