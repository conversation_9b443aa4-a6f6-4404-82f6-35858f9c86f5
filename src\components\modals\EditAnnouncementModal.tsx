import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Calendar, Megaphone, Users } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

const announcementSchema = z.object({
  title: z.string().min(2, { message: 'Title must be at least 2 characters' }),
  content: z.string().min(10, { message: 'Content must be at least 10 characters' }),
  type: z.string().min(1, { message: 'Type is required' }),
  audience: z.array(z.string()).min(1, { message: 'At least one audience must be selected' }),
  isPinned: z.boolean().default(false),
});

export type EditAnnouncementFormData = z.infer<typeof announcementSchema>;

interface Announcement {
  id: string;
  title: string;
  content: string;
  author: string;
  date: string;
  type: string;
  audience: string[];
  isPinned: boolean;
  comments: number;
}

interface EditAnnouncementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: EditAnnouncementFormData) => void;
  announcement: Announcement | null;
}

export function EditAnnouncementModal({ isOpen, onClose, onSubmit, announcement }: EditAnnouncementModalProps) {
  const form = useForm<EditAnnouncementFormData>({
    resolver: zodResolver(announcementSchema),
    defaultValues: {
      title: '',
      content: '',
      type: 'notice',
      audience: ['all'],
      isPinned: false,
    },
  });
  
  const [selectedAudience, setSelectedAudience] = useState<string[]>(['all']);
  
  // Update form when announcement changes
  useEffect(() => {
    if (announcement) {
      form.reset({
        title: announcement.title,
        content: announcement.content,
        type: announcement.type,
        audience: announcement.audience,
        isPinned: announcement.isPinned
      });
      setSelectedAudience(announcement.audience);
    }
  }, [announcement, form]);
  
  const handleAudienceChange = (audience: string, checked: boolean) => {
    if (audience === 'all' && checked) {
      setSelectedAudience(['all']);
      form.setValue('audience', ['all']);
    } else {
      let newAudience = [...selectedAudience];
      
      if (checked) {
        // Remove 'all' if it exists
        newAudience = newAudience.filter(a => a !== 'all');
        // Add the new audience
        newAudience.push(audience);
      } else {
        // Remove the audience
        newAudience = newAudience.filter(a => a !== audience);
        // If empty, add 'all'
        if (newAudience.length === 0) {
          newAudience = ['all'];
        }
      }
      
      setSelectedAudience(newAudience);
      form.setValue('audience', newAudience);
    }
  };

  const handleSubmit = (data: EditAnnouncementFormData) => {
    onSubmit(data);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Announcement</DialogTitle>
          <DialogDescription>
            Update the announcement details.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Announcement title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Enter announcement details here..." 
                      className="min-h-[120px]" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Announcement Type</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="event" className="flex items-center">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-primary" />
                            <span>Event</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="notice">
                          <div className="flex items-center">
                            <Megaphone className="h-4 w-4 mr-2 text-warning" />
                            <span>Notice</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="academic">
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-2 text-success" />
                            <span>Academic</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="isPinned"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Pin Announcement</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Pinned announcements appear at the top
                      </p>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="audience"
              render={() => (
                <FormItem>
                  <FormLabel>Target Audience</FormLabel>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="audience-all" 
                        checked={selectedAudience.includes('all')}
                        onCheckedChange={(checked) => handleAudienceChange('all', !!checked)}
                      />
                      <Label htmlFor="audience-all">Everyone</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="audience-students" 
                        checked={selectedAudience.includes('students')}
                        onCheckedChange={(checked) => handleAudienceChange('students', !!checked)}
                        disabled={selectedAudience.includes('all')}
                      />
                      <Label htmlFor="audience-students">Students</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="audience-teachers" 
                        checked={selectedAudience.includes('teachers')}
                        onCheckedChange={(checked) => handleAudienceChange('teachers', !!checked)}
                        disabled={selectedAudience.includes('all')}
                      />
                      <Label htmlFor="audience-teachers">Teachers</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="audience-parents" 
                        checked={selectedAudience.includes('parents')}
                        onCheckedChange={(checked) => handleAudienceChange('parents', !!checked)}
                        disabled={selectedAudience.includes('all')}
                      />
                      <Label htmlFor="audience-parents">Parents</Label>
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" className="gradient-bg text-white hover:opacity-90">
                Update Announcement
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}