import { useState, useEffect, useCallback } from 'react';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import type { 
  Fee, 
  Payment, 
  ArrearsRecord, 
  FeeSummary, 
  ArrearsSummary,
  FeeFormData,
  PaymentFormData,
  RequestParams
} from '@/types';

interface UseFeeManagementReturn {
  // Fee State
  fees: Fee[];
  feeLoading: boolean;
  feeError: string | null;
  feeTotal: number;
  feePage: number;
  feeLimit: number;

  // Payment State
  payments: Payment[];
  paymentLoading: boolean;
  paymentError: string | null;
  paymentTotal: number;
  paymentPage: number;
  paymentLimit: number;

  // Arrears State
  arrears: ArrearsRecord[];
  arrearsLoading: boolean;
  arrearsError: string | null;
  arrearsTotal: number;
  arrearsPage: number;
  arrearsLimit: number;

  // Summary State
  feeSummary: FeeSummary | null;
  arrearsSummary: ArrearsSummary | null;
  summaryLoading: boolean;

  // Actions
  fetchFees: (params?: RequestParams) => Promise<void>;
  fetchPayments: (params?: RequestParams) => Promise<void>;
  fetchArrears: (params?: RequestParams) => Promise<void>;
  fetchSummaries: () => Promise<void>;
  createFee: (data: FeeFormData) => Promise<void>;
  updateFee: (id: string, data: Partial<FeeFormData>) => Promise<void>;
  deleteFee: (id: string) => Promise<void>;
  createPayment: (data: PaymentFormData) => Promise<void>;
  updatePayment: (id: string, data: Partial<PaymentFormData>) => Promise<void>;
  deletePayment: (id: string) => Promise<void>;
  exportFeeRecords: (params?: any) => Promise<void>;
  getStudentFees: (studentId: string) => Promise<Fee[]>;
}

export const useFeeManagement = (): UseFeeManagementReturn => {
  // Fee State
  const [fees, setFees] = useState<Fee[]>([]);
  const [feeLoading, setFeeLoading] = useState(false);
  const [feeError, setFeeError] = useState<string | null>(null);
  const [feeTotal, setFeeTotal] = useState(0);
  const [feePage, setFeePage] = useState(1);
  const [feeLimit] = useState(10);

  // Payment State
  const [payments, setPayments] = useState<Payment[]>([]);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [paymentTotal, setPaymentTotal] = useState(0);
  const [paymentPage, setPaymentPage] = useState(1);
  const [paymentLimit] = useState(10);

  // Arrears State
  const [arrears, setArrears] = useState<ArrearsRecord[]>([]);
  const [arrearsLoading, setArrearsLoading] = useState(false);
  const [arrearsError, setArrearsError] = useState<string | null>(null);
  const [arrearsTotal, setArrearsTotal] = useState(0);
  const [arrearsPage, setArrearsPage] = useState(1);
  const [arrearsLimit] = useState(10);

  // Summary State
  const [feeSummary, setFeeSummary] = useState<FeeSummary | null>(null);
  const [arrearsSummary, setArrearsSummary] = useState<ArrearsSummary | null>(null);
  const [summaryLoading, setSummaryLoading] = useState(false);

  const fetchFees = useCallback(async (params: RequestParams = {}) => {
    try {
      setFeeLoading(true);
      setFeeError(null);

      const response = await headmasterAPI.getFeeRecords({
        page: feePage,
        limit: feeLimit,
        ...params
      });

      if (response.success) {
        setFees(response.data.data || []);
        setFeeTotal(response.data.total || 0);
      }
    } catch (err) {
      console.error('Failed to fetch fees:', err);
      setFeeError('Failed to load fee records');
      showToast('Failed to load fee records', 'error');
    } finally {
      setFeeLoading(false);
    }
  }, [feePage, feeLimit]);

  const fetchPayments = useCallback(async (params: RequestParams = {}) => {
    try {
      setPaymentLoading(true);
      setPaymentError(null);

      const response = await headmasterAPI.getPaymentRecords({
        page: paymentPage,
        limit: paymentLimit,
        ...params
      });

      if (response.success) {
        setPayments(response.data.data || []);
        setPaymentTotal(response.data.total || 0);
      }
    } catch (err) {
      console.error('Failed to fetch payments:', err);
      setPaymentError('Failed to load payment records');
      showToast('Failed to load payment records', 'error');
    } finally {
      setPaymentLoading(false);
    }
  }, [paymentPage, paymentLimit]);

  const fetchArrears = useCallback(async (params: RequestParams = {}) => {
    try {
      setArrearsLoading(true);
      setArrearsError(null);

      const response = await headmasterAPI.getArrears({
        page: arrearsPage,
        limit: arrearsLimit,
        ...params
      });

      if (response.success) {
        setArrears(response.data.data || []);
        setArrearsTotal(response.data.total || 0);
      }
    } catch (err) {
      console.error('Failed to fetch arrears:', err);
      setArrearsError('Failed to load arrears records');
      showToast('Failed to load arrears records', 'error');
    } finally {
      setArrearsLoading(false);
    }
  }, [arrearsPage, arrearsLimit]);

  const fetchSummaries = useCallback(async () => {
    try {
      setSummaryLoading(true);

      const [feeSummaryResponse, arrearsSummaryResponse] = await Promise.all([
        headmasterAPI.getFeeSummary(),
        headmasterAPI.getArrearsSummary()
      ]);

      if (feeSummaryResponse.success) {
        setFeeSummary(feeSummaryResponse.data);
      }

      if (arrearsSummaryResponse.success) {
        setArrearsSummary(arrearsSummaryResponse.data);
      }
    } catch (err) {
      console.error('Failed to fetch summaries:', err);
      showToast('Failed to load summary data', 'error');
    } finally {
      setSummaryLoading(false);
    }
  }, []);

  const createFee = useCallback(async (data: FeeFormData) => {
    try {
      const response = await headmasterAPI.createFeeRecord(data);
      if (response.success) {
        showToast('Fee record created successfully', 'success');
        fetchFees();
        fetchSummaries();
      }
    } catch (err) {
      console.error('Failed to create fee:', err);
      showToast('Failed to create fee record', 'error');
      throw err;
    }
  }, [fetchFees, fetchSummaries]);

  const updateFee = useCallback(async (id: string, data: Partial<FeeFormData>) => {
    try {
      const response = await headmasterAPI.updateFeeRecord(id, data);
      if (response.success) {
        showToast('Fee record updated successfully', 'success');
        fetchFees();
        fetchSummaries();
      }
    } catch (err) {
      console.error('Failed to update fee:', err);
      showToast('Failed to update fee record', 'error');
      throw err;
    }
  }, [fetchFees, fetchSummaries]);

  const deleteFee = useCallback(async (id: string) => {
    try {
      const response = await headmasterAPI.deleteFeeRecord(id);
      if (response.success) {
        showToast('Fee record deleted successfully', 'success');
        fetchFees();
        fetchSummaries();
      }
    } catch (err) {
      console.error('Failed to delete fee:', err);
      showToast('Failed to delete fee record', 'error');
      throw err;
    }
  }, [fetchFees, fetchSummaries]);

  const createPayment = useCallback(async (data: PaymentFormData) => {
    try {
      const response = await headmasterAPI.createPayment(data);
      if (response.success) {
        showToast('Payment recorded successfully', 'success');
        fetchPayments();
        fetchFees();
        fetchArrears();
        fetchSummaries();
      }
    } catch (err) {
      console.error('Failed to create payment:', err);
      showToast('Failed to record payment', 'error');
      throw err;
    }
  }, [fetchPayments, fetchFees, fetchArrears, fetchSummaries]);

  const updatePayment = useCallback(async (id: string, data: Partial<PaymentFormData>) => {
    try {
      const response = await headmasterAPI.updatePayment(id, data);
      if (response.success) {
        showToast('Payment updated successfully', 'success');
        fetchPayments();
        fetchFees();
        fetchArrears();
        fetchSummaries();
      }
    } catch (err) {
      console.error('Failed to update payment:', err);
      showToast('Failed to update payment', 'error');
      throw err;
    }
  }, [fetchPayments, fetchFees, fetchArrears, fetchSummaries]);

  const deletePayment = useCallback(async (id: string) => {
    try {
      const response = await headmasterAPI.deletePayment(id);
      if (response.success) {
        showToast('Payment deleted successfully', 'success');
        fetchPayments();
        fetchFees();
        fetchArrears();
        fetchSummaries();
      }
    } catch (err) {
      console.error('Failed to delete payment:', err);
      showToast('Failed to delete payment', 'error');
      throw err;
    }
  }, [fetchPayments, fetchFees, fetchArrears, fetchSummaries]);

  const exportFeeRecords = useCallback(async (params: any = {}) => {
    try {
      const response = await headmasterAPI.exportFeeRecords(params);
      // Handle CSV download
      const blob = new Blob([response], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `fee-records-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      showToast('Fee records exported successfully', 'success');
    } catch (err) {
      console.error('Failed to export fee records:', err);
      showToast('Failed to export fee records', 'error');
    }
  }, []);

  const getStudentFees = useCallback(async (studentId: string): Promise<Fee[]> => {
    try {
      const response = await headmasterAPI.getStudentFees(studentId);
      if (response.success) {
        return response.data || [];
      }
      return [];
    } catch (err) {
      console.error('Failed to fetch student fees:', err);
      showToast('Failed to load student fees', 'error');
      return [];
    }
  }, []);

  return {
    // Fee State
    fees,
    feeLoading,
    feeError,
    feeTotal,
    feePage,
    feeLimit,

    // Payment State
    payments,
    paymentLoading,
    paymentError,
    paymentTotal,
    paymentPage,
    paymentLimit,

    // Arrears State
    arrears,
    arrearsLoading,
    arrearsError,
    arrearsTotal,
    arrearsPage,
    arrearsLimit,

    // Summary State
    feeSummary,
    arrearsSummary,
    summaryLoading,

    // Actions
    fetchFees,
    fetchPayments,
    fetchArrears,
    fetchSummaries,
    createFee,
    updateFee,
    deleteFee,
    createPayment,
    updatePayment,
    deletePayment,
    exportFeeRecords,
    getStudentFees,
  };
};
