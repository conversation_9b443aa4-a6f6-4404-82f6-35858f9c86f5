import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Building2,
  GraduationCap,
  Users,
  School as SchoolIcon,
  Plus,
  TrendingUp,
  ArrowUpRight,
  Calendar,
  Bell,
  BookOpen,
  CheckCircle,
  Loader2,
  Sparkles,
  Target,
  BarChart3,
  Activity,
  Award,
} from "lucide-react";
import { Layout } from "@/components/common/Layout";
import { DashboardCard } from "@/components/dashboard/DashboardCard";
import { ChartCard } from "@/components/dashboard/ChartCard";
import { AnnouncementModal } from "@/components/modals/AnnouncementModal";
import { AddTeacherModal } from "@/components/modals/AddTeacherModal";
import { AddStudentModal } from "@/components/modals/AddStudentModal";
import { AddParentModal, AddParentFormData } from "@/components/modals/AddParentModal";
import { AddClassModal } from "@/components/modals/AddClassModal";
import { AddCourseModal } from "@/components/modals/AddCourseModal";
import { useAuth } from "@/contexts/AuthContext";
import { useHeadmasterDashboard } from "@/hooks/useHeadmasterDashboard";
import { Button } from "@/components/ui/button";
import { TeacherFormData, StudentFormData, ParentFormData } from "@/types";

interface ClassFormData {
  name: string;
  classTeacher: string;
  grade: string;
  section: string;
  capacity: string;
}

interface CourseFormData {
  name: string;
  code: string;
  teacher: string;
  department: string;
  credits: string;
  schedule: string;
}

export function HeadmasterDashboard() {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Use the custom hook for dashboard state management
  const {
    stats,
    announcements,
    loading,
    error,
    createAnnouncement,
    createTeacher,
    createStudent,
    createParent,
    createClass,
    createCourse,
  } = useHeadmasterDashboard();

  // Modal states
  const [isAnnouncementModalOpen, setIsAnnouncementModalOpen] = useState(false);
  const [isTeacherModalOpen, setIsTeacherModalOpen] = useState(false);
  const [isStudentModalOpen, setIsStudentModalOpen] = useState(false);
  const [isParentModalOpen, setIsParentModalOpen] = useState(false);
  const [isClassModalOpen, setIsClassModalOpen] = useState(false);
  const [isCourseModalOpen, setIsCourseModalOpen] = useState(false);

  // Create handlers using the hook functions
  const handleCreateTeacher = async (data: TeacherFormData) => {
    try {
      await createTeacher(data);
      setIsTeacherModalOpen(false);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleCreateStudent = async (data: StudentFormData) => {
    try {
      await createStudent(data);
      setIsStudentModalOpen(false);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleCreateParent = async (data: AddParentFormData) => {
    try {
      // Transform AddParentFormData to ParentFormData
      const parentData: ParentFormData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        children: "", // Initialize as empty string, can be updated later
        occupation: data.occupation,
      };
      await createParent(parentData);
      setIsParentModalOpen(false);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleCreateClass = async (data: ClassFormData) => {
    try {
      await createClass({
        name: data.name,
        classTeacher: data.classTeacher,
        grade: data.grade,
        section: data.section,
        capacity: data.capacity,
      });
      setIsClassModalOpen(false);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleCreateCourse = async (data: CourseFormData) => {
    try {
      await createCourse({
        name: data.name,
        code: data.code,
        teacher: data.teacher,
        department: data.department,
        credits: data.credits,
        schedule: data.schedule,
      });
      setIsCourseModalOpen(false);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleCreateAnnouncement = async (data: any) => {
    try {
      await createAnnouncement(data);
      setIsAnnouncementModalOpen(false);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  // Navigation handlers
  const navigateTo = {
    toTeachers: () => navigate("/headmaster/teachers"),
    toStudents: () => navigate("/headmaster/students"),
    toClasses: () => navigate("/headmaster/classes"),
    toCourses: () => navigate("/headmaster/courses"),
    toAnnouncements: () => navigate("/headmaster/announcements"),
    toAttendance: () => navigate("/headmaster/attendance"),
  };

  // Format current date
  const currentDate = new Date().toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Prepare attendance data for charts
  const attendanceData = stats?.attendanceStats
    ? [
        { name: "Present", value: stats.attendanceStats.present },
        { name: "Absent", value: stats.attendanceStats.absent },
        { name: "Late", value: stats.attendanceStats.late },
        { name: "Excused", value: stats.attendanceStats.excused },
      ].filter((item) => item.value > 0)
    : [];

  // If no attendance data, add placeholder
  if (attendanceData.length === 0) {
    attendanceData.push({ name: "No Data", value: 1 });
  }

  // Only show error in banner if it exists
  const renderError = error && <div className="bg-destructive/10 text-destructive rounded-md p-3 mb-6">{error}</div>;

  return (
    <Layout>
      <div className="p-4 md:p-6">
        {renderError}

        {/* Enhanced Welcome Header */}
        <div className="relative overflow-hidden bg-gradient-to-br from-emerald-900 via-teal-800 to-green-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
            <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
            <div className="absolute top-1/2 left-1/2 w-40 h-40 border border-white/10 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
          </div>

          <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                  <Sparkles className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold tracking-tight">
                    Welcome back, {user?.name?.split(" ")[0] || "Headmaster"}
                  </h1>
                  <p className="text-emerald-200 flex items-center mt-1">
                    <Calendar className="h-4 w-4 mr-2" />
                    {currentDate}
                  </p>
                  {stats?.schoolName && <p className="text-emerald-100 font-medium mt-1">{stats.schoolName}</p>}
                </div>
              </div>
              <p className="text-emerald-100 text-lg max-w-2xl">
                Manage your school with comprehensive tools and real-time insights for educational excellence.
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
              >
                <Bell className="h-4 w-4 mr-2" />
                <span>Notifications</span>
              </Button>
              <Button
                onClick={() => setIsAnnouncementModalOpen(true)}
                className="bg-white text-emerald-900 hover:bg-emerald-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Plus className="h-4 w-4 mr-2" />
                <span>New Announcement</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          <div
            className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group cursor-pointer rounded-xl"
            onClick={navigateTo.toStudents}
          >
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-700">Total Students</p>
                  <p className="text-3xl font-bold text-blue-900">
                    {loading ? "..." : (stats?.totalStudents || 0).toLocaleString()}
                  </p>
                  <p className="text-sm text-blue-600 mt-1">Enrolled students</p>
                </div>
                <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          </div>

          <div
            className="relative overflow-hidden bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group cursor-pointer rounded-xl"
            onClick={navigateTo.toTeachers}
          >
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-emerald-700">Total Teachers</p>
                  <p className="text-3xl font-bold text-emerald-900">
                    {loading ? "..." : (stats?.totalTeachers || 0).toLocaleString()}
                  </p>
                  <p className="text-sm text-emerald-600 mt-1">Faculty members</p>
                </div>
                <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                  <GraduationCap className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          </div>

          <div
            className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group cursor-pointer rounded-xl"
            onClick={navigateTo.toClasses}
          >
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-700">Total Classes</p>
                  <p className="text-3xl font-bold text-purple-900">
                    {loading ? "..." : (stats?.totalClasses || 0).toLocaleString()}
                  </p>
                  <p className="text-sm text-purple-600 mt-1">Active classes</p>
                </div>
                <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                  <SchoolIcon className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          </div>

          <div
            className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group cursor-pointer rounded-xl"
            onClick={navigateTo.toCourses}
          >
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-700">Total Subjects</p>
                  <p className="text-3xl font-bold text-orange-900">
                    {loading ? "..." : (stats?.totalSubjects || 0).toLocaleString()}
                  </p>
                  <p className="text-sm text-orange-600 mt-1">Curriculum subjects</p>
                </div>
                <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                  <BookOpen className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <div className="bg-gradient-to-br from-white to-blue-50/30 border-blue-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl">
              <div className="p-6 border-b border-blue-100/50 bg-gradient-to-r from-blue-50 to-transparent">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-blue-600 p-2 rounded-lg">
                      <BarChart3 className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-blue-900">Today's Attendance</h3>
                      <p className="text-sm text-blue-600">Real-time attendance tracking</p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-200 text-blue-700 hover:bg-blue-50"
                    onClick={navigateTo.toAttendance}
                  >
                    View Details
                  </Button>
                </div>
              </div>
              <div className="p-6">
                <div
                  style={{ width: "100%", height: 300 }}
                  className="flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100/30 rounded-lg"
                >
                  <div className="text-center">
                    <Activity className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                    <p className="text-blue-600 font-medium">Attendance Chart</p>
                    <p className="text-sm text-blue-500">Real-time data visualization</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <div className="bg-gradient-to-br from-white to-emerald-50/30 border-emerald-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl">
              <div className="p-6 border-b border-emerald-100/50 bg-gradient-to-r from-emerald-50 to-transparent">
                <div className="flex items-center gap-3">
                  <div className="bg-emerald-600 p-2 rounded-lg">
                    <Target className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-emerald-900">Attendance Overview</h3>
                    <p className="text-sm text-emerald-600">Distribution summary</p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div
                  style={{ width: "100%", height: 300 }}
                  className="flex items-center justify-center bg-gradient-to-br from-emerald-50 to-emerald-100/30 rounded-lg"
                >
                  <div className="text-center">
                    <Award className="h-12 w-12 text-emerald-400 mx-auto mb-4" />
                    <p className="text-emerald-600 font-medium">Overview Chart</p>
                    <p className="text-sm text-emerald-500">Status distribution</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Announcements and Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          <div className="lg:col-span-2">
            <div className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl h-full">
              <div className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-slate-700 p-2 rounded-lg">
                      <Bell className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-900">Recent Announcements</h3>
                      <p className="text-sm text-slate-600">Latest school updates and notices</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-slate-200 text-slate-700 hover:bg-slate-50"
                      onClick={navigateTo.toAnnouncements}
                    >
                      View All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-emerald-200 text-emerald-700 hover:bg-emerald-50"
                      onClick={() => setIsAnnouncementModalOpen(true)}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      New
                    </Button>
                  </div>
                </div>
              </div>
              <div className="p-0">
                {loading ? (
                  <div className="flex items-center justify-center h-[300px]">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : announcements && announcements.length > 0 ? (
                  <div className="divide-y divide-border">
                    {announcements.map((announcement) => (
                      <div
                        key={announcement.id}
                        className="p-4 hover:bg-muted/20 cursor-pointer"
                        onClick={navigateTo.toAnnouncements}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-primary">{announcement.title}</h4>
                          <span
                            className={`text-xs px-2 py-1 rounded-full ${
                              announcement.priority === "high"
                                ? "bg-destructive/10 text-destructive"
                                : announcement.priority === "medium"
                                ? "bg-warning/10 text-warning"
                                : "bg-success/10 text-success"
                            }`}
                          >
                            {announcement.priority.charAt(0).toUpperCase() + announcement.priority.slice(1)}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{announcement.content}</p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>By {announcement.author}</span>
                          <span>{new Date(announcement.date).toLocaleDateString()}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-[200px] text-muted-foreground gap-4">
                    <p>No recent announcements</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs text-primary border-primary/20 hover:bg-primary/5"
                      onClick={() => setIsAnnouncementModalOpen(true)}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Create Announcement
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div>
            <div className="bg-gradient-to-br from-white to-indigo-50/30 border-indigo-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl h-full">
              <div className="p-6 border-b border-indigo-100/50 bg-gradient-to-r from-indigo-50 to-transparent">
                <div className="flex items-center gap-3">
                  <div className="bg-indigo-600 p-2 rounded-lg">
                    <TrendingUp className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-indigo-900">Quick Actions</h3>
                    <p className="text-sm text-indigo-600">Frequently used operations</p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-4 px-4 border-blue-200 hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
                      onClick={() => setIsStudentModalOpen(true)}
                    >
                      <div className="flex items-center w-full">
                        <div className="bg-blue-100 p-2.5 rounded-xl mr-4">
                          <Users className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-blue-900">Add Student</div>
                          <div className="text-sm text-blue-600">Create a new student record</div>
                        </div>
                        <ArrowUpRight className="h-4 w-4 text-blue-400" />
                      </div>
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-4 px-4 border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-200"
                      onClick={navigateTo.toStudents}
                    >
                      <div className="flex items-center w-full">
                        <div className="bg-emerald-100 p-2.5 rounded-xl mr-4">
                          <Users className="h-5 w-5 text-emerald-600" />
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-emerald-900">Manage Students</div>
                          <div className="text-sm text-emerald-600">View all students</div>
                        </div>
                        <ArrowUpRight className="h-4 w-4 text-emerald-400" />
                      </div>
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-4 px-4 border-purple-200 hover:bg-purple-50 hover:border-purple-300 transition-all duration-200"
                      onClick={() => setIsTeacherModalOpen(true)}
                    >
                      <div className="flex items-center w-full">
                        <div className="bg-purple-100 p-2.5 rounded-xl mr-4">
                          <GraduationCap className="h-5 w-5 text-purple-600" />
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-purple-900">Add Teacher</div>
                          <div className="text-sm text-purple-600">Create a new teacher record</div>
                        </div>
                        <ArrowUpRight className="h-4 w-4 text-purple-400" />
                      </div>
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-4 px-4 border-orange-200 hover:bg-orange-50 hover:border-orange-300 transition-all duration-200"
                      onClick={navigateTo.toTeachers}
                    >
                      <div className="flex items-center w-full">
                        <div className="bg-orange-100 p-2.5 rounded-xl mr-4">
                          <GraduationCap className="h-5 w-5 text-orange-600" />
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-orange-900">Manage Teachers</div>
                          <div className="text-sm text-orange-600">View all teachers</div>
                        </div>
                        <ArrowUpRight className="h-4 w-4 text-orange-400" />
                      </div>
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-3 px-4"
                      onClick={() => setIsClassModalOpen(true)}
                    >
                      <div className="flex items-center">
                        <div className="p-2 rounded-full bg-primary/10 mr-3">
                          <SchoolIcon className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <div className="font-medium">Add Class</div>
                          <div className="text-xs text-muted-foreground">Create a new class</div>
                        </div>
                      </div>
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-3 px-4"
                      onClick={navigateTo.toClasses}
                    >
                      <div className="flex items-center">
                        <div className="p-2 rounded-full bg-primary/10 mr-3">
                          <ArrowUpRight className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <div className="font-medium">Manage Classes</div>
                          <div className="text-xs text-muted-foreground">View all classes</div>
                        </div>
                      </div>
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-3 px-4"
                      onClick={() => setIsAnnouncementModalOpen(true)}
                    >
                      <div className="flex items-center">
                        <div className="p-2 rounded-full bg-warning/10 mr-3">
                          <Bell className="h-4 w-4 text-warning" />
                        </div>
                        <div>
                          <div className="font-medium">Create Announcement</div>
                          <div className="text-xs text-muted-foreground">Post a new announcement</div>
                        </div>
                      </div>
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-3 px-4"
                      onClick={navigateTo.toAttendance}
                    >
                      <div className="flex items-center">
                        <div className="p-2 rounded-full bg-warning/10 mr-3">
                          <CheckCircle className="h-4 w-4 text-warning" />
                        </div>
                        <div>
                          <div className="font-medium">Take Attendance</div>
                          <div className="text-xs text-muted-foreground">Record today's attendance</div>
                        </div>
                      </div>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <AnnouncementModal
        isOpen={isAnnouncementModalOpen}
        onClose={() => setIsAnnouncementModalOpen(false)}
        onSubmit={handleCreateAnnouncement}
      />

      <AddTeacherModal
        open={isTeacherModalOpen}
        onClose={() => setIsTeacherModalOpen(false)}
        onSubmit={handleCreateTeacher}
      />

      <AddStudentModal
        isOpen={isStudentModalOpen}
        onClose={() => setIsStudentModalOpen(false)}
        onSubmit={handleCreateStudent}
      />

      <AddParentModal
        open={isParentModalOpen}
        onClose={() => setIsParentModalOpen(false)}
        onSubmit={handleCreateParent}
      />

      <AddClassModal
        isOpen={isClassModalOpen}
        onClose={() => setIsClassModalOpen(false)}
        onSubmit={handleCreateClass}
      />

      <AddCourseModal
        isOpen={isCourseModalOpen}
        onClose={() => setIsCourseModalOpen(false)}
        onSubmit={handleCreateCourse}
      />
    </Layout>
  );
}
