import React, { useState, useEffect } from "react";
import { Building2, GraduationCap, Users, School, Plus, Calendar, Bell, TrendingUp, ArrowUpRight, BarChart3, <PERSON><PERSON>hart as PieChartIcon, Activity as ActivityIcon, Sparkles, Globe, Award } from "lucide-react";
import { Layout } from "@/components/common/Layout";
import { DashboardCard } from "@/components/dashboard/DashboardCard";
import { ActivityCard } from "@/components/dashboard/ActivityCard";
import { Button } from "@/components/ui/button";
import { AddSchoolModal } from "@/components/modals/AddSchoolModal";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts";
import { MOCK_CHART_DATA } from "@/lib/constants";
import { AddSchoolFormData, DashboardStats, Activity } from "@/types";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { superAdminAPI } from "@/lib/api";
import { showToast } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";

export function SuperAdminDashboard() {
  const { user } = useAuth();
  const [isAddSchoolModalOpen, setIsAddSchoolModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalSchools: 0,
    totalHeadmasters: 0,
    totalStudents: 0,
    totalTeachers: 0,
    activeSchools: 0,
    inactiveSchools: 0,
  });
  const [activities, setActivities] = useState<Activity[]>([]);

  // Use mock data
  const { studentsPerSchool, attendanceSummary } = MOCK_CHART_DATA;

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const statsResponse = await superAdminAPI.getDashboardStats();
      if (statsResponse.success) {
        setStats(statsResponse.data.stats);
        setActivities(statsResponse.data.activities);
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      showToast.error("Error", "Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  const handleAddSchool = async (data: AddSchoolFormData) => {
    try {
      const schoolData = {
        name: data.name,
        address: data.address,
        contactInfo: data.contactInfo,
        headmasterName: data.headmasterName,
        headmasterEmail: data.email,
        headmasterPassword: data.password,
        city: "City",
        state: "State",
        zipCode: "00000",
        phone: data.contactInfo,
        email: data.email,
        active: true,
      };

      const response = await superAdminAPI.createSchool(schoolData);

      if (response.success) {
        showToast.success("School Added", `${data.name} has been successfully added`);
        fetchDashboardData();
      }
    } catch (error) {
      console.error("Error adding school:", error);
      showToast.error("Error", "Failed to add school");
    }

    setIsAddSchoolModalOpen(false);
  };

  const currentDate = new Date().toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <Layout>
      {/* Enhanced Welcome Header */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-40 h-40 border border-white/10 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>
        
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">
                  Welcome back, {user?.name?.split(" ")[0] || "Admin"}
                </h1>
                <p className="text-blue-200 flex items-center mt-1">
                  <Calendar className="h-4 w-4 mr-2" />
                  {currentDate}
                </p>
              </div>
            </div>
            <p className="text-blue-100 text-lg max-w-2xl">
              Manage your educational network with comprehensive insights and powerful tools.
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Bell className="h-4 w-4 mr-2" />
              <span>Notifications</span>
            </Button>
            <Button
              onClick={() => setIsAddSchoolModalOpen(true)}
              className="bg-white text-blue-900 hover:bg-blue-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>Add School</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Schools</p>
                <p className="text-3xl font-bold text-blue-900">{loading ? "..." : stats.totalSchools.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-green-600 font-medium">+5% from last month</span>
                </div>
              </div>
              <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Building2 className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="relative overflow-hidden bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-700">Total Headmasters</p>
                <p className="text-3xl font-bold text-emerald-900">{loading ? "..." : stats.totalHeadmasters.toLocaleString()}</p>
                <p className="text-sm text-emerald-600 mt-2">Managing schools</p>
              </div>
              <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Total Students</p>
                <p className="text-3xl font-bold text-purple-900">{loading ? "..." : stats.totalStudents.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-green-600 font-medium">+12% from last month</span>
                </div>
              </div>
              <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700">Total Teachers</p>
                <p className="text-3xl font-bold text-orange-900">{loading ? "..." : stats.totalTeachers.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-green-600 font-medium">+5% from last month</span>
                </div>
              </div>
              <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <School className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <Card className="bg-gradient-to-br from-white to-blue-50/30 border-blue-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="border-b border-blue-100/50 bg-gradient-to-r from-blue-50 to-transparent">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-blue-600 p-2 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-blue-900">School Performance Analytics</CardTitle>
                    <p className="text-sm text-blue-600">Student enrollment across institutions</p>
                  </div>
                </div>
                <Button variant="outline" size="sm" className="border-blue-200 text-blue-700 hover:bg-blue-50">
                  View Details
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div style={{ width: "100%", height: 350 }}>
                <ResponsiveContainer>
                  <BarChart data={studentsPerSchool} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <defs>
                      <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#3B82F6" stopOpacity={0.8}/>
                        <stop offset="100%" stopColor="#1E40AF" stopOpacity={0.6}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                    <XAxis dataKey="name" tick={{ fill: '#6B7280' }} />
                    <YAxis tick={{ fill: '#6B7280' }} />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'white', 
                        border: '1px solid #E5E7EB', 
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }} 
                    />
                    <Bar dataKey="students" fill="url(#barGradient)" radius={[6, 6, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="bg-gradient-to-br from-white to-emerald-50/30 border-emerald-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="border-b border-emerald-100/50 bg-gradient-to-r from-emerald-50 to-transparent">
              <div className="flex items-center gap-3">
                <div className="bg-emerald-600 p-2 rounded-lg">
                  <PieChartIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-emerald-900">Attendance Overview</CardTitle>
                  <p className="text-sm text-emerald-600">Daily attendance distribution</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div style={{ width: "100%", height: 350 }}>
                <ResponsiveContainer>
                  <PieChart>
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'white', 
                        border: '1px solid #E5E7EB', 
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }} 
                    />
                    <Pie
                      data={attendanceSummary}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={100}
                      fill="#10B981"
                      dataKey="value"
                      label={({ name, value }) => `${name} ${value}%`}
                    >
                      {attendanceSummary.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={["#10B981", "#F59E0B", "#EF4444"][index % 3]}
                        />
                      ))}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Enhanced Activity and Stats Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
              <div className="flex items-center gap-3">
                <div className="bg-slate-700 p-2 rounded-lg">
                  <ActivityIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-slate-900">Latest Activities</CardTitle>
                  <p className="text-sm text-slate-600">Recent actions across all schools</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              {loading ? (
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="flex items-center space-x-4 animate-pulse">
                      <div className="bg-slate-200 rounded-full h-10 w-10"></div>
                      <div className="flex-1 space-y-2">
                        <div className="bg-slate-200 h-4 rounded w-3/4"></div>
                        <div className="bg-slate-200 h-3 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : activities.length > 0 ? (
                <div className="space-y-4">
                  {activities.slice(0, 6).map((activity, index) => (
                    <div key={index} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-slate-50 transition-colors">
                      <div className="bg-blue-100 p-2 rounded-full">
                        <Globe className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-slate-900">{activity.description}</p>
                        <p className="text-sm text-slate-500">{activity.timestamp}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ActivityIcon className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                  <p className="text-slate-500">No recent activities</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="bg-gradient-to-br from-white to-indigo-50/30 border-indigo-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="border-b border-indigo-100/50 bg-gradient-to-r from-indigo-50 to-transparent">
              <div className="flex items-center gap-3">
                <div className="bg-indigo-600 p-2 rounded-lg">
                  <Award className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-indigo-900">Performance Metrics</CardTitle>
                  <p className="text-sm text-indigo-600">Key performance indicators</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="bg-gradient-to-r from-emerald-50 to-emerald-100/50 p-4 rounded-xl border border-emerald-200/50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="bg-emerald-600 p-2 rounded-lg">
                        <School className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-emerald-900">Active Schools</p>
                        <p className="text-sm text-emerald-600">Currently operational</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-emerald-900">{stats.activeSchools}</p>
                      <div className="flex items-center text-emerald-600">
                        <ArrowUpRight className="h-4 w-4 mr-1" />
                        <span className="text-sm font-medium">100%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-red-50 to-red-100/50 p-4 rounded-xl border border-red-200/50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="bg-red-600 p-2 rounded-lg">
                        <School className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-red-900">Inactive Schools</p>
                        <p className="text-sm text-red-600">Needs attention</p>
                      </div>
                    </div>
                    <p className="text-2xl font-bold text-red-900">{stats.inactiveSchools}</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-blue-100/50 p-4 rounded-xl border border-blue-200/50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="bg-blue-600 p-2 rounded-lg">
                        <Users className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-blue-900">Avg. Students/School</p>
                        <p className="text-sm text-blue-600">Per institution</p>
                      </div>
                    </div>
                    <p className="text-2xl font-bold text-blue-900">
                      {stats.totalSchools > 0 ? Math.round(stats.totalStudents / stats.totalSchools).toLocaleString() : 0}
                    </p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-amber-50 to-amber-100/50 p-4 rounded-xl border border-amber-200/50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="bg-amber-600 p-2 rounded-lg">
                        <GraduationCap className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-amber-900">Student/Teacher Ratio</p>
                        <p className="text-sm text-amber-600">Network average</p>
                      </div>
                    </div>
                    <p className="text-2xl font-bold text-amber-900">
                      {stats.totalTeachers > 0 ? (stats.totalStudents / stats.totalTeachers).toFixed(1) : 0}:1
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 pt-4 border-t border-indigo-100">
                <Button className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Detailed Reports
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <AddSchoolModal
        isOpen={isAddSchoolModalOpen}
        onClose={() => setIsAddSchoolModalOpen(false)}
        onSubmit={handleAddSchool}
      />
    </Layout>
  );
}
