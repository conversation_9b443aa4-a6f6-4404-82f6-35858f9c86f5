import React, { useState, useEffect, useCallback } from "react";
import { Layout } from "@/components/common/Layout";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  MoreHorizontal,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Download,
  Filter,
  Loader2,
  UserPlus,
  X,
  Check,
  ArrowUpDown,
  Users,
  GraduationCap,
  BookOpen,
  TrendingUp,
  Mail,
  Calendar,
  Award,
  User,
} from "lucide-react";
import { format } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { headmasterAPI } from "@/lib/api";
import { showToast } from "@/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { AddStudentModal, AddStudentFormData } from "@/components/modals/AddStudentModal";
import { ViewStudentModal } from "@/components/modals/ViewStudentModal";
import { EditStudentModal } from "@/components/modals/EditStudentModal";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import type { Student, StudentFormData } from "@/types";

export function StudentsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [classes, setClasses] = useState<any[]>([]);
  const [selectedClass, setSelectedClass] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

  const fetchClasses = useCallback(async () => {
    try {
      const response = await headmasterAPI.getClasses();

      if (response.success) {
        setClasses(response.data.classes);
      }
    } catch (error) {
      console.error("Error fetching classes:", error);
    }
  }, []);

  const fetchStudents = useCallback(async () => {
    try {
      setLoading(true);
      const params: any = {
        page: pagination.page,
        limit: pagination.limit,
        search: searchQuery,
      };

      if (selectedClass) {
        params.class = selectedClass;
      }
      if (statusFilter !== "all") {
        params.status = statusFilter;
      }

      console.log("Fetching students with params:", params);
      const response = await headmasterAPI.getStudents(params);
      console.log("Students API response:", response);

      if (response.success) {
        // Backend returns data directly in response.data, not response.data.data
        const studentsData = Array.isArray(response.data) ? response.data : [];
        console.log("Students API success - received", studentsData.length, "students");
        console.log("First student data:", studentsData[0]);
        if (studentsData[0]) {
          console.log("First student subjects:", studentsData[0].subjects);
          console.log("First student subjectIds:", studentsData[0].subjectIds);
        }
        setStudents(studentsData);
        setPagination((prev) => ({
          ...prev,
          total: response.total || 0,
        }));
      } else {
        console.error("Students API failed:", response);
      }
    } catch (error) {
      console.error("Error fetching students:", error);
      showToast.error("Error", "Failed to load students");
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit, searchQuery, selectedClass, statusFilter]);

  useEffect(() => {
    fetchClasses();
  }, [fetchClasses]);

  useEffect(() => {
    fetchStudents();
  }, [fetchStudents]);

  const handleSearch = () => {
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
    fetchStudents();
  };

  const handleClassChange = (value: string) => {
    // If "all" is selected, we clear the class filter by setting an empty string
    const classValue = value === "all" ? "" : value;
    setSelectedClass(classValue);
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
  };

  const handlePrevPage = () => {
    if (pagination.page > 1) {
      setPagination((prev) => ({
        ...prev,
        page: prev.page - 1,
      }));
    }
  };

  const handleNextPage = () => {
    if (pagination.page * pagination.limit < pagination.total) {
      setPagination((prev) => ({
        ...prev,
        page: prev.page + 1,
      }));
    }
  };

  const resetFilters = () => {
    setSearchQuery("");
    setSelectedClass("");
    setStatusFilter("all");
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const hasActiveFilters = searchQuery || selectedClass || statusFilter !== "all";

  const handleExport = async () => {
    try {
      const csvHeaders = ["Name", "Email", "Class", "Parent", "Status", "Join Date"];
      const csvRows = [csvHeaders.join(",")];

      students.forEach((student) => {
        const row = [
          `"${student.firstName} ${student.lastName}"`,
          `"${student.email}"`,
          `"${student.class}"`,
          `"${student.parent}"`,
          student.status || "active",
          new Date(student.joinDate).toLocaleDateString(),
        ];
        csvRows.push(row.join(","));
      });

      const csvContent = csvRows.join("\n");
      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `students-${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showToast.success("Export Complete", "Students data exported successfully");
    } catch (error) {
      showToast.error("Export Failed", "Failed to export students data");
    }
  };

  const handleAddSubmit = async (data: AddStudentFormData): Promise<void> => {
    try {
      console.log("Creating student with data:", data);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append("firstName", data.firstName);
      formData.append("lastName", data.lastName);
      formData.append("classId", data.classId);
      formData.append("parentId", data.parentId);
      formData.append("subjects", JSON.stringify(data.subjects));

      // Add image if provided
      if (data.image) {
        formData.append("image", data.image);
      }

      const response = await headmasterAPI.createStudentWithImage(formData);
      console.log("Create student response:", response);
      if (response.success) {
        showToast.success("Success", "Student added successfully");
        fetchStudents();
        setIsAddModalOpen(false);
      } else {
        console.error("Create student failed:", response);
        showToast.error("Error", response.message || "Failed to add student");
        throw new Error(response.message || "Failed to add student");
      }
    } catch (error) {
      console.error("Error adding student:", error);
      showToast.error("Error", "Failed to add student");
      throw error; // Re-throw so modal knows there was an error
    }
  };

  const handleViewStudent = (student: Student) => {
    setSelectedStudent(student);
    setIsViewModalOpen(true);
  };

  const handleEditStudent = (student: Student) => {
    setSelectedStudent(student);
    setIsEditModalOpen(true);
  };

  const handleEditSubmit = async (data: any): Promise<void> => {
    if (!selectedStudent) return;
    try {
      console.log("Updating student with data:", data);

      // Create FormData for file upload if image is provided
      if (data.image) {
        const formData = new FormData();
        formData.append("firstName", data.firstName);
        formData.append("lastName", data.lastName);
        formData.append("classId", data.classId);
        formData.append("parentId", data.parentId);
        formData.append("subjects", JSON.stringify(data.subjects));
        formData.append("status", data.status);
        formData.append("image", data.image);

        const response = await headmasterAPI.updateStudentWithImage(selectedStudent.id, formData);
        if (response.success) {
          showToast.success("Success", "Student updated successfully");
          await fetchStudents();
          setIsEditModalOpen(false);
          setSelectedStudent(null);
        } else {
          showToast.error("Error", response.message || "Failed to update student");
          throw new Error(response.message || "Failed to update student");
        }
      } else {
        // Regular update without image
        const response = await headmasterAPI.updateStudent(selectedStudent.id, data);
        if (response.success) {
          showToast.success("Success", "Student updated successfully");
          await fetchStudents();
          setIsEditModalOpen(false);
          setSelectedStudent(null);
        } else {
          showToast.error("Error", response.message || "Failed to update student");
          throw new Error(response.message || "Failed to update student");
        }
      }
    } catch (error) {
      console.error("Error updating student:", error);
      showToast.error("Error", "Failed to update student");
      throw error; // Re-throw so modal knows there was an error
    }
  };

  const handleDeleteClick = (student: Student) => {
    setSelectedStudent(student);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedStudent) return;
    try {
      const response = await headmasterAPI.deleteStudent(selectedStudent.id);
      if (response.success) {
        showToast.success("Success", "Student deleted successfully");
        fetchStudents();
        setIsDeleteDialogOpen(false);
      }
    } catch (error) {
      console.error("Error deleting student:", error);
      showToast.error("Error", "Failed to delete student");
    }
  };

  return (
    <Layout>
      {/* Enhanced Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-teal-900 via-cyan-800 to-blue-900 rounded-2xl p-6 md:p-8 mb-8 text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-10 left-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-40 h-40 border border-white/10 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>

        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl backdrop-blur-sm">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Student Management</h1>
                <p className="text-teal-200 mt-1">Manage and track all student records</p>
              </div>
            </div>
            <p className="text-teal-100 text-lg max-w-2xl">
              Comprehensive student management with academic tracking and performance monitoring tools.
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <Award className="h-4 w-4 mr-2" />
              <span>Reports</span>
            </Button>
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-white text-teal-900 hover:bg-teal-50 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              <span>Add Student</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Students</p>
                <p className="text-3xl font-bold text-blue-900">{loading ? "..." : students.length.toLocaleString()}</p>
                <p className="text-sm text-blue-600 mt-1">Enrolled students</p>
              </div>
              <div className="bg-blue-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-emerald-50 to-emerald-100/50 border-emerald-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-700">Active Students</p>
                <p className="text-3xl font-bold text-emerald-900">
                  {loading ? "..." : students.filter((s) => s.status === "active").length.toLocaleString()}
                </p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-emerald-600 font-medium">Currently enrolled</span>
                </div>
              </div>
              <div className="bg-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Total Classes</p>
                <p className="text-3xl font-bold text-purple-900">
                  {loading ? "..." : classes.length.toLocaleString()}
                </p>
                <p className="text-sm text-purple-600 mt-1">Available classes</p>
              </div>
              <div className="bg-purple-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50 hover:shadow-lg transition-all duration-300 group rounded-xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700">Avg Performance</p>
                <p className="text-3xl font-bold text-orange-900">85%</p>
                <p className="text-sm text-orange-600 mt-1">Academic average</p>
              </div>
              <div className="bg-orange-600 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Award className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <Card className="bg-gradient-to-br from-white to-slate-50/50 border-slate-200/50 shadow-xl hover:shadow-2xl transition-all duration-300">
        <div className="p-6 border-b border-slate-100/50 bg-gradient-to-r from-slate-50 to-transparent">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-slate-700 p-2 rounded-lg">
                <Search className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-slate-900">Student Directory</h3>
                <p className="text-sm text-slate-600">Search and manage student records</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search students..."
                  className="pl-10 w-64 border-slate-200 focus:border-teal-500 focus:ring-teal-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>
              <Select value={selectedClass} onValueChange={handleClassChange}>
                <SelectTrigger className="w-[180px] border-slate-200 focus:border-teal-500 focus:ring-teal-500">
                  <SelectValue placeholder="Filter by class" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="p-4 border-b border-slate-100/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={`flex items-center gap-1 border-slate-200 hover:bg-slate-50 ${
                    hasActiveFilters ? "bg-teal-50 border-teal-200 text-teal-700" : ""
                  }`}
                >
                  <Filter className="h-4 w-4" />
                  <span>Filter</span>
                  {hasActiveFilters && (
                    <span className="ml-1 text-xs bg-teal-600 text-white rounded-full px-1.5 py-0.5">•</span>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl">
                <div className="px-2 py-1.5 text-sm font-medium">Status</div>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === "all"}
                  onCheckedChange={() => setStatusFilter("all")}
                >
                  All Students
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === "active"}
                  onCheckedChange={() => setStatusFilter("active")}
                >
                  Active Only
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === "inactive"}
                  onCheckedChange={() => setStatusFilter("inactive")}
                >
                  Inactive Only
                </DropdownMenuCheckboxItem>
                {hasActiveFilters && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={resetFilters} className="text-destructive">
                      <X className="h-4 w-4 mr-2" />
                      Reset Filters
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="outline" size="sm" className="flex items-center gap-1" onClick={handleExport}>
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="w-[60px]">Photo</TableHead>
                <TableHead className="w-[200px]">Name</TableHead>
                <TableHead>Class</TableHead>
                <TableHead>Parent</TableHead>
                <TableHead>Join Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {(() => {
                if (loading) {
                  return (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex justify-center items-center">
                          <Loader2 className="h-6 w-6 animate-spin mr-2" />
                          <span>Loading students...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                }

                if (!students || students.length === 0) {
                  return (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No students found matching your search criteria
                      </TableCell>
                    </TableRow>
                  );
                }

                return students.map((student) => (
                  <TableRow key={student.id} className="hover:bg-slate-50/50 transition-colors">
                    <TableCell>
                      <Popover>
                        <PopoverTrigger asChild>
                          <div className="cursor-pointer">
                            {student.imageUrl ? (
                              <img
                                src={student.imageUrl}
                                alt={`${student.firstName} ${student.lastName}`}
                                className="w-10 h-10 rounded-full object-cover border-2 border-gray-200 hover:border-teal-500 transition-colors"
                              />
                            ) : (
                              <div className="w-10 h-10 rounded-full bg-teal-100 border-2 border-gray-200 hover:border-teal-500 transition-colors flex items-center justify-center">
                                <User className="h-5 w-5 text-teal-600" />
                              </div>
                            )}
                          </div>
                        </PopoverTrigger>
                        <PopoverContent className="w-80">
                          <div className="flex flex-col items-center space-y-2">
                            {student.imageUrl ? (
                              <img
                                src={student.imageUrl}
                                alt={`${student.firstName} ${student.lastName}`}
                                className="w-32 h-32 rounded-full object-cover border-4 border-gray-200"
                              />
                            ) : (
                              <div className="w-32 h-32 rounded-full bg-teal-100 border-4 border-gray-200 flex items-center justify-center">
                                <User className="h-16 w-16 text-teal-600" />
                              </div>
                            )}
                            <div className="text-center">
                              <h3 className="font-semibold text-lg">{`${student.firstName} ${student.lastName}`}</h3>
                              <p className="text-sm text-gray-600">{student.class}</p>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div>
                          <div className="font-semibold text-slate-900">{`${student.firstName} ${student.lastName}`}</div>
                          <Badge
                            variant={student.status === "active" ? "default" : "secondary"}
                            className={
                              student.status === "active"
                                ? "bg-green-100 text-green-800 border-green-200 hover:bg-green-200 mt-1"
                                : "bg-gray-100 text-gray-800 border-gray-200 mt-1"
                            }
                          >
                            <div
                              className={`w-2 h-2 rounded-full mr-2 ${
                                student.status === "active" ? "bg-green-500" : "bg-gray-400"
                              }`}
                            ></div>
                            {student.status?.charAt(0).toUpperCase() + student.status?.slice(1) || "Active"}
                          </Badge>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <BookOpen className="h-4 w-4 text-purple-600" />
                        <span className="font-medium text-slate-900">{student.class}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-emerald-600" />
                        <span className="font-medium text-slate-900">{student.parent}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2 text-slate-600">
                        <Calendar className="h-4 w-4" />
                        <span>{format(new Date(student.joinDate), "MMM d, yyyy")}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="w-56 bg-white/95 backdrop-blur-sm border-slate-200 shadow-xl"
                        >
                          <DropdownMenuItem
                            className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                            onSelect={() => handleViewStudent(student)}
                          >
                            <div className="bg-blue-100 p-1.5 rounded-lg">
                              <Eye className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <span className="font-medium">View Details</span>
                              <p className="text-xs text-slate-500">See full information</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-3 cursor-pointer hover:bg-slate-50 p-3"
                            onSelect={() => handleEditStudent(student)}
                          >
                            <div className="bg-emerald-100 p-1.5 rounded-lg">
                              <Edit className="h-4 w-4 text-emerald-600" />
                            </div>
                            <div>
                              <span className="font-medium">Edit Student</span>
                              <p className="text-xs text-slate-500">Modify student details</p>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-slate-200" />
                          <DropdownMenuItem
                            className="flex items-center gap-3 text-red-600 focus:text-red-600 cursor-pointer hover:bg-red-50 p-3"
                            onSelect={() => handleDeleteClick(student)}
                          >
                            <div className="bg-red-100 p-1.5 rounded-lg">
                              <Trash2 className="h-4 w-4 text-red-600" />
                            </div>
                            <div>
                              <span className="font-medium">Delete Student</span>
                              <p className="text-xs text-red-500">Remove permanently</p>
                            </div>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ));
              })()}
            </TableBody>
          </Table>
        </div>

        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{students.length}</span> of{" "}
            <span className="font-medium">{pagination.total}</span> students
            {hasActiveFilters && (
              <span className="ml-2">
                • Filtered
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetFilters}
                  className="ml-1 h-auto p-1 text-xs text-destructive hover:text-destructive"
                >
                  Clear
                </Button>
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handlePrevPage} disabled={pagination.page <= 1}>
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={pagination.page * pagination.limit >= pagination.total}
            >
              Next
            </Button>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <AddStudentModal open={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} onSubmit={handleAddSubmit} />

      {selectedStudent && (
        <>
          <ViewStudentModal
            open={isViewModalOpen}
            onClose={() => setIsViewModalOpen(false)}
            student={selectedStudent}
          />
          <EditStudentModal
            open={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            student={selectedStudent}
            onSubmit={handleEditSubmit}
          />
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this student? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteDialogOpen(false)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm} className="bg-red-500 text-white hover:bg-red-600">
              Delete Student
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}
