import React, { useState, useEffect } from 'react';
import { 
  <PERSON>alog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, UserPlus, UserMinus, Users } from 'lucide-react';
import { headmasterAPI } from '@/lib/api';
import { showToast } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';

interface ManageStudentsModalProps {
  open: boolean;
  onClose: () => void;
  classData: any;
}

export function ManageStudentsModal({ open, onClose, classData }: ManageStudentsModalProps) {
  const [currentStudents, setCurrentStudents] = useState<any[]>([]);
  const [availableStudents, setAvailableStudents] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open && classData) {
      fetchStudents();
    }
  }, [open, classData]);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      // Get current students in class
      const currentResponse = await headmasterAPI.getStudents({ class: classData.id });
      if (currentResponse.success) {
        setCurrentStudents(currentResponse.data.data);
      }

      // Get all available students not in this class
      const allResponse = await headmasterAPI.getStudents({ limit: 1000 });
      if (allResponse.success) {
        const available = allResponse.data.data.filter(
          (student: any) => student.class !== classData.name
        );
        setAvailableStudents(available);
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      showToast.error('Error', 'Failed to load students');
    } finally {
      setLoading(false);
    }
  };

  const handleStudentToggle = (studentId: string) => {
    setSelectedStudents(prev => 
      prev.includes(studentId) 
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    );
  };

  const handleAddStudents = async () => {
    if (selectedStudents.length === 0) return;
    
    try {
      setLoading(true);
      const promises = selectedStudents.map(studentId =>
        headmasterAPI.updateStudent(studentId, { class: classData.name })
      );
      
      await Promise.all(promises);
      showToast.success('Success', `${selectedStudents.length} student(s) added to class`);
      setSelectedStudents([]);
      fetchStudents();
    } catch (error) {
      console.error('Error adding students:', error);
      showToast.error('Error', 'Failed to add students to class');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveStudent = async (studentId: string) => {
    try {
      setLoading(true);
      await headmasterAPI.updateStudent(studentId, { class: '' });
      showToast.success('Success', 'Student removed from class');
      fetchStudents();
    } catch (error) {
      console.error('Error removing student:', error);
      showToast.error('Error', 'Failed to remove student from class');
    } finally {
      setLoading(false);
    }
  };

  const filteredAvailableStudents = availableStudents.filter(student =>
    student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (!classData) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Students - {classData.name}</DialogTitle>
          <DialogDescription>
            Add or remove students from this class
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Current Students */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Users className="h-4 w-4" />
              <h3 className="font-medium">Current Students ({currentStudents.length})</h3>
            </div>
            
            {currentStudents.length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead className="text-right">Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentStudents.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell className="font-medium">{student.name}</TableCell>
                        <TableCell>{student.email}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRemoveStudent(student.id)}
                            disabled={loading}
                          >
                            <UserMinus className="h-3 w-3 mr-1" />
                            Remove
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No students in this class</p>
            )}
          </div>

          <Separator />

          {/* Available Students */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">Add Students to Class</h3>
              {selectedStudents.length > 0 && (
                <Button
                  onClick={handleAddStudents}
                  disabled={loading}
                  className="gradient-bg text-white hover:opacity-90"
                >
                  <UserPlus className="h-3 w-3 mr-1" />
                  Add Selected ({selectedStudents.length})
                </Button>
              )}
            </div>
            
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search available students..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {filteredAvailableStudents.length > 0 ? (
              <div className="border rounded-md max-h-64 overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedStudents.length === filteredAvailableStudents.length}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedStudents(filteredAvailableStudents.map(s => s.id));
                            } else {
                              setSelectedStudents([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Current Class</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAvailableStudents.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedStudents.includes(student.id)}
                            onCheckedChange={() => handleStudentToggle(student.id)}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{student.name}</TableCell>
                        <TableCell>{student.email}</TableCell>
                        <TableCell>
                          {student.class ? (
                            <Badge variant="outline">{student.class}</Badge>
                          ) : (
                            <span className="text-sm text-muted-foreground">No class</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No available students found</p>
            )}
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}