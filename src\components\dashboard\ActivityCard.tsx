import React from 'react';
import { format } from 'date-fns';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Activity } from '@/types';
import { 
  PlusCircle, 
  UserPlus, 
  Edit, 
  School, 
  GraduationCap,
  Loader2,
  Trash2
} from 'lucide-react';
import { Button } from '../ui/button';

interface ActivityCardProps {
  title: string;
  description?: string;
  activities: Activity[];
  loading?: boolean;
}

export function ActivityCard({ title, description, activities, loading = false }: ActivityCardProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'school_added':
        return <PlusCircle className="h-4 w-4 text-success" />;
      case 'headmaster_added':
        return <UserPlus className="h-4 w-4 text-primary" />;
      case 'school_updated':
        return <Edit className="h-4 w-4 text-warning" />;
      case 'headmaster_updated':
        return <Edit className="h-4 w-4 text-warning" />;
      case 'school_deleted':
        return <Trash2 className="h-4 w-4 text-error" />;
      case 'headmaster_deleted':
        return <Trash2 className="h-4 w-4 text-error" />;
      default:
        return <School className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getActivityText = (activity: Activity) => {
    switch (activity.type) {
      case 'school_added':
        return `New school "${activity.name}" was added`;
      case 'headmaster_added':
        return `New headmaster "${activity.name}" was assigned`;
      case 'school_updated':
        return `School "${activity.name}" was updated`;
      case 'headmaster_updated':
        return `Headmaster "${activity.name}" information was updated`;
      case 'school_deleted':
        return `School "${activity.name}" was deleted`;
      case 'headmaster_deleted':
        return `Headmaster "${activity.name}" was removed`;
      default:
        return `Activity related to "${activity.name}"`;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'school_added':
      case 'headmaster_added':
        return 'bg-success/10 border-success/20';
      case 'school_updated':
      case 'headmaster_updated':
        return 'bg-warning/10 border-warning/20';
      case 'school_deleted':
      case 'headmaster_deleted':
        return 'bg-error/10 border-error/20';
      default:
        return 'bg-muted/30 border-muted/30';
    }
  };

  return (
    <Card className="hover-lift blue-card h-full">
      <CardHeader className="flex flex-row items-center justify-between pb-2 border-b border-primary/10">
        <div>
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </div>
        <Button variant="outline" size="sm" className="text-xs text-primary border-primary/20 hover:bg-primary/5">View All</Button>
      </CardHeader>
      <CardContent className="p-0">
        {loading ? (
          <div className="flex items-center justify-center h-[300px]">
            <Loader2 className="h-8 w-8 animate-spin text-primary/50" />
          </div>
        ) : activities.length > 0 ? (
          <div className="divide-y divide-border">
            {activities.map((activity) => (
              <div 
                key={activity.id} 
                className={`flex items-start gap-4 p-4 transition-colors hover:bg-muted/20 ${getActivityColor(activity.type)}`}
              >
                <div className="p-2 rounded-full bg-muted/30">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium leading-none">{getActivityText(activity)}</p>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <span>{format(new Date(activity.date), 'MMM d, yyyy • h:mm a')}</span>
                    <span className="mx-1">•</span>
                    <span className="font-medium">By {activity.by}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center justify-center h-[200px] text-muted-foreground">
            No recent activities
          </div>
        )}
      </CardContent>
    </Card>
  );
}